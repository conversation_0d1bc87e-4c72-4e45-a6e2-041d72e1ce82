{"archive": {}, "artifacts": [{"path": "lib/libomop_cdm.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "include_directories", "target_include_directories", "target_sources"], "files": ["src/lib/cdm/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 4, "parent": 0}, {"command": 1, "file": 0, "line": 37, "parent": 0}, {"command": 2, "file": 0, "line": 31, "parent": 0}, {"file": 1}, {"command": 3, "file": 1, "line": 477, "parent": 4}, {"command": 4, "file": 0, "line": 22, "parent": 0}, {"command": 5, "file": 0, "line": 19, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden"}], "includes": [{"backtrace": 5, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib"}, {"backtrace": 5, "path": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include"}, {"backtrace": 5, "path": "/opt/homebrew/opt/postgresql@15/include"}, {"backtrace": 5, "path": "/opt/homebrew/opt/postgresql@15/include/postgresql/server"}, {"backtrace": 5, "path": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src"}, {"backtrace": 6, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/cdm"}, {"backtrace": 3, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.."}], "language": "CXX", "languageStandard": {"backtraces": [3], "standard": "20"}, "sourceIndexes": [0, 1]}], "dependencies": [{"backtrace": 3, "id": "fmt::@976f4f0bee90b99ecdb6"}, {"backtrace": 3, "id": "spdlog::@eb35cfce7893ccfeff1e"}, {"backtrace": 3, "id": "omop_common::@a72075f8f48ba5338622"}], "id": "omop_cdm::@3089087819f5751d3e23", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "/Users/<USER>/uclwork/etl/omop-etl/install/x86_64-release"}}, "name": "omop_cdm", "nameOnDisk": "libomop_cdm.a", "paths": {"build": "src/lib/cdm", "source": "src/lib/cdm"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}], "sources": [{"backtrace": 7, "compileGroupIndex": 0, "path": "src/lib/cdm/omop_tables.cpp", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "src/lib/cdm/table_definitions.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}