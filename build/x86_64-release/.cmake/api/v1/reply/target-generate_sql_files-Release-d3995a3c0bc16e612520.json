{"backtrace": 3, "backtraceGraph": {"commands": ["add_custom_target", "include"], "files": ["src/lib/cdm/sql/schema_config.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 460, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 34, "parent": 2}]}, "id": "generate_sql_files::@6890427a1f51a3e7e1df", "name": "generate_sql_files", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 3, "isGenerated": true, "path": "build/x86_64-release/CMakeFiles/generate_sql_files", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/x86_64-release/CMakeFiles/generate_sql_files.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/x86_64-release/create_tables.sql.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}