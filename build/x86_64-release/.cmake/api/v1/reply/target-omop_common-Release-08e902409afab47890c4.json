{"archive": {}, "artifacts": [{"path": "lib/libomop_common.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "include_directories", "target_include_directories", "target_compile_features"], "files": ["src/lib/common/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 23, "parent": 0}, {"command": 1, "file": 0, "line": 53, "parent": 0}, {"command": 2, "file": 0, "line": 36, "parent": 0}, {"file": 1}, {"command": 3, "file": 1, "line": 477, "parent": 4}, {"command": 3, "file": 0, "line": 18, "parent": 0}, {"command": 4, "file": 0, "line": 26, "parent": 0}, {"command": 5, "file": 0, "line": 50, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden"}], "defines": [{"backtrace": 3, "define": "FMT_SHARED"}, {"backtrace": 3, "define": "SPDLOG_COMPILED_LIB"}, {"backtrace": 3, "define": "SPDLOG_SHARED_LIB"}], "includes": [{"backtrace": 5, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib"}, {"backtrace": 5, "path": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include"}, {"backtrace": 5, "path": "/opt/homebrew/opt/postgresql@15/include"}, {"backtrace": 5, "path": "/opt/homebrew/opt/postgresql@15/include/postgresql/server"}, {"backtrace": 5, "path": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src"}, {"backtrace": 6, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/common"}, {"backtrace": 6, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/core"}, {"backtrace": 7, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.."}, {"backtrace": 3, "path": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlo<PERSON>_json-src/include"}, {"backtrace": 3, "path": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include"}, {"backtrace": 3, "path": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include"}, {"backtrace": 3, "isSystem": true, "path": "/opt/homebrew/include"}], "language": "CXX", "languageStandard": {"backtraces": [8, 3], "standard": "20"}, "sourceIndexes": [0, 1, 2, 3, 4]}], "dependencies": [{"backtrace": 3, "id": "fmt::@976f4f0bee90b99ecdb6"}, {"backtrace": 3, "id": "spdlog::@eb35cfce7893ccfeff1e"}], "id": "omop_common::@a72075f8f48ba5338622", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "/Users/<USER>/uclwork/etl/omop-etl/install/x86_64-release"}}, "name": "omop_common", "nameOnDisk": "libomop_common.a", "paths": {"build": "src/lib/common", "source": "src/lib/common"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/common/configuration.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/common/exceptions.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/common/logging.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/common/utilities.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/common/validation.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}