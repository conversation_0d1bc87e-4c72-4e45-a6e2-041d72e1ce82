# Changelog
All notable changes to this project will be documented in this file. This project adheres to [Semantic Versioning](http://semver.org/).

## [3.11.2](https://github.com/nlohmann/json/releases/tag/3.11.2) (2022-08-12)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.11.1...3.11.2)

- MSVC natvis visualizer does not work after introduction of inline ABI namespace [\#3696](https://github.com/nlohmann/json/issues/3696)
- The use of parenthesis gives compilation errors in some situations [\#3682](https://github.com/nlohmann/json/issues/3682)
- extern from/to\_json result in linker error [\#3657](https://github.com/nlohmann/json/issues/3657)
- json\_fwd.hpp no longer standalone [\#3656](https://github.com/nlohmann/json/issues/3656)
- regression: `.value<size_t>` is compilation error. [\#3655](https://github.com/nlohmann/json/issues/3655)
- Regression: no match for 'operator!=' comparing json\_pointer and const char \*/string\_t [\#3654](https://github.com/nlohmann/json/issues/3654)
- Regression: call to member function 'value' is ambiguous [\#3652](https://github.com/nlohmann/json/issues/3652)
- macOS 10.15 Actions runner image deprecation [\#3612](https://github.com/nlohmann/json/issues/3612)

- generate\_natvis.py: validate version number; cleanup [\#3698](https://github.com/nlohmann/json/pull/3698) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Add Python script for generating Natvis file and update file for 3.11.2 [\#3697](https://github.com/nlohmann/json/pull/3697) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- fix typo in json\_pointer.md [\#3692](https://github.com/nlohmann/json/pull/3692) ([eltociear](https://github.com/eltociear))
- Add amalgamated json-fwd.hpp to release [\#3687](https://github.com/nlohmann/json/pull/3687) ([nlohmann](https://github.com/nlohmann))
- Documentation updates for 3.11.2 [\#3686](https://github.com/nlohmann/json/pull/3686) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Make json\_pointer usable as map key \(again\) [\#3685](https://github.com/nlohmann/json/pull/3685) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Deprecate json\_pointer/string\_t comparisons [\#3684](https://github.com/nlohmann/json/pull/3684) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Restructure inline namespace and allow version component to be disabled [\#3683](https://github.com/nlohmann/json/pull/3683) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Properly constrain non-string json\_pointer overloads [\#3681](https://github.com/nlohmann/json/pull/3681) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Amalgamate the forward declaration header [\#3679](https://github.com/nlohmann/json/pull/3679) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Fix 'const' qualifier on bool& has no effect [\#3678](https://github.com/nlohmann/json/pull/3678) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Fix whitespace in workflow files [\#3675](https://github.com/nlohmann/json/pull/3675) ([nlohmann](https://github.com/nlohmann))
- Attempt to fix labeler permissions [\#3674](https://github.com/nlohmann/json/pull/3674) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Refine 'Publish documentation' workflow [\#3673](https://github.com/nlohmann/json/pull/3673) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Documentation change [\#3672](https://github.com/nlohmann/json/pull/3672) ([nlohmann](https://github.com/nlohmann))
- Add labeler action [\#3671](https://github.com/nlohmann/json/pull/3671) ([nlohmann](https://github.com/nlohmann))
- Complete contributor list [\#3670](https://github.com/nlohmann/json/pull/3670) ([nlohmann](https://github.com/nlohmann))
- Add json\_pointer/string\_t equality comparison operators [\#3664](https://github.com/nlohmann/json/pull/3664) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Reimplement value\(\) access functions [\#3663](https://github.com/nlohmann/json/pull/3663) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Complete contributor list [\#3662](https://github.com/nlohmann/json/pull/3662) ([nlohmann](https://github.com/nlohmann))
- Adjust naming of GitHub action jobs [\#3661](https://github.com/nlohmann/json/pull/3661) ([nlohmann](https://github.com/nlohmann))
- Publish documentation on push to develop branch [\#3660](https://github.com/nlohmann/json/pull/3660) ([nlohmann](https://github.com/nlohmann))
- Add Discord badge to README [\#3651](https://github.com/nlohmann/json/pull/3651) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Miscellaneous small fixes [\#3643](https://github.com/nlohmann/json/pull/3643) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Minor BJData fixes [\#3637](https://github.com/nlohmann/json/pull/3637) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Update CI [\#3626](https://github.com/nlohmann/json/pull/3626) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))

## [v3.11.1](https://github.com/nlohmann/json/releases/tag/v3.11.1) (2022-08-01)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.11.0...v3.11.1)

- Regression: no matching literal operator for call to 'operator""\_json' [\#3645](https://github.com/nlohmann/json/issues/3645)
- \_json  operator""\(\)  [\#3644](https://github.com/nlohmann/json/issues/3644)

- Fix global UDLs [\#3646](https://github.com/nlohmann/json/pull/3646) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))

## [v3.11.0](https://github.com/nlohmann/json/releases/tag/v3.11.0) (2022-08-01)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.10.5...v3.11.0)

- ICPC: warning \#1098: the qualifier on this friend declaration is ignored [\#3632](https://github.com/nlohmann/json/issues/3632)
- Starting with 3.10.4, just adding `\#include json.hpp` causes compile error: `overload resolution selected deleted operator '=' [\#3620](https://github.com/nlohmann/json/issues/3620)
- xwidgets doesn't compile with version \>3.10.3 [\#3602](https://github.com/nlohmann/json/issues/3602)
- json\_pointer\_\_pop\_back.cpp example does not compile [\#3600](https://github.com/nlohmann/json/issues/3600)
- nlohmann::json::array 'push\_back' is ambiguous [\#3589](https://github.com/nlohmann/json/issues/3589)
- Multiple versions causing conflict [\#3588](https://github.com/nlohmann/json/issues/3588)
- ERROR: ThreadSanitizer: SEGV on unknown address [\#3584](https://github.com/nlohmann/json/issues/3584)
- unicode4 test consistently fails on RISC-V hardware [\#3579](https://github.com/nlohmann/json/issues/3579)
- sax\_parse\(iterator, json\_sax\_t \*\) string callback clobbers spaces [\#3574](https://github.com/nlohmann/json/issues/3574)
- Nlohmann JSON Parse crash with raylib-cpp [\#3570](https://github.com/nlohmann/json/issues/3570)
- ordered\_json doesn't accept keys of types other than string\_t \(e.g., string\_view\) [\#3558](https://github.com/nlohmann/json/issues/3558)
- turning an object into an array [\#3547](https://github.com/nlohmann/json/issues/3547)
- json:parse\_bjdata\_fuzzer: ASSERT: ref\_stack.back\(\)-\>is\_array\(\) [\#3541](https://github.com/nlohmann/json/issues/3541)
- Warning about potential null dereference in GCC 12.1 \(Fedora 36\) [\#3525](https://github.com/nlohmann/json/issues/3525)
- Enable 32bit unit test in CI [\#3524](https://github.com/nlohmann/json/issues/3524)
- Error when roundtripping BJData [\#3519](https://github.com/nlohmann/json/issues/3519)
- ASSERT error while parsing BJData [\#3513](https://github.com/nlohmann/json/issues/3513)
- An exception occurred when sending a string with double quotes [\#3504](https://github.com/nlohmann/json/issues/3504)
- Binary reader for BJData creates incorrect SAX events [\#3503](https://github.com/nlohmann/json/issues/3503)
- It can't support  "nan", "inf", "-inf" for float type [\#3494](https://github.com/nlohmann/json/issues/3494)
- ASAN error while parsing BJData \(Heap-buffer-overflow READ 1\) [\#3492](https://github.com/nlohmann/json/issues/3492)
- UBSAN error while parsing BJData \(Null-dereference\) [\#3491](https://github.com/nlohmann/json/issues/3491)
- UBSAN error while parsing BJData \(Invalid-bool-value\) [\#3490](https://github.com/nlohmann/json/issues/3490)
- json:parse\_bjdata\_fuzzer reaches assertion [\#3475](https://github.com/nlohmann/json/issues/3475)
- Compilation with -fmodules-ts and use inside of a module [\#3472](https://github.com/nlohmann/json/issues/3472)
- json.exception.parse\_error.101 only occurs outside of IDE  [\#3467](https://github.com/nlohmann/json/issues/3467)
- json:parse\_bjdata\_fuzzer reaches assertion [\#3461](https://github.com/nlohmann/json/issues/3461)
- NLOHMANN\_DEFINE\_TYPE\_NON\_INTRUSIVE\_WITH\_DEFAULT can not parse { "key" : null} [\#3458](https://github.com/nlohmann/json/issues/3458)
- Unable to compile when using Microsoft's \_CRTDBG [\#3457](https://github.com/nlohmann/json/issues/3457)
- Compilation errors when including `<filesystem>` and using `--std=c++17` or above \(MinGW/Win10\) [\#3449](https://github.com/nlohmann/json/issues/3449)
- Weird things on for statement [\#3447](https://github.com/nlohmann/json/issues/3447)
- Parsing error when there is a json string within a Json [\#3445](https://github.com/nlohmann/json/issues/3445)
- ordered\_json vs json types comparison [\#3443](https://github.com/nlohmann/json/issues/3443)
- Error occurred when converting nlohmann::json to std::any [\#3428](https://github.com/nlohmann/json/issues/3428)
- I was forced to report an assertion error when copying an array of strings [\#3419](https://github.com/nlohmann/json/issues/3419)
- About Serialization Error invalid UTF-8 byte at index [\#3414](https://github.com/nlohmann/json/issues/3414)
- Comparison of NaN differs between json and float [\#3409](https://github.com/nlohmann/json/issues/3409)
- when i use it in C++ sserver,it it constantly show that fatal error: adl\_serializer.hpp: No such file or directory [\#3404](https://github.com/nlohmann/json/issues/3404)
- parse error [\#3403](https://github.com/nlohmann/json/issues/3403)
- CMake script MAIN\_PROJECT always OFF [\#3390](https://github.com/nlohmann/json/issues/3390)
- Parser unable to handle large floating point numbers [\#3389](https://github.com/nlohmann/json/issues/3389)
- Compilation error if json\_pointer is used with alternative string type [\#3388](https://github.com/nlohmann/json/issues/3388)
- Unit tests conversions & items fail to build \(Clang \<4.0/C++14 only\) [\#3384](https://github.com/nlohmann/json/issues/3384)
- Regression test for \#3070 is not being run and fails when enabled [\#3377](https://github.com/nlohmann/json/issues/3377)
- Refactor unit tests to use more convenient doctest assertion macros [\#3365](https://github.com/nlohmann/json/issues/3365)
- An json.h issue reported in a static code analyzer [\#3361](https://github.com/nlohmann/json/issues/3361)
- Mixing different JSON\_DIAGNOSTICS settings in separately compiled units leads to core [\#3360](https://github.com/nlohmann/json/issues/3360)
- json::out\_of\_range exception matches against lot of others while testing [\#3352](https://github.com/nlohmann/json/issues/3352)
- use mipsel-openwrt-linux-g++ -std=c++11 to compile, it has some errors "error: 'snprintf' is not a member of 'std'" [\#3349](https://github.com/nlohmann/json/issues/3349)
- Add proper issue templates [\#3348](https://github.com/nlohmann/json/issues/3348)
- switch from json to ordered\_json [\#3343](https://github.com/nlohmann/json/issues/3343)
- Json dump use to compilation errors [\#3339](https://github.com/nlohmann/json/issues/3339)
- Ambiguous conversion from nlohmann::basic\_json\<\> to custom class. [\#3333](https://github.com/nlohmann/json/issues/3333)
- Iterator doesn't satisfy std::incrementable because post-increment may change constness [\#3331](https://github.com/nlohmann/json/issues/3331)
- Inconsistent handling of floating point numbers after parse\(\) [\#3329](https://github.com/nlohmann/json/issues/3329)
- Documentation for `ordered_json` should show proper use of the `parse()` function. [\#3325](https://github.com/nlohmann/json/issues/3325)
- "type must be boolean, but is object" error thrown on non-boolean object [\#3319](https://github.com/nlohmann/json/issues/3319)
- Incomplete Type in request parms [\#3318](https://github.com/nlohmann/json/issues/3318)
- 小米 MIX4 MIUI13  bug [\#3316](https://github.com/nlohmann/json/issues/3316)
- json.exception.parse\_error.101 when parsing data received over a socket [\#3313](https://github.com/nlohmann/json/issues/3313)
- Parse to custom class from unordered\_json breaks on G++11.2.0 with C++20 [\#3312](https://github.com/nlohmann/json/issues/3312)
- try to assign dumped string to a class member varible [\#3300](https://github.com/nlohmann/json/issues/3300)
- includedir in pkgconfig is error if install\_headers\(\) has subdir argument.  [\#3284](https://github.com/nlohmann/json/issues/3284)
- SHA-256 sum of json-3.10.5.tar.xz changes over time \(but not the content itself\) [\#3281](https://github.com/nlohmann/json/issues/3281)
- items\(\) method does not follow order of json message [\#3278](https://github.com/nlohmann/json/issues/3278)
- Perplexing template deduction failure serialising a 3rd party type using base class [\#3267](https://github.com/nlohmann/json/issues/3267)
- json.hpp 'isfinite' is not a member of 'std' also isinf; snprintf; stoull and to\_string members of std [\#3263](https://github.com/nlohmann/json/issues/3263)
- JSON build fails for C++ cmake [\#3256](https://github.com/nlohmann/json/issues/3256)
- Unexpected implicit conversion [\#3254](https://github.com/nlohmann/json/issues/3254)
- Add a function that checks for valid json in a C++ string  [\#3245](https://github.com/nlohmann/json/issues/3245)
- Replace use of standard IO from error handling [\#3239](https://github.com/nlohmann/json/issues/3239)
- Use Catch for unit tests [\#3232](https://github.com/nlohmann/json/issues/3232)
- Exception thrown during initialization causes a memory leak [\#3215](https://github.com/nlohmann/json/issues/3215)
- Tests failing when compiling with c++20 [\#3207](https://github.com/nlohmann/json/issues/3207)
- ambiguous regression [\#3204](https://github.com/nlohmann/json/issues/3204)
- Deserialization: if class is\_constructible from std::string wrong from\_json overload is being selected, compilation failed [\#3171](https://github.com/nlohmann/json/issues/3171)
- 'clang++ ./json.hpp' with no usage: Compiler syntax problem in clang 3.7.0 \(tizen :/ \) [\#3153](https://github.com/nlohmann/json/issues/3153)
- build failure on upcoming gcc-12: test/src/unit-regression1.cpp:392:22: error: ambiguous overload for 'operator=' [\#3138](https://github.com/nlohmann/json/issues/3138)
- Applying JSON patch creates parent object [\#3134](https://github.com/nlohmann/json/issues/3134)
- Iterators cannot be used with range-v3 [\#3130](https://github.com/nlohmann/json/issues/3130)
- std::shared\_ptr\<T\> == nlohmann::json compiles, which seem undesirable [\#3026](https://github.com/nlohmann/json/issues/3026)
- Error in test\download\_test\_data.vcxproj custom build step when compiling with Visual Studio 2019 16.7.7 msbuild on Windows 10 [\#2593](https://github.com/nlohmann/json/issues/2593)
- Consider putting the user-defined literals in a namespace [\#1682](https://github.com/nlohmann/json/issues/1682)
- Using versioned namespaces [\#1539](https://github.com/nlohmann/json/issues/1539)
- How can I use std::string\_view as the json\_key to "operator \[\]" ? [\#1529](https://github.com/nlohmann/json/issues/1529)
- serialize std::variant\<...\> [\#1261](https://github.com/nlohmann/json/issues/1261)

- Prepare 3.11.0 release [\#3635](https://github.com/nlohmann/json/pull/3635) ([nlohmann](https://github.com/nlohmann))
- Fix warning [\#3634](https://github.com/nlohmann/json/pull/3634) ([nlohmann](https://github.com/nlohmann))
- Add license header to new files [\#3633](https://github.com/nlohmann/json/pull/3633) ([nlohmann](https://github.com/nlohmann))
- Add a unit test including windows.h [\#3631](https://github.com/nlohmann/json/pull/3631) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Fixed latest build error in msvc platform [\#3630](https://github.com/nlohmann/json/pull/3630) ([KsaNL](https://github.com/KsaNL))
- Add regression tests for \#3204 and \#3333 [\#3629](https://github.com/nlohmann/json/pull/3629) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Fix patch::add creating nonexistent parents [\#3628](https://github.com/nlohmann/json/pull/3628) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Adjust JSON Pointer examples [\#3622](https://github.com/nlohmann/json/pull/3622) ([nlohmann](https://github.com/nlohmann))
- Disable exceptions on ICPC [\#3621](https://github.com/nlohmann/json/pull/3621) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- build: install .pc and .cmake files to share/ [\#3619](https://github.com/nlohmann/json/pull/3619) ([Tachi107](https://github.com/Tachi107))
- Fix MinGW CI failures [\#3618](https://github.com/nlohmann/json/pull/3618) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Fix Unicode test timeout \(for real this time!\) [\#3614](https://github.com/nlohmann/json/pull/3614) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Use 'concurrency' in GitHub workflows [\#3610](https://github.com/nlohmann/json/pull/3610) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Use swap\(\) by ADL [\#3609](https://github.com/nlohmann/json/pull/3609) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Move UDLs out of the global namespace [\#3605](https://github.com/nlohmann/json/pull/3605) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Re-add value\_type detection to distinguish string types [\#3604](https://github.com/nlohmann/json/pull/3604) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Add operator\<\<\(json\_pointer\) [\#3601](https://github.com/nlohmann/json/pull/3601) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Add documentation for comparing json and ordered\_json [\#3599](https://github.com/nlohmann/json/pull/3599) ([nlohmann](https://github.com/nlohmann))
- Clean up after \#3581 [\#3596](https://github.com/nlohmann/json/pull/3596) ([nlohmann](https://github.com/nlohmann))
- Add assertion if nullptr is passed to parse function [\#3593](https://github.com/nlohmann/json/pull/3593) ([nlohmann](https://github.com/nlohmann))
- Minor documentation fixes [\#3592](https://github.com/nlohmann/json/pull/3592) ([nlohmann](https://github.com/nlohmann))
- Add versioned, ABI-tagged inline namespace and namespace macros [\#3590](https://github.com/nlohmann/json/pull/3590) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Add badge for https://repology.org/project/nlohmann-json/versions [\#3586](https://github.com/nlohmann/json/pull/3586) ([nlohmann](https://github.com/nlohmann))
- Add error message if test suite cannot be found [\#3585](https://github.com/nlohmann/json/pull/3585) ([nlohmann](https://github.com/nlohmann))
- add patch\_inplace function [\#3581](https://github.com/nlohmann/json/pull/3581) ([wolfv](https://github.com/wolfv))
- Enable overriding test properties and set Unicode test timeouts [\#3580](https://github.com/nlohmann/json/pull/3580) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Ignore output directory [\#3572](https://github.com/nlohmann/json/pull/3572) ([NN---](https://github.com/NN---))
- Optimize output vector adapter write [\#3569](https://github.com/nlohmann/json/pull/3569) ([romainreignier](https://github.com/romainreignier))
- Add overloads for more key types to ordered\_map and fix ordered\_map::erase\(first, last\) with first == last [\#3564](https://github.com/nlohmann/json/pull/3564) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Make certain usage patterns more prominent in the README [\#3557](https://github.com/nlohmann/json/pull/3557) ([jez](https://github.com/jez))
- CI: fix "JSON\_MultipleHeaders" option spelling [\#3555](https://github.com/nlohmann/json/pull/3555) ([karzhenkov](https://github.com/karzhenkov))
- More documentation updates for 3.11.0 [\#3553](https://github.com/nlohmann/json/pull/3553) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Use DOCTEST\_\* compiler macros and suppress pragmas warning [\#3550](https://github.com/nlohmann/json/pull/3550) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Add unit test to make sure iterator\_input\_adapter advances iterators correctly [\#3548](https://github.com/nlohmann/json/pull/3548) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Use REUSE framework [\#3546](https://github.com/nlohmann/json/pull/3546) ([nlohmann](https://github.com/nlohmann))
- Use `std::iterator_traits` to extract `iterator_category` [\#3544](https://github.com/nlohmann/json/pull/3544) ([Mike-Leo-Smith](https://github.com/Mike-Leo-Smith))
- BJData dimension length can not be string\_t::npos, fix \#3541 [\#3543](https://github.com/nlohmann/json/pull/3543) ([fangq](https://github.com/fangq))
- Allow disabling default enum conversions [\#3536](https://github.com/nlohmann/json/pull/3536) ([zxey](https://github.com/zxey))
- Add to\_json\(\) for std::vector\<bool\>::reference [\#3534](https://github.com/nlohmann/json/pull/3534) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- CI: Enable 32bit unit test \(3\) [\#3532](https://github.com/nlohmann/json/pull/3532) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Use new CI image [\#3528](https://github.com/nlohmann/json/pull/3528) ([nlohmann](https://github.com/nlohmann))
- Fix ndarray dimension signedness, fix ndarray length overflow \(2\); add 32bit unit test [\#3523](https://github.com/nlohmann/json/pull/3523) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Small documentation fixes [\#3520](https://github.com/nlohmann/json/pull/3520) ([nlohmann](https://github.com/nlohmann))
- Add assertion to converting constructor [\#3517](https://github.com/nlohmann/json/pull/3517) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- CI: Remove -Wstrict-overflow [\#3516](https://github.com/nlohmann/json/pull/3516) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Fix nlohmann/json\#3513, explain is\_ndarray flag [\#3514](https://github.com/nlohmann/json/pull/3514) ([fangq](https://github.com/fangq))
- Prevent ndarray size vector from recursive use, fix nlohmann/json\#3503 [\#3505](https://github.com/nlohmann/json/pull/3505) ([fangq](https://github.com/fangq))
- prevent ndarray dimension vector from recusive array, nlohmann/json\#3500 [\#3502](https://github.com/nlohmann/json/pull/3502) ([fangq](https://github.com/fangq))
- Discard optimized containers with negative counts in UBJSON/BJData \(\#3491,\#3492,\#3490\) [\#3500](https://github.com/nlohmann/json/pull/3500) ([fangq](https://github.com/fangq))
- Update json.hpp [\#3499](https://github.com/nlohmann/json/pull/3499) ([ivanovmp](https://github.com/ivanovmp))
- Add assertion for invariant in SAX-DOM parser [\#3498](https://github.com/nlohmann/json/pull/3498) ([nlohmann](https://github.com/nlohmann))
- Add more macOS builders [\#3485](https://github.com/nlohmann/json/pull/3485) ([nlohmann](https://github.com/nlohmann))
- change bjdata ndarray flag to detect negative size, as part of \#3475 [\#3479](https://github.com/nlohmann/json/pull/3479) ([fangq](https://github.com/fangq))
- Document fuzzer usage [\#3478](https://github.com/nlohmann/json/pull/3478) ([nlohmann](https://github.com/nlohmann))
- Add build step for ICPC \(with fixes\) [\#3465](https://github.com/nlohmann/json/pull/3465) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Complete documentation for 3.11.0 [\#3464](https://github.com/nlohmann/json/pull/3464) ([nlohmann](https://github.com/nlohmann))
- Handle invalid BJData optimized type, fix \#3461 [\#3463](https://github.com/nlohmann/json/pull/3463) ([fangq](https://github.com/fangq))
- Reorganize directories [\#3462](https://github.com/nlohmann/json/pull/3462) ([nlohmann](https://github.com/nlohmann))
- Enable rapid testing and development on Compiler Explorer [\#3456](https://github.com/nlohmann/json/pull/3456) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- cpplint 1.6.0 [\#3454](https://github.com/nlohmann/json/pull/3454) ([nlohmann](https://github.com/nlohmann))
- Disable regression test for \#3070 on GCC \<8.4 [\#3451](https://github.com/nlohmann/json/pull/3451) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Fix C++20/gcc-12 issues \(Part 2\) [\#3446](https://github.com/nlohmann/json/pull/3446) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Overwork documentation [\#3444](https://github.com/nlohmann/json/pull/3444) ([nlohmann](https://github.com/nlohmann))
- Fix typo in basic\_json documentation [\#3439](https://github.com/nlohmann/json/pull/3439) ([jhnlee](https://github.com/jhnlee))
- Exclude std::any from implicit conversion \(fixes \#3428\) [\#3437](https://github.com/nlohmann/json/pull/3437) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Document which version introduced the macros [\#3431](https://github.com/nlohmann/json/pull/3431) ([nlohmann](https://github.com/nlohmann))
- Fix constraints on from\_json\(\) for strings \(fixes \#3171, \#3267, \#3312, \#3384\) [\#3427](https://github.com/nlohmann/json/pull/3427) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- at.md: fix typo [\#3426](https://github.com/nlohmann/json/pull/3426) ([heinemml](https://github.com/heinemml))
- Implement support for string\_view \(attempt no. 3\) [\#3423](https://github.com/nlohmann/json/pull/3423) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- CI: speedup AppVeyor builds by ~30% [\#3422](https://github.com/nlohmann/json/pull/3422) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Restore disabled check for \#3070 \(except on MSVC\) [\#3421](https://github.com/nlohmann/json/pull/3421) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Update CI image [\#3420](https://github.com/nlohmann/json/pull/3420) ([nlohmann](https://github.com/nlohmann))
- Add check if different version is also included [\#3418](https://github.com/nlohmann/json/pull/3418) ([nlohmann](https://github.com/nlohmann))
- Report the right \_\_cplusplus value for MSVC in basic\_json meta\(\) [\#3417](https://github.com/nlohmann/json/pull/3417) ([flagarde](https://github.com/flagarde))
- CI: windows-2016 has been deprecated; remove jobs [\#3416](https://github.com/nlohmann/json/pull/3416) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Re-template json\_pointer on string type [\#3415](https://github.com/nlohmann/json/pull/3415) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Refactor unit tests to use more convenient doctest assertion macros \(Part 2\) [\#3405](https://github.com/nlohmann/json/pull/3405) ([kkarbowiak](https://github.com/kkarbowiak))
- Refactor unit tests to use more convenient doctest assertion macros [\#3393](https://github.com/nlohmann/json/pull/3393) ([kkarbowiak](https://github.com/kkarbowiak))
- Improve unit testing \(Part 1\) [\#3380](https://github.com/nlohmann/json/pull/3380) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Fix C++20/gcc-12 issues \(Part 1\) [\#3379](https://github.com/nlohmann/json/pull/3379) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Add clarification to avoid misunderstanding that cause \#3360 [\#3378](https://github.com/nlohmann/json/pull/3378) ([puffetto](https://github.com/puffetto))
- Fix ordered\_map ctor with initializer\_list \(fixes \#3343\) [\#3370](https://github.com/nlohmann/json/pull/3370) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Fix and update CI [\#3368](https://github.com/nlohmann/json/pull/3368) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- FetchContent\_MakeAvailable [\#3351](https://github.com/nlohmann/json/pull/3351) ([nlohmann](https://github.com/nlohmann))
- Avoid clash with Arduino defines [\#3338](https://github.com/nlohmann/json/pull/3338) ([DarkZeros](https://github.com/DarkZeros))
- Support UBJSON-derived Binary JData \(BJData\) format [\#3336](https://github.com/nlohmann/json/pull/3336) ([fangq](https://github.com/fangq))
- Make iterator operator++/--\(int\) equality-preserving [\#3332](https://github.com/nlohmann/json/pull/3332) ([falbrechtskirchinger](https://github.com/falbrechtskirchinger))
- Add note on parsing ordered\_json [\#3326](https://github.com/nlohmann/json/pull/3326) ([nlohmann](https://github.com/nlohmann))
- Fix CITATION.cff and add automatic validation of your citation metadata [\#3320](https://github.com/nlohmann/json/pull/3320) ([fdiblen](https://github.com/fdiblen))
- .github/workflows/windows.yml: Add support for Visual Studio 2022 [\#3295](https://github.com/nlohmann/json/pull/3295) ([t-b](https://github.com/t-b))
- Add maintainer targets to create source archive [\#3289](https://github.com/nlohmann/json/pull/3289) ([nlohmann](https://github.com/nlohmann))
- Fix a typo [\#3265](https://github.com/nlohmann/json/pull/3265) ([fhuberts](https://github.com/fhuberts))
- Fix typo [\#3249](https://github.com/nlohmann/json/pull/3249) ([rex4539](https://github.com/rex4539))
- Add documentation for JSON Lines [\#3247](https://github.com/nlohmann/json/pull/3247) ([nlohmann](https://github.com/nlohmann))
- Improve documentation InputType and IteratorType [\#3246](https://github.com/nlohmann/json/pull/3246) ([nlohmann](https://github.com/nlohmann))
- Remove stringstream [\#3244](https://github.com/nlohmann/json/pull/3244) ([nlohmann](https://github.com/nlohmann))
- fix \_MSC\_VER version to check for std::filesystem [\#3240](https://github.com/nlohmann/json/pull/3240) ([gcerretani](https://github.com/gcerretani))
- Add macros NLOHMANN\_DEFINE\_TYPE\_INTRUSIVE\_WITH\_DEFAULT and ...\_NON\_INTRUSIVE\_WITH\_DEFAULT [\#3143](https://github.com/nlohmann/json/pull/3143) ([pketelsen](https://github.com/pketelsen))

## [v3.10.5](https://github.com/nlohmann/json/releases/tag/v3.10.5) (2022-01-03)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.10.4...v3.10.5)

- \#include \<filesystem\> doesn't work with gcc-7 when `-std=c++17` is specified. [\#3203](https://github.com/nlohmann/json/issues/3203)
- Not able to use nlohmann json with c++ code built using emscripten to wasm [\#3200](https://github.com/nlohmann/json/issues/3200)
- Warning for shadowed variables [\#3188](https://github.com/nlohmann/json/issues/3188)
- Accessing missing keys on const json object leads to assert [\#3183](https://github.com/nlohmann/json/issues/3183)
- Data member is available, but null is reported, and program throws error [\#3173](https://github.com/nlohmann/json/issues/3173)
- serialization problem, from\_json need construct new object [\#3169](https://github.com/nlohmann/json/issues/3169)
- std::filesystem unavailable on macOS lower deployment targets [\#3156](https://github.com/nlohmann/json/issues/3156)
- \[json.exception.type\_error.305\] cannot use operator\[\] with a string argument with string [\#3151](https://github.com/nlohmann/json/issues/3151)
- json::dump\(\) is not compatible with C++ standards [\#3147](https://github.com/nlohmann/json/issues/3147)
- Issue with json::parse decoding codepoints [\#3142](https://github.com/nlohmann/json/issues/3142)
- Simple parse of json object thinks it should be an array [\#3136](https://github.com/nlohmann/json/issues/3136)
- How to properly read a Json string that may be null in some cases? [\#3135](https://github.com/nlohmann/json/issues/3135)
- Deadlock on create json - windows only [\#3129](https://github.com/nlohmann/json/issues/3129)
- Wrong parsing of int64 values nearest of limit [\#3126](https://github.com/nlohmann/json/issues/3126)
- ordered\_json doesn't support range based erase [\#3108](https://github.com/nlohmann/json/issues/3108)
- Apple build failed with json/single\_include/nlohmann/json.hpp:4384:57: 'path' is unavailable [\#3097](https://github.com/nlohmann/json/issues/3097)
- GCC 7.5.0 with --std=c++17: filesystem: No such file or directory [\#3090](https://github.com/nlohmann/json/issues/3090)
- Drop Travis CI [\#3087](https://github.com/nlohmann/json/issues/3087)
- ordered\_json::reset\(\) compile error with nvcc [\#3013](https://github.com/nlohmann/json/issues/3013)
- Support for unordered\_map as object\_t [\#2932](https://github.com/nlohmann/json/issues/2932)
- Compiler warning with Intel compiler, same as \#755 [\#2712](https://github.com/nlohmann/json/issues/2712)
- Compiler warnings with NVCC 11.2 [\#2676](https://github.com/nlohmann/json/issues/2676)
- some static analysis warning at line 11317 [\#1390](https://github.com/nlohmann/json/issues/1390)
- Compiling with icpc [\#755](https://github.com/nlohmann/json/issues/755)

- Fix compilation error with NVCC [\#3234](https://github.com/nlohmann/json/pull/3234) ([nlohmann](https://github.com/nlohmann))
- Remove Travis CI [\#3233](https://github.com/nlohmann/json/pull/3233) ([nlohmann](https://github.com/nlohmann))
- Add build step for NVCC and fix a warning [\#3227](https://github.com/nlohmann/json/pull/3227) ([nlohmann](https://github.com/nlohmann))
- Update cpplint [\#3225](https://github.com/nlohmann/json/pull/3225) ([nlohmann](https://github.com/nlohmann))
- Fix: Warning for shadowed variables \(\#3188\) [\#3193](https://github.com/nlohmann/json/pull/3193) ([kernie](https://github.com/kernie))
- Fix FAQ hyperlink typo in readme [\#3148](https://github.com/nlohmann/json/pull/3148) ([Prince-Mendiratta](https://github.com/Prince-Mendiratta))
- Docs: Update `skip_comments` to `ignore_comments` [\#3145](https://github.com/nlohmann/json/pull/3145) ([daniel-kun](https://github.com/daniel-kun))
- fix typos in documentation [\#3140](https://github.com/nlohmann/json/pull/3140) ([striezel](https://github.com/striezel))
- Fix spelling [\#3125](https://github.com/nlohmann/json/pull/3125) ([axic](https://github.com/axic))
- Extend std specializations [\#3121](https://github.com/nlohmann/json/pull/3121) ([nlohmann](https://github.com/nlohmann))
- Add missing erase\(first, last\) function to ordered\_map [\#3109](https://github.com/nlohmann/json/pull/3109) ([nlohmann](https://github.com/nlohmann))
- Fix typos in operator\[\] documentation [\#3102](https://github.com/nlohmann/json/pull/3102) ([axnsan12](https://github.com/axnsan12))
- Add C++17 copies of the test binaries [\#3101](https://github.com/nlohmann/json/pull/3101) ([nlohmann](https://github.com/nlohmann))
- Add examples for parsing from iterator pair [\#3100](https://github.com/nlohmann/json/pull/3100) ([nlohmann](https://github.com/nlohmann))
- Update CI [\#3088](https://github.com/nlohmann/json/pull/3088) ([nlohmann](https://github.com/nlohmann))
- Consolidate documentation [\#3071](https://github.com/nlohmann/json/pull/3071) ([nlohmann](https://github.com/nlohmann))
- Add recursive update function [\#3069](https://github.com/nlohmann/json/pull/3069) ([nlohmann](https://github.com/nlohmann))

## [v3.10.4](https://github.com/nlohmann/json/releases/tag/v3.10.4) (2021-10-16)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.10.3...v3.10.4)

- Compiler error in output serializer due to 'incompatible initializer' [\#3081](https://github.com/nlohmann/json/issues/3081)
- Strange behaviour when using std::sort on std::vector\<json\> [\#3080](https://github.com/nlohmann/json/issues/3080)
- Unhandled exception: nlohmann::detail::parse\_error [\#3078](https://github.com/nlohmann/json/issues/3078)
- explicit constructor with default does not compile [\#3077](https://github.com/nlohmann/json/issues/3077)
- Parse an object but get an array using GCC [\#3076](https://github.com/nlohmann/json/issues/3076)
- Version 3.10.3 breaks backward-compatibility with 3.10.2 [\#3070](https://github.com/nlohmann/json/issues/3070)
- Feature request, Add to\_json/from\_json to align with other to/from binary api.  [\#3067](https://github.com/nlohmann/json/issues/3067)
- vcpkg is out of date [\#3066](https://github.com/nlohmann/json/issues/3066)

- Revert invalid fix [\#3082](https://github.com/nlohmann/json/pull/3082) ([nlohmann](https://github.com/nlohmann))
- Allow to use get with explicit constructor [\#3079](https://github.com/nlohmann/json/pull/3079) ([nlohmann](https://github.com/nlohmann))
- fix std::filesystem::path regression [\#3073](https://github.com/nlohmann/json/pull/3073) ([theodelrieu](https://github.com/theodelrieu))

## [v3.10.3](https://github.com/nlohmann/json/releases/tag/v3.10.3) (2021-10-08)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.10.2...v3.10.3)

- Parsing an emtpy string returns a string with size 1 instead of expected 0 [\#3057](https://github.com/nlohmann/json/issues/3057)
- Linking error  "duplicate symbol: std::type\_info::operator==" on static build with MinGW [\#3042](https://github.com/nlohmann/json/issues/3042)
- Yet another assertion failure when inserting into arrays with JSON\_DIAGNOSTICS set [\#3032](https://github.com/nlohmann/json/issues/3032)
- accept and parse function not work well with a pure number string [\#3029](https://github.com/nlohmann/json/issues/3029)
- push\_back doesn't work for serializing containers [\#3027](https://github.com/nlohmann/json/issues/3027)
- Strange behaviour when creating array with single element [\#3025](https://github.com/nlohmann/json/issues/3025)
- Input ordered\_json doesn't work [\#3023](https://github.com/nlohmann/json/issues/3023)
- Issue iterating through 'items' [\#3021](https://github.com/nlohmann/json/issues/3021)
- Cannot spell the namespace right [\#3015](https://github.com/nlohmann/json/issues/3015)
- JSON Parse error when reading json object from file [\#3011](https://github.com/nlohmann/json/issues/3011)
- Parent pointer not properly set when using update\(\) [\#3007](https://github.com/nlohmann/json/issues/3007)
- Overwriting terminated null character [\#3001](https://github.com/nlohmann/json/issues/3001)
- 'operator =' is ambiguous on VS2017 [\#2997](https://github.com/nlohmann/json/issues/2997)
- JSON Patch for Array Elements [\#2994](https://github.com/nlohmann/json/issues/2994)
- JSON Parse throwing error [\#2983](https://github.com/nlohmann/json/issues/2983)
- to\_{binary format} does not provide a mechanism for specifying a custom allocator for the returned type. [\#2982](https://github.com/nlohmann/json/issues/2982)
- 3.10.1 zip json.hpp has version number 3.10.0 instead of 3.10.1 [\#2973](https://github.com/nlohmann/json/issues/2973)
- Assertion failure when serializing array with JSON\_DIAGNOSTICS set [\#2926](https://github.com/nlohmann/json/issues/2926)

- Fix Clang version [\#3040](https://github.com/nlohmann/json/pull/3040) ([nlohmann](https://github.com/nlohmann))
- Fix assertion failure for JSON\_DIAGNOSTICS [\#3037](https://github.com/nlohmann/json/pull/3037) ([carlsmedstad](https://github.com/carlsmedstad))
- meta: fix is\_compatible/constructible traits [\#3020](https://github.com/nlohmann/json/pull/3020) ([theodelrieu](https://github.com/theodelrieu))
- Set parent pointers for values inserted via update\(\) \(fixes \#3007\). [\#3008](https://github.com/nlohmann/json/pull/3008) ([AnthonyVH](https://github.com/AnthonyVH))
- Allow allocators for output\_vector\_adapter [\#2989](https://github.com/nlohmann/json/pull/2989) ([nlohmann](https://github.com/nlohmann))
- Re-add Clang 12 [\#2986](https://github.com/nlohmann/json/pull/2986) ([nlohmann](https://github.com/nlohmann))
- Use new Docker image [\#2981](https://github.com/nlohmann/json/pull/2981) ([nlohmann](https://github.com/nlohmann))
- Update docset generation script [\#2967](https://github.com/nlohmann/json/pull/2967) ([nlohmann](https://github.com/nlohmann))

## [v3.10.2](https://github.com/nlohmann/json/releases/tag/v3.10.2) (2021-08-26)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.10.1...v3.10.2)

- Annoying -Wundef on new JSON\_DIAGNOSTICS macro [\#2975](https://github.com/nlohmann/json/issues/2975)
- += issue with multiple redirection. [\#2970](https://github.com/nlohmann/json/issues/2970)
- "incomplete type ‘nlohmann::detail::wide\_string\_input\_helper" compilation error [\#2969](https://github.com/nlohmann/json/issues/2969)

- Fix -Wunused warnings on JSON\_DIAGNOSTICS  [\#2976](https://github.com/nlohmann/json/pull/2976) ([gcerretani](https://github.com/gcerretani))

## [v3.10.1](https://github.com/nlohmann/json/releases/tag/v3.10.1) (2021-08-24)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.10.0...v3.10.1)

- JSON\_DIAGNOSTICS assertion for ordered\_json [\#2962](https://github.com/nlohmann/json/issues/2962)
- Inserting in unordered json using a pointer retains the leading slash [\#2958](https://github.com/nlohmann/json/issues/2958)
- Test  \#9: test-cbor test case sample.json fails in debug mode - Stack overflow [\#2955](https://github.com/nlohmann/json/issues/2955)
- 3.10.0 broke at least the Bear project [\#2953](https://github.com/nlohmann/json/issues/2953)
- 2 tests fail in 3.10.0: cmake\_fetch\_content\_configure, cmake\_fetch\_content\_build [\#2951](https://github.com/nlohmann/json/issues/2951)
- ctest \(58+60,/67 cmake\_import\_configure\) fails when build with -D JSON\_Install:BOOL=OFF because of missing nlohmann\_jsonTargets.cmake [\#2946](https://github.com/nlohmann/json/issues/2946)
- Document vcpkg usage [\#2944](https://github.com/nlohmann/json/issues/2944)
- Linker error LNK2005 when compiling \(x64\) json-3.10.0.zip with Visual Studio 2019 16.11.1  [\#2941](https://github.com/nlohmann/json/issues/2941)
- Move Travis jobs to travis-ci.com [\#2938](https://github.com/nlohmann/json/issues/2938)

- Fixed typo in docs/api/basic\_json/parse.md [\#2968](https://github.com/nlohmann/json/pull/2968) ([mbadhan](https://github.com/mbadhan))
- Add link to Homebrew package [\#2966](https://github.com/nlohmann/json/pull/2966) ([nlohmann](https://github.com/nlohmann))
- Fix parent update for diagnostics with ordered\_json [\#2963](https://github.com/nlohmann/json/pull/2963) ([nlohmann](https://github.com/nlohmann))
- Set stack size for some unit tests when using MSVC [\#2961](https://github.com/nlohmann/json/pull/2961) ([nlohmann](https://github.com/nlohmann))
- Add regression test [\#2960](https://github.com/nlohmann/json/pull/2960) ([nlohmann](https://github.com/nlohmann))
- Update Travis badge [\#2959](https://github.com/nlohmann/json/pull/2959) ([nlohmann](https://github.com/nlohmann))
- Fix some extra ";" clang warnings [\#2957](https://github.com/nlohmann/json/pull/2957) ([Hallot](https://github.com/Hallot))
- Add documentation for integration via vcpkg [\#2954](https://github.com/nlohmann/json/pull/2954) ([nlohmann](https://github.com/nlohmann))
- Avoid duplicate AppVeyor builds [\#2952](https://github.com/nlohmann/json/pull/2952) ([nlohmann](https://github.com/nlohmann))
- 🚨 fix gdb\_pretty\_printer failure on basic types [\#2950](https://github.com/nlohmann/json/pull/2950) ([senyai](https://github.com/senyai))
- Add header to use value\_t [\#2948](https://github.com/nlohmann/json/pull/2948) ([nlohmann](https://github.com/nlohmann))
- Skip some tests if JSON\_Install is not set [\#2947](https://github.com/nlohmann/json/pull/2947) ([nlohmann](https://github.com/nlohmann))
- Remove outdated json\_unit test binary [\#2945](https://github.com/nlohmann/json/pull/2945) ([nlohmann](https://github.com/nlohmann))
- Updating the Homebrew Command [\#2943](https://github.com/nlohmann/json/pull/2943) ([amirmasoudabdol](https://github.com/amirmasoudabdol))

## [v3.10.0](https://github.com/nlohmann/json/releases/tag/v3.10.0) (2021-08-17)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.9.1...v3.10.0)

- Latest version 3.9.1 uses throw instead of JSON\_THROW in the amalgamated json.hpp file [\#2934](https://github.com/nlohmann/json/issues/2934)
- Copy to a variable inside a Structure [\#2933](https://github.com/nlohmann/json/issues/2933)
- warning C4068: unknown pragma 'GCC' on MSVC/cl [\#2924](https://github.com/nlohmann/json/issues/2924)
- Errors during ninja test [\#2918](https://github.com/nlohmann/json/issues/2918)
- compiler warning: "not return a value" [\#2917](https://github.com/nlohmann/json/issues/2917)
- Comparison floating points causes warning [\#2909](https://github.com/nlohmann/json/issues/2909)
- Why can't I have std::vector\<json&\> testList? [\#2900](https://github.com/nlohmann/json/issues/2900)
- \[json.hpp\] from releases doesnt work [\#2897](https://github.com/nlohmann/json/issues/2897)
- g++ \(11\) -Wuseless-cast gives lots of warnings [\#2893](https://github.com/nlohmann/json/issues/2893)
- Cannot serialize and immediatly deserialize json to/from bson [\#2892](https://github.com/nlohmann/json/issues/2892)
- Floating-point precision conversion error [\#2876](https://github.com/nlohmann/json/issues/2876)
- How to avoid escaping for an already escaped string in .dump\(\) [\#2870](https://github.com/nlohmann/json/issues/2870)
- can't parse std::vector\<std::byte\> [\#2869](https://github.com/nlohmann/json/issues/2869)
- ASAN detects memory leaks [\#2865](https://github.com/nlohmann/json/issues/2865)
- Binary subtype field cannot represent all CBOR tags [\#2863](https://github.com/nlohmann/json/issues/2863)
- string literals possibly being parsed as another type due to the presence of only digits and full-stops [\#2852](https://github.com/nlohmann/json/issues/2852)
- json::parse\(\) works only with absolute paths [\#2851](https://github.com/nlohmann/json/issues/2851)
- Compiler Warnings on Raspberry Pi OS [\#2850](https://github.com/nlohmann/json/issues/2850)
- Braced initialization and aggregate initialization behavior is different for `json::array()` function call. [\#2848](https://github.com/nlohmann/json/issues/2848)
- 3.9.1: test suite is failing [\#2845](https://github.com/nlohmann/json/issues/2845)
- Documentation for macro JSON\_NO\_IO is missing [\#2842](https://github.com/nlohmann/json/issues/2842)
- Assertion failure when inserting into arrays with JSON\_DIAGNOSTICS set [\#2838](https://github.com/nlohmann/json/issues/2838)
- HELP! There is a memory leak in the code?! [\#2837](https://github.com/nlohmann/json/issues/2837)
- Elegant conversion of a 2-D-json array to a standard C++ array [\#2805](https://github.com/nlohmann/json/issues/2805)
- Swift Package Manager support [\#2802](https://github.com/nlohmann/json/issues/2802)
- Referencing a subkey which doesn't exist gives crash [\#2797](https://github.com/nlohmann/json/issues/2797)
- Failed benchmark due to renamed branch [\#2796](https://github.com/nlohmann/json/issues/2796)
- Build Errors with VS 2019 and json Version 3.9.1 when attempting to replicate SAX Example [\#2782](https://github.com/nlohmann/json/issues/2782)
- Value with spaces cannot be parsed [\#2781](https://github.com/nlohmann/json/issues/2781)
- \[Question\] CBOR rfc support. [\#2779](https://github.com/nlohmann/json/issues/2779)
- Using JSON.hpp header file in Visual Studio 2013 \(C++ Project\) [\#2775](https://github.com/nlohmann/json/issues/2775)
- compilation error on clang-8 + C++17 [\#2759](https://github.com/nlohmann/json/issues/2759)
- Undefined symbol EOF  [\#2755](https://github.com/nlohmann/json/issues/2755)
- Parsing a string into json object behaves differently under g++ and MinGW compilers. [\#2746](https://github.com/nlohmann/json/issues/2746)
- big git history size [\#2742](https://github.com/nlohmann/json/issues/2742)
- How to get reference of std::vector\<T\> [\#2735](https://github.com/nlohmann/json/issues/2735)
- CMake failure in VS2019 Community [\#2734](https://github.com/nlohmann/json/issues/2734)
- Possibility to use with custom c++ version to use in intel sgx enclaves [\#2730](https://github.com/nlohmann/json/issues/2730)
- Possibility to use without the dependency to file io and streams to use in intel sgx enclaves [\#2728](https://github.com/nlohmann/json/issues/2728)
- error C2784& error C2839... in my visual studio 2015 compiler [\#2726](https://github.com/nlohmann/json/issues/2726)
- `-fno-expection` not respected anymore in 3.9.1 [\#2725](https://github.com/nlohmann/json/issues/2725)
- When exceptions disabled with JSON\_NOEXCEPTION, lib just aborts without any message [\#2724](https://github.com/nlohmann/json/issues/2724)
- Critical error detected c0000374 on windows10 msvc 2019 16.8.5 [\#2710](https://github.com/nlohmann/json/issues/2710)
- unused parameter error/warning [\#2706](https://github.com/nlohmann/json/issues/2706)
- How to store data into a Map from json file [\#2691](https://github.com/nlohmann/json/issues/2691)
- Tests do not compile with pre-release glibc [\#2686](https://github.com/nlohmann/json/issues/2686)
- compile errors .... chromium-style [\#2680](https://github.com/nlohmann/json/issues/2680)
- .dump\(\) not allowing compact form [\#2678](https://github.com/nlohmann/json/issues/2678)
- error: no matching function for call to ‘nlohmann::basic\_json\<\>::value\(int, std::set\<int\>&\)’ [\#2671](https://github.com/nlohmann/json/issues/2671)
- Compiler warning: unused parameter [\#2668](https://github.com/nlohmann/json/issues/2668)
- Deserializing to a struct as shown on the project homepage throws compile time errors [\#2665](https://github.com/nlohmann/json/issues/2665)
- Unable to compile on MSVC 2019 with SDL checking enabled: This function or variable may be unsafe [\#2664](https://github.com/nlohmann/json/issues/2664)
- terminating with uncaught exception of type nlohmann::detail::type\_error: \[json.exception.type\_error.302\] type must be array, but is object [\#2661](https://github.com/nlohmann/json/issues/2661)
- unused-parameter on OSX when Diagnostics is off [\#2658](https://github.com/nlohmann/json/issues/2658)
- std::pair wrong serialization [\#2655](https://github.com/nlohmann/json/issues/2655)
- The result of json is\_number\_integer\(\) function is wrong when read a json file  [\#2653](https://github.com/nlohmann/json/issues/2653)
- 2 backslash cause problem [\#2652](https://github.com/nlohmann/json/issues/2652)
- No support for using an external/system copy of Hedley [\#2651](https://github.com/nlohmann/json/issues/2651)
- error: incomplete type 'qfloat16' used in type trait expression [\#2650](https://github.com/nlohmann/json/issues/2650)
- Unused variable in exception class when not using improved diagnostics [\#2646](https://github.com/nlohmann/json/issues/2646)
- I am trying to do this - converting from wstring works incorrectly! [\#2642](https://github.com/nlohmann/json/issues/2642)
- Exception 207 On ARM Processor During Literal String Parsing [\#2634](https://github.com/nlohmann/json/issues/2634)
- double free or corruption \(!prev\) error on Json push\_back and write [\#2632](https://github.com/nlohmann/json/issues/2632)
- nlohmann::detail::parse\_error: syntax error while parsing CBOR string: expected length specification \(0x60-0x7B\) or indefinite string type \(0x7F\) [\#2629](https://github.com/nlohmann/json/issues/2629)
- please allow disabling implicit conversions in non-single-file use [\#2621](https://github.com/nlohmann/json/issues/2621)
- Preserve decimal formatting [\#2618](https://github.com/nlohmann/json/issues/2618)
- Visual Studio Visual Assist code issues reported by VA code inspection of file json.hpp [\#2615](https://github.com/nlohmann/json/issues/2615)
- Missing get function and no viable overloaded '=' on mac [\#2610](https://github.com/nlohmann/json/issues/2610)
- corruption when parse from string [\#2603](https://github.com/nlohmann/json/issues/2603)
- Parse from byte-vector results in compile error [\#2602](https://github.com/nlohmann/json/issues/2602)
- Memory leak when working on ARM Linux [\#2601](https://github.com/nlohmann/json/issues/2601)
- Unhandled exception in test-cbor.exe Stack overflow when debugging project with Visual Studio 2019 16.7.7 compiled with c++17 or c++latest [\#2598](https://github.com/nlohmann/json/issues/2598)
- Error in download\_test\_data.vcxproj when compiling with Visual Studio 2019 16.7.7 Professional msbuild on Windows 10 2004 Professional [\#2594](https://github.com/nlohmann/json/issues/2594)
- Warnings  C4715 and C4127 when building json-3.9.1 with Visual Studio 2019 16.7.7 [\#2592](https://github.com/nlohmann/json/issues/2592)
- I tried some change to dump\(\) for \[1,2,3...\] [\#2584](https://github.com/nlohmann/json/issues/2584)
- try/catch block does not catch parsing error [\#2579](https://github.com/nlohmann/json/issues/2579)
- Serializing uint64\_t is broken for large values [\#2578](https://github.com/nlohmann/json/issues/2578)
- deserializing arrays should be part of the library [\#2575](https://github.com/nlohmann/json/issues/2575)
- Deserialization to std::array with non-default constructable types fails [\#2574](https://github.com/nlohmann/json/issues/2574)
- Compilation error when trying to use same type for number\_integer\_t and number\_unsigned\_t in basic\_json template specification. [\#2573](https://github.com/nlohmann/json/issues/2573)
- compiler error: directive output may be truncated writing between 2 and 8 bytes [\#2572](https://github.com/nlohmann/json/issues/2572)
- Incorrect convert map to json when key cannot construct an string i.e. int  [\#2564](https://github.com/nlohmann/json/issues/2564)
- no matching function for call to ‘nlohmann::basic\_json\<\>::basic\_json\(\<brace-enclosed initializer list\>\)’ [\#2559](https://github.com/nlohmann/json/issues/2559)
- type\_error factory creates a dangling pointer \(in VisualStudio 2019\) [\#2535](https://github.com/nlohmann/json/issues/2535)
- Cannot assign from ordered\_json vector\<CustomStruct\> to value in not ordered json [\#2528](https://github.com/nlohmann/json/issues/2528)
- Qt6: Break changes [\#2519](https://github.com/nlohmann/json/issues/2519)
- valgrind memcheck Illegal instruction when use nlohmann::json::parse [\#2518](https://github.com/nlohmann/json/issues/2518)
- Buffer overflow [\#2515](https://github.com/nlohmann/json/issues/2515)
- Including CTest in the top-level CMakeLists.txt sets BUILD\_TESTING=ON for parent projects [\#2513](https://github.com/nlohmann/json/issues/2513)
- Compilation error when using NLOHMANN\_JSON\_SERIALIZE\_ENUM ordered\_json on libc++ [\#2491](https://github.com/nlohmann/json/issues/2491)
- Missing "void insert\( InputIt first, InputIt last \);" overload in nlohmann::ordered\_map [\#2490](https://github.com/nlohmann/json/issues/2490)
- Could not find a package configuration file provided by "nlohmann\_json" [\#2482](https://github.com/nlohmann/json/issues/2482)
- json becomes empty for unknown reason [\#2470](https://github.com/nlohmann/json/issues/2470)
- Using std::wstring as StringType fails compiling [\#2459](https://github.com/nlohmann/json/issues/2459)
- Sample code in GIF slide outdated \(cannot use emplace\(\) with array\) [\#2457](https://github.com/nlohmann/json/issues/2457)
- from\_json\<std::wstring\> is treated as an array on latest MSVC [\#2453](https://github.com/nlohmann/json/issues/2453)
- MemorySanitizer: use-of-uninitialized-value [\#2449](https://github.com/nlohmann/json/issues/2449)
- I need help [\#2441](https://github.com/nlohmann/json/issues/2441)
- type conversion failing with clang ext\_vector\_type  [\#2436](https://github.com/nlohmann/json/issues/2436)
- json::parse\(\) can't be resolved under specific circumstances [\#2427](https://github.com/nlohmann/json/issues/2427)
- from\_\*\(ptr, len\) deprecation [\#2426](https://github.com/nlohmann/json/issues/2426)
- Error ONLY in release mode [\#2425](https://github.com/nlohmann/json/issues/2425)
- "Custom data source" exemple make no sense [\#2423](https://github.com/nlohmann/json/issues/2423)
- Refuses to compile in project [\#2419](https://github.com/nlohmann/json/issues/2419)
- Compilation failure of tests with C++20 standard \(caused by change of u8 literals\)  [\#2413](https://github.com/nlohmann/json/issues/2413)
- No matching function for call to 'input\_adapter' under Xcode of with nlohmann version 3.9.1 [\#2412](https://github.com/nlohmann/json/issues/2412)
- Git tags are not valid semvers [\#2409](https://github.com/nlohmann/json/issues/2409)
- after dump, stderr output disappear [\#2403](https://github.com/nlohmann/json/issues/2403)
- Using custom string. [\#2398](https://github.com/nlohmann/json/issues/2398)
- value\(\) throws unhandled exception for partially specified json object [\#2393](https://github.com/nlohmann/json/issues/2393)
- assertion on runtime causes program to stop when accessing const json with missing key [\#2392](https://github.com/nlohmann/json/issues/2392)
- Usage with -fno-elide-constructors causes dump\(\) output to be array of `null`s [\#2387](https://github.com/nlohmann/json/issues/2387)
- Build fails with clang-cl due to override of CMAKE\_CXX\_COMPILER\(?\) [\#2384](https://github.com/nlohmann/json/issues/2384)
- std::optional not working with primitive types [\#2383](https://github.com/nlohmann/json/issues/2383)
- Unexpected array when initializing a json const& on gcc 4.8.5 using uniform syntax [\#2370](https://github.com/nlohmann/json/issues/2370)
- setprecision support [\#2362](https://github.com/nlohmann/json/issues/2362)
- json::parse\(allow\_exceptions = false\) documentation is misleading. [\#2360](https://github.com/nlohmann/json/issues/2360)
- std::begin and std::end usage without specifying std namespace [\#2359](https://github.com/nlohmann/json/issues/2359)
- Custom object conversion to json hangs in background thread [\#2358](https://github.com/nlohmann/json/issues/2358)
- Add support of nullable fields to NLOHMANN\_DEFINE\_TYPE\_NON\_INTRUSIVE and NLOHMANN\_DEFINE\_TYPE\_INTRUSIVE [\#2356](https://github.com/nlohmann/json/issues/2356)
- the portfile for the vcpkg is not working. [\#2351](https://github.com/nlohmann/json/issues/2351)
- Compiler warns of implicit fallthrough when defining preprocessor macro NDEBUG [\#2348](https://github.com/nlohmann/json/issues/2348)
- Compile error on Intel compiler running in Windows [\#2346](https://github.com/nlohmann/json/issues/2346)
- Build error caused by overwriting CMAKE\_CXX\_COMPILER [\#2343](https://github.com/nlohmann/json/issues/2343)
- Error: an attribute list cannot appear here     JSON\_HEDLEY\_DEPRECATED\_FOR [\#2342](https://github.com/nlohmann/json/issues/2342)
- compiler warning [\#2341](https://github.com/nlohmann/json/issues/2341)
- 3.9.0: tests make build non-reproducible [\#2324](https://github.com/nlohmann/json/issues/2324)
- Initialization different between gcc/clang [\#2311](https://github.com/nlohmann/json/issues/2311)
- Attempt to `get()` a numeric value as a type which cannot represent it should throw [\#2310](https://github.com/nlohmann/json/issues/2310)
- Surprising behaviour with overloaded operators [\#2256](https://github.com/nlohmann/json/issues/2256)
- ADL issue in input\_adapter [\#2248](https://github.com/nlohmann/json/issues/2248)
- Output adapters should be templated. [\#2172](https://github.com/nlohmann/json/issues/2172)
- error when using nlohmann::json, std::function and std::bind [\#2147](https://github.com/nlohmann/json/issues/2147)
- Remove undefined behavior for const operator\[\] [\#2111](https://github.com/nlohmann/json/issues/2111)
- json\({}\) gives null instead of empty object with GCC and -std=c++17 [\#2046](https://github.com/nlohmann/json/issues/2046)
- GDB pretty printing support [\#1952](https://github.com/nlohmann/json/issues/1952)
- Always compile tests with all warnings enabled and error out on warnings [\#1798](https://github.com/nlohmann/json/issues/1798)
- Fixes Cppcheck warnings [\#1759](https://github.com/nlohmann/json/issues/1759)
- How to get position info or parser context with custom from\_json\(\) that may throw exceptions? [\#1508](https://github.com/nlohmann/json/issues/1508)
- Suggestion to improve value\(\) accessors with respect to move semantics [\#1275](https://github.com/nlohmann/json/issues/1275)
- Add Key name to Exception [\#932](https://github.com/nlohmann/json/issues/932)

- Overwork warning flags [\#2936](https://github.com/nlohmann/json/pull/2936) ([nlohmann](https://github.com/nlohmann))
- Treat MSVC warnings as errors [\#2930](https://github.com/nlohmann/json/pull/2930) ([nlohmann](https://github.com/nlohmann))
- All: fix warnings when compiling with -Wswitch-enum [\#2927](https://github.com/nlohmann/json/pull/2927) ([fhuberts](https://github.com/fhuberts))
- Guard GCC pragmas [\#2925](https://github.com/nlohmann/json/pull/2925) ([nlohmann](https://github.com/nlohmann))
- Supress -Wfloat-equal on intended float comparisions [\#2911](https://github.com/nlohmann/json/pull/2911) ([Finkman](https://github.com/Finkman))
- Fix binary subtypes [\#2908](https://github.com/nlohmann/json/pull/2908) ([nlohmann](https://github.com/nlohmann))
- Fix useless-cast warnings [\#2902](https://github.com/nlohmann/json/pull/2902) ([nlohmann](https://github.com/nlohmann))
- Add regression test [\#2898](https://github.com/nlohmann/json/pull/2898) ([nlohmann](https://github.com/nlohmann))
- Refactor Unicode tests [\#2889](https://github.com/nlohmann/json/pull/2889) ([nlohmann](https://github.com/nlohmann))
- CMake cleanup [\#2885](https://github.com/nlohmann/json/pull/2885) ([nlohmann](https://github.com/nlohmann))
- Avoid string in case of empty CBOR objects [\#2879](https://github.com/nlohmann/json/pull/2879) ([nlohmann](https://github.com/nlohmann))
- Suppress C4127 warning in unit-json\_pointer.cpp [\#2875](https://github.com/nlohmann/json/pull/2875) ([nlohmann](https://github.com/nlohmann))
- Fix truncation warning [\#2874](https://github.com/nlohmann/json/pull/2874) ([nlohmann](https://github.com/nlohmann))
- Fix memory leak in to\_json [\#2872](https://github.com/nlohmann/json/pull/2872) ([nlohmann](https://github.com/nlohmann))
- Fix assertion failure in diagnostics [\#2866](https://github.com/nlohmann/json/pull/2866) ([nlohmann](https://github.com/nlohmann))
- Update documentation [\#2861](https://github.com/nlohmann/json/pull/2861) ([nlohmann](https://github.com/nlohmann))
- Consistency with `using` in README.md [\#2826](https://github.com/nlohmann/json/pull/2826) ([justanotheranonymoususer](https://github.com/justanotheranonymoususer))
- Properly constrain the basic\_json conversion operator [\#2825](https://github.com/nlohmann/json/pull/2825) ([ldionne](https://github.com/ldionne))
- Fix CI [\#2817](https://github.com/nlohmann/json/pull/2817) ([nlohmann](https://github.com/nlohmann))
- Specified git branch for google benchmark fetch in benchmark test [\#2795](https://github.com/nlohmann/json/pull/2795) ([grafail](https://github.com/grafail))
- Add C++ standards to macOS matrix [\#2790](https://github.com/nlohmann/json/pull/2790) ([nlohmann](https://github.com/nlohmann))
- Update URLs to HTTPS [\#2789](https://github.com/nlohmann/json/pull/2789) ([TotalCaesar659](https://github.com/TotalCaesar659))
- Link to Conan Center package added [\#2771](https://github.com/nlohmann/json/pull/2771) ([offa](https://github.com/offa))
- Keep consistent formatting [\#2770](https://github.com/nlohmann/json/pull/2770) ([jasmcaus](https://github.com/jasmcaus))
- Add a cmake option to use SYSTEM in target\_include\_directories [\#2762](https://github.com/nlohmann/json/pull/2762) ([jpl-mac](https://github.com/jpl-mac))
- replace EOF with std::char\_traits\<char\>::eof\(\) [\#2756](https://github.com/nlohmann/json/pull/2756) ([nlohmann](https://github.com/nlohmann))
- Fix typo in README [\#2754](https://github.com/nlohmann/json/pull/2754) ([mortenfyhn](https://github.com/mortenfyhn))
- Update documentation [\#2749](https://github.com/nlohmann/json/pull/2749) ([nlohmann](https://github.com/nlohmann))
- Add documentation for numbers [\#2747](https://github.com/nlohmann/json/pull/2747) ([nlohmann](https://github.com/nlohmann))
- Use Clang 12 in CI [\#2737](https://github.com/nlohmann/json/pull/2737) ([nlohmann](https://github.com/nlohmann))
- Fixes \#2730 [\#2731](https://github.com/nlohmann/json/pull/2731) ([theShmoo](https://github.com/theShmoo))
- Possibility to use without the dependency to file io and streams to use in intel sgx enclaves [\#2729](https://github.com/nlohmann/json/pull/2729) ([theShmoo](https://github.com/theShmoo))
- Update json.hpp [\#2707](https://github.com/nlohmann/json/pull/2707) ([raduteo](https://github.com/raduteo))
- pkg-config.pc.in: Don't concatenate paths [\#2690](https://github.com/nlohmann/json/pull/2690) ([doronbehar](https://github.com/doronbehar))
- add more CI steps [\#2689](https://github.com/nlohmann/json/pull/2689) ([nlohmann](https://github.com/nlohmann))
- Update doctest from 2.4.4 to 2.4.6 \(fixes \#2686\) [\#2687](https://github.com/nlohmann/json/pull/2687) ([musicinmybrain](https://github.com/musicinmybrain))
- License fix [\#2683](https://github.com/nlohmann/json/pull/2683) ([nlohmann](https://github.com/nlohmann))
- Update parse\_exceptions.md - correct `json::exception::parse_error` [\#2679](https://github.com/nlohmann/json/pull/2679) ([frasermarlow](https://github.com/frasermarlow))
- Remove HEDLEY annotation from exception::what\(\) [\#2673](https://github.com/nlohmann/json/pull/2673) ([remyjette](https://github.com/remyjette))
- Fix amount of entries in the json object [\#2659](https://github.com/nlohmann/json/pull/2659) ([abbaswasim](https://github.com/abbaswasim))
- Fix missing 1.78 in example in README.md [\#2625](https://github.com/nlohmann/json/pull/2625) ([wawiesel](https://github.com/wawiesel))
- Add GDB pretty printer [\#2607](https://github.com/nlohmann/json/pull/2607) ([nlohmann](https://github.com/nlohmann))
- readme: fix tilde character display [\#2582](https://github.com/nlohmann/json/pull/2582) ([bl-ue](https://github.com/bl-ue))
- Add support for deserialization of STL containers of non-default constructable types \(fixes \#2574\). [\#2576](https://github.com/nlohmann/json/pull/2576) ([AnthonyVH](https://github.com/AnthonyVH))
- Better diagnostics [\#2562](https://github.com/nlohmann/json/pull/2562) ([nlohmann](https://github.com/nlohmann))
- CI targets [\#2561](https://github.com/nlohmann/json/pull/2561) ([nlohmann](https://github.com/nlohmann))
- Add switch to skip non-reproducible tests. [\#2560](https://github.com/nlohmann/json/pull/2560) ([nlohmann](https://github.com/nlohmann))
- Fix compilation of input\_adapter\(container\) in edge cases [\#2553](https://github.com/nlohmann/json/pull/2553) ([jasujm](https://github.com/jasujm))
- Allow parsing from std::byte containers [\#2550](https://github.com/nlohmann/json/pull/2550) ([nlohmann](https://github.com/nlohmann))
- Travis doesn't run any tests in C++17 mode [\#2540](https://github.com/nlohmann/json/pull/2540) ([karzhenkov](https://github.com/karzhenkov))
- Doctest is updated to v2.4.3 [\#2538](https://github.com/nlohmann/json/pull/2538) ([YarikTH](https://github.com/YarikTH))
- Fix warnings [\#2537](https://github.com/nlohmann/json/pull/2537) ([nlohmann](https://github.com/nlohmann))
- Fix a shadowing warning [\#2536](https://github.com/nlohmann/json/pull/2536) ([nlohmann](https://github.com/nlohmann))
- Clarify license of is\_complete\_type implementation [\#2534](https://github.com/nlohmann/json/pull/2534) ([nlohmann](https://github.com/nlohmann))
- Do not unconditionally redefine C++14 constructs [\#2533](https://github.com/nlohmann/json/pull/2533) ([nlohmann](https://github.com/nlohmann))
- Doctest is updated to v2.4.1 [\#2525](https://github.com/nlohmann/json/pull/2525) ([YarikTH](https://github.com/YarikTH))
- Add MAIN\_PROJECT check for test and install options [\#2514](https://github.com/nlohmann/json/pull/2514) ([globberwops](https://github.com/globberwops))
- Ranged insert test section is added in unit-ordered\_json.cpp [\#2512](https://github.com/nlohmann/json/pull/2512) ([YarikTH](https://github.com/YarikTH))
- Add asserts to suppress C28020 [\#2447](https://github.com/nlohmann/json/pull/2447) ([jbzdarkid](https://github.com/jbzdarkid))
- Change argument name "subtype" in byte\_container\_with\_subtype [\#2444](https://github.com/nlohmann/json/pull/2444) ([linev](https://github.com/linev))
- 📝  add CPM.Cmake example [\#2406](https://github.com/nlohmann/json/pull/2406) ([leozz37](https://github.com/leozz37))
- Fix move constructor of json\_ref [\#2405](https://github.com/nlohmann/json/pull/2405) ([karzhenkov](https://github.com/karzhenkov))
- Properly select "Release" build for Travis [\#2375](https://github.com/nlohmann/json/pull/2375) ([karzhenkov](https://github.com/karzhenkov))
- Update Hedley [\#2367](https://github.com/nlohmann/json/pull/2367) ([nlohmann](https://github.com/nlohmann))
- Fix and extend documentation of discarded values [\#2363](https://github.com/nlohmann/json/pull/2363) ([nlohmann](https://github.com/nlohmann))
- Fix typos in documentation [\#2354](https://github.com/nlohmann/json/pull/2354) ([rbuch](https://github.com/rbuch))
- Remove "\#define private public" from tests [\#2352](https://github.com/nlohmann/json/pull/2352) ([nlohmann](https://github.com/nlohmann))
- Remove -Wimplicit-fallthrough warning [\#2349](https://github.com/nlohmann/json/pull/2349) ([nlohmann](https://github.com/nlohmann))
- Fix code to work without exceptions [\#2347](https://github.com/nlohmann/json/pull/2347) ([nlohmann](https://github.com/nlohmann))
- fix cmake script overwriting compiler path [\#2344](https://github.com/nlohmann/json/pull/2344) ([ongjunjie](https://github.com/ongjunjie))

## [v3.9.1](https://github.com/nlohmann/json/releases/tag/v3.9.1) (2020-08-06)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.9.0...v3.9.1)

- Can't parse not formatted JSON. [\#2340](https://github.com/nlohmann/json/issues/2340)
- parse returns desired array contained in array when JSON text begins with square bracket on gcc 7.5.0 [\#2339](https://github.com/nlohmann/json/issues/2339)
- Unexpected deserialization difference between Mac and Linux [\#2338](https://github.com/nlohmann/json/issues/2338)
- Reading ordered\_json from file causes compile error [\#2331](https://github.com/nlohmann/json/issues/2331)
- ignore\_comment=true fails on multiple consecutive lines starting with comments [\#2330](https://github.com/nlohmann/json/issues/2330)
- Update documentation about Homebrew installation and CMake integration - Homebrew [\#2326](https://github.com/nlohmann/json/issues/2326)
- Chinese character initialize error [\#2325](https://github.com/nlohmann/json/issues/2325)
- json.update and vector\<pair\>does not work with ordered\_json [\#2315](https://github.com/nlohmann/json/issues/2315)
- Ambiguous call to overloaded function [\#2210](https://github.com/nlohmann/json/issues/2210)

- Fix fallthrough warning [\#2333](https://github.com/nlohmann/json/pull/2333) ([nlohmann](https://github.com/nlohmann))
- Fix lexer to properly cope with repeated comments [\#2332](https://github.com/nlohmann/json/pull/2332) ([nlohmann](https://github.com/nlohmann))
- Fix name of Homebrew formula in documentation [\#2327](https://github.com/nlohmann/json/pull/2327) ([nlohmann](https://github.com/nlohmann))
- fix typo [\#2320](https://github.com/nlohmann/json/pull/2320) ([wx257osn2](https://github.com/wx257osn2))
- Fix a bug due to missing overloads in ordered\_map container [\#2319](https://github.com/nlohmann/json/pull/2319) ([nlohmann](https://github.com/nlohmann))
- cmake: install pkg-config file relative to current\_binary\_dir [\#2318](https://github.com/nlohmann/json/pull/2318) ([eli-schwartz](https://github.com/eli-schwartz))
- Fixed installation of pkg-config file on other than Ubuntu [\#2314](https://github.com/nlohmann/json/pull/2314) ([xvitaly](https://github.com/xvitaly))

## [v3.9.0](https://github.com/nlohmann/json/releases/tag/v3.9.0) (2020-07-27)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.8.0...v3.9.0)

- Unknown Type Name clang error when using NLOHMANN\_DEFINE\_TYPE\_NON\_INTRUSIVE [\#2313](https://github.com/nlohmann/json/issues/2313)
- Clang 10.0 / GCC 10.1 warnings on disabled exceptions [\#2304](https://github.com/nlohmann/json/issues/2304)
- Application stalls indefinitely with message byte size 10 [\#2293](https://github.com/nlohmann/json/issues/2293)
- linker error [\#2292](https://github.com/nlohmann/json/issues/2292)
- Add support for high-precision numbers in UBJSON encoding [\#2286](https://github.com/nlohmann/json/issues/2286)
- NLOHMANN\_DEFINE\_TYPE\_NON\_INTRUSIVE fails if the length of the argument is 10 [\#2280](https://github.com/nlohmann/json/issues/2280)
- Custom types : MACRO expansion bug [\#2267](https://github.com/nlohmann/json/issues/2267)
- to/from\_json Failing To Convert String [\#2238](https://github.com/nlohmann/json/issues/2238)
- clang 9.0 report warning: unused type alias 'size\_type' \[-Wunused-local-typedef\] [\#2221](https://github.com/nlohmann/json/issues/2221)
- Enormous array created when working with map\<int,T\> [\#2220](https://github.com/nlohmann/json/issues/2220)
- Can I disable sorting of json values [\#2219](https://github.com/nlohmann/json/issues/2219)
- Getting Qt types to work [\#2217](https://github.com/nlohmann/json/issues/2217)
- Convert to Qt QVariant  [\#2216](https://github.com/nlohmann/json/issues/2216)
- How to custom serialize same data type of vector? [\#2215](https://github.com/nlohmann/json/issues/2215)
- json constructor does not support std::optional [\#2214](https://github.com/nlohmann/json/issues/2214)
- Failing to Parse Valid JSON [\#2209](https://github.com/nlohmann/json/issues/2209)
- \(De-\)Serialization of std::variant with namespaces [\#2208](https://github.com/nlohmann/json/issues/2208)
- Addint support for complex type [\#2207](https://github.com/nlohmann/json/issues/2207)
- array\_index possible out of range [\#2205](https://github.com/nlohmann/json/issues/2205)
- Object deserialized as array [\#2204](https://github.com/nlohmann/json/issues/2204)
- Sending to a function a reference to a sub-branch [\#2200](https://github.com/nlohmann/json/issues/2200)
- How to Serialize derived class to JSON object?  [\#2199](https://github.com/nlohmann/json/issues/2199)
- JSON incorrectly serialized [\#2198](https://github.com/nlohmann/json/issues/2198)
- Exception Unhandled out\_of\_range error [\#2197](https://github.com/nlohmann/json/issues/2197)
- msgpack serialisation : float is treated as 64bit float, not 32bit float. [\#2196](https://github.com/nlohmann/json/issues/2196)
- Is it possible to use compile-time type guarantees for JSON structures? [\#2195](https://github.com/nlohmann/json/issues/2195)
- Question : performance against python dict [\#2194](https://github.com/nlohmann/json/issues/2194)
- vs2017 compile error [\#2192](https://github.com/nlohmann/json/issues/2192)
- Check if a key exists [\#2191](https://github.com/nlohmann/json/issues/2191)
- Failed to run tests due to missing test data on builders without Internet access [\#2190](https://github.com/nlohmann/json/issues/2190)
- 3.8.0: unit-cbor.cpp test failures [\#2189](https://github.com/nlohmann/json/issues/2189)
- 'nlohmann/json.hpp' file not found [\#2188](https://github.com/nlohmann/json/issues/2188)
- How to send json data over the wire? [\#2185](https://github.com/nlohmann/json/issues/2185)
- Ubuntu 16 not supporting nlohmann/json? [\#2184](https://github.com/nlohmann/json/issues/2184)
- .get\<std::string\> causing emdash errors [\#2180](https://github.com/nlohmann/json/issues/2180)
- Object properties should not be re-sorted alphabetically [\#2179](https://github.com/nlohmann/json/issues/2179)
- Custom type registration : instrusive API [\#2175](https://github.com/nlohmann/json/issues/2175)
- Many version of the function "void to\_json\(json& j, const MyStruct& struct\)" [\#2171](https://github.com/nlohmann/json/issues/2171)
- How should strings be escaped? [\#2155](https://github.com/nlohmann/json/issues/2155)
- Adding a value to an existing json puts it at the beginning instead of the end [\#2149](https://github.com/nlohmann/json/issues/2149)
- The header file is big, can we use what we need. [\#2134](https://github.com/nlohmann/json/issues/2134)
- Changing the default format for unordered\_map \(or other set\) [\#2132](https://github.com/nlohmann/json/issues/2132)
- Getting size of deserialized bson document [\#2131](https://github.com/nlohmann/json/issues/2131)
- implicit conversion failure [\#2128](https://github.com/nlohmann/json/issues/2128)
- Error thrown when parsing in a subclass [\#2124](https://github.com/nlohmann/json/issues/2124)
- explicit conversion to string not considered for std::map keys in GCC8 [\#2096](https://github.com/nlohmann/json/issues/2096)
- Add support for JSONC [\#2061](https://github.com/nlohmann/json/issues/2061)
- Library provides template arg for string\_type but assumes std::string in some places [\#2059](https://github.com/nlohmann/json/issues/2059)
- incremental parsing with sax\_parser [\#2030](https://github.com/nlohmann/json/issues/2030)
- Question about flatten and unflatten [\#1989](https://github.com/nlohmann/json/issues/1989)
- CBOR parser doesn't skip tags [\#1968](https://github.com/nlohmann/json/issues/1968)
- Compilation failure using Clang on Windows [\#1898](https://github.com/nlohmann/json/issues/1898)
- Fail to build when including json.hpp as a system include [\#1818](https://github.com/nlohmann/json/issues/1818)
- Parsing string into json doesn't preserve the order correctly. [\#1817](https://github.com/nlohmann/json/issues/1817)
- \[C++17\] Allow std::optional to convert to nlohmann::json [\#1749](https://github.com/nlohmann/json/issues/1749)
- How can I save json object in file in order?  [\#1717](https://github.com/nlohmann/json/issues/1717)
- Support for Comments [\#1513](https://github.com/nlohmann/json/issues/1513)
- clang compiler: error : unknown type name 'not' [\#1119](https://github.com/nlohmann/json/issues/1119)
- dump\(\) without alphabetical order [\#1106](https://github.com/nlohmann/json/issues/1106)
- operator T\(\) considered harmful [\#958](https://github.com/nlohmann/json/issues/958)
- Order of the elements in JSON object [\#952](https://github.com/nlohmann/json/issues/952)
- How to prevent alphabetical sorting of data? [\#727](https://github.com/nlohmann/json/issues/727)
- Why is an object ordering values by Alphabetical Order?  [\#660](https://github.com/nlohmann/json/issues/660)
- Feature request: Comments [\#597](https://github.com/nlohmann/json/issues/597)
- Head Elements Sorting [\#543](https://github.com/nlohmann/json/issues/543)
- Automatic ordered JSON [\#424](https://github.com/nlohmann/json/issues/424)
- Support for comments. [\#376](https://github.com/nlohmann/json/issues/376)
- Optional comment support. [\#363](https://github.com/nlohmann/json/issues/363)
- Strip comments / Minify [\#294](https://github.com/nlohmann/json/issues/294)
- maintaining order of keys during iteration [\#106](https://github.com/nlohmann/json/issues/106)

- Update documentation [\#2312](https://github.com/nlohmann/json/pull/2312) ([nlohmann](https://github.com/nlohmann))
- Fix bug in CBOR tag handling [\#2308](https://github.com/nlohmann/json/pull/2308) ([nlohmann](https://github.com/nlohmann))
- added inline to NLOHMANN\_DEFINE\_TYPE\_NON\_INTRUSIVE macro [\#2306](https://github.com/nlohmann/json/pull/2306) ([jwittbrodt](https://github.com/jwittbrodt))
- fixes unused variable 'ex' for \#2304 [\#2305](https://github.com/nlohmann/json/pull/2305) ([AODQ](https://github.com/AODQ))
- Cleanup [\#2303](https://github.com/nlohmann/json/pull/2303) ([nlohmann](https://github.com/nlohmann))
- Add test with multiple translation units [\#2301](https://github.com/nlohmann/json/pull/2301) ([nlohmann](https://github.com/nlohmann))
- Merge GitHub actions [\#2300](https://github.com/nlohmann/json/pull/2300) ([nlohmann](https://github.com/nlohmann))
- Fix unused parameter [\#2299](https://github.com/nlohmann/json/pull/2299) ([nlohmann](https://github.com/nlohmann))
- Add support for high-precision numbers in UBJSON encoding [\#2297](https://github.com/nlohmann/json/pull/2297) ([nlohmann](https://github.com/nlohmann))
- fix eof for get\_binary and get\_string [\#2294](https://github.com/nlohmann/json/pull/2294) ([jprochazk](https://github.com/jprochazk))
- Serialisation macros: increase upper bound on number of member variables [\#2287](https://github.com/nlohmann/json/pull/2287) ([pfeatherstone](https://github.com/pfeatherstone))
- add inline specifier for detail::combine [\#2285](https://github.com/nlohmann/json/pull/2285) ([T0b1-iOS](https://github.com/T0b1-iOS))
- Add static assertion for missing binary function in SAX interface [\#2282](https://github.com/nlohmann/json/pull/2282) ([nlohmann](https://github.com/nlohmann))
- Add test for target\_include\_directories [\#2279](https://github.com/nlohmann/json/pull/2279) ([nlohmann](https://github.com/nlohmann))
- Clean up maintainer Makefiles and fix some linter warnings [\#2274](https://github.com/nlohmann/json/pull/2274) ([nlohmann](https://github.com/nlohmann))
- Add option to ignore CBOR tags [\#2273](https://github.com/nlohmann/json/pull/2273) ([nlohmann](https://github.com/nlohmann))
- Hash function without allocation [\#2269](https://github.com/nlohmann/json/pull/2269) ([nlohmann](https://github.com/nlohmann))
- Add ClangCL for MSVC [\#2268](https://github.com/nlohmann/json/pull/2268) ([t-b](https://github.com/t-b))
- Makefile: Always use SED variable [\#2264](https://github.com/nlohmann/json/pull/2264) ([t-b](https://github.com/t-b))
- Add Xcode 12 CI [\#2262](https://github.com/nlohmann/json/pull/2262) ([nlohmann](https://github.com/nlohmann))
- Make library work with Clang on Windows [\#2259](https://github.com/nlohmann/json/pull/2259) ([nlohmann](https://github.com/nlohmann))
- Add ordered\_json specialization with ordered object keys [\#2258](https://github.com/nlohmann/json/pull/2258) ([nlohmann](https://github.com/nlohmann))
- Add pkg-config file [\#2253](https://github.com/nlohmann/json/pull/2253) ([ericonr](https://github.com/ericonr))
- Fix regression from \#2181 [\#2251](https://github.com/nlohmann/json/pull/2251) ([nlohmann](https://github.com/nlohmann))
- Tag binary values in cbor if set [\#2244](https://github.com/nlohmann/json/pull/2244) ([matthewbauer](https://github.com/matthewbauer))
- Make assert configurable via JSON\_ASSERT [\#2242](https://github.com/nlohmann/json/pull/2242) ([nlohmann](https://github.com/nlohmann))
- Add specialization of get\_to [\#2233](https://github.com/nlohmann/json/pull/2233) ([nlohmann](https://github.com/nlohmann))
- Refine documentation of error\_handler parameter [\#2232](https://github.com/nlohmann/json/pull/2232) ([nlohmann](https://github.com/nlohmann))
- Simplify conversion from/to custom types [\#2225](https://github.com/nlohmann/json/pull/2225) ([nlohmann](https://github.com/nlohmann))
- Remove unused typedefs [\#2224](https://github.com/nlohmann/json/pull/2224) ([nlohmann](https://github.com/nlohmann))
- Enable CMake policy CMP0077 [\#2222](https://github.com/nlohmann/json/pull/2222) ([alexreinking](https://github.com/alexreinking))
- Add option to ignore comments in parse/accept functions [\#2212](https://github.com/nlohmann/json/pull/2212) ([nlohmann](https://github.com/nlohmann))
- Fix Clang-Tidy warnings [\#2211](https://github.com/nlohmann/json/pull/2211) ([nlohmann](https://github.com/nlohmann))
- Simple ordered\_json that works on all supported compilers [\#2206](https://github.com/nlohmann/json/pull/2206) ([gatopeich](https://github.com/gatopeich))
- Use unsigned indizies for array index in json pointer [\#2203](https://github.com/nlohmann/json/pull/2203) ([t-b](https://github.com/t-b))
- Add option to not rely on Internet connectivity during test stage [\#2202](https://github.com/nlohmann/json/pull/2202) ([nlohmann](https://github.com/nlohmann))
- Serialize floating-point numbers with 32 bit when possible \(MessagePack\) [\#2201](https://github.com/nlohmann/json/pull/2201) ([nlohmann](https://github.com/nlohmann))
- Fix consistency in function `int_to_string()` [\#2193](https://github.com/nlohmann/json/pull/2193) ([dota17](https://github.com/dota17))
- Fix issue\#1275 [\#2181](https://github.com/nlohmann/json/pull/2181) ([dota17](https://github.com/dota17))
- C++20 support by removing swap specialization [\#2176](https://github.com/nlohmann/json/pull/2176) ([gracicot](https://github.com/gracicot))
- Feat/explicit conversion operator [\#1559](https://github.com/nlohmann/json/pull/1559) ([theodelrieu](https://github.com/theodelrieu))

## [v3.8.0](https://github.com/nlohmann/json/releases/tag/v3.8.0) (2020-06-14)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.7.3...v3.8.0)

- sorry delete this issue, i'm stupid [\#2187](https://github.com/nlohmann/json/issues/2187)
- Append to a std::nlohmann::json type [\#2186](https://github.com/nlohmann/json/issues/2186)
- Some troubles to compile the last revision [\#2177](https://github.com/nlohmann/json/issues/2177)
- ​\#​ Top level CMakeLists.txt​
​project​\(FOO\)
...
​option​\(FOO\_USE\_EXTERNAL\_JSON ​"Use an external JSON library"​ ​OFF​\)
...
​add\_subdirectory​\(thirdparty\)
...
​add\_library​\(foo ...\)
...
​\#​ Note that the namespaced target will always be available regardless of the​
​\#​ import method​
​target\_link\_libraries​\(foo ​PRIVATE​ nlohmann\_json::nlohmann\_json\) [\#2170](https://github.com/nlohmann/json/issues/2170)
- https://www.github.com/nlohmann/json/tree/develop/include%2Fnlohmann%2Fjson\_fwd.hpp [\#2169](https://github.com/nlohmann/json/issues/2169)
- templated from\_json of non primitive types causes gcc error [\#2168](https://github.com/nlohmann/json/issues/2168)
- few warnings/errors in copy assignment [\#2167](https://github.com/nlohmann/json/issues/2167)
- Different output when upgrading from clang 9 to clang 10 [\#2166](https://github.com/nlohmann/json/issues/2166)
- Cannot build with VS 2019 / C++17 [\#2163](https://github.com/nlohmann/json/issues/2163)
- Q: When I received an illegal string,How the program knows? [\#2162](https://github.com/nlohmann/json/issues/2162)
- Problem while reading a json file [\#2161](https://github.com/nlohmann/json/issues/2161)
- converting std::chrono::system\_clock::time\_point to json. [\#2159](https://github.com/nlohmann/json/issues/2159)
-  how to parse vector\<struct\> format [\#2157](https://github.com/nlohmann/json/issues/2157)
- nlohmann::json and =nullptr [\#2156](https://github.com/nlohmann/json/issues/2156)
- test-cbor fails [\#2154](https://github.com/nlohmann/json/issues/2154)
- Accessing array inside array syntax?  [\#2151](https://github.com/nlohmann/json/issues/2151)
- Best way to catch errors when querying json [\#2150](https://github.com/nlohmann/json/issues/2150)
- JSON Data Mapping Key-Value from other Key-Value [\#2148](https://github.com/nlohmann/json/issues/2148)
- Conflicts with std \<any\> compiling with GCC 10 [\#2146](https://github.com/nlohmann/json/issues/2146)
- Incorrect CMake FetchContent example [\#2142](https://github.com/nlohmann/json/issues/2142)
- Help for a Beginner? [\#2141](https://github.com/nlohmann/json/issues/2141)
- Read Json from File [\#2139](https://github.com/nlohmann/json/issues/2139)
- How to feed a predefined integer value into json string [\#2138](https://github.com/nlohmann/json/issues/2138)
- getting json array inside json object [\#2135](https://github.com/nlohmann/json/issues/2135)
- Add .contains example to doc [\#2133](https://github.com/nlohmann/json/issues/2133)
- Is it safe to return string.c\_str\(\) received from get\(\)? [\#2130](https://github.com/nlohmann/json/issues/2130)
- GCC 10: Compilation error when including any before including json header in C++17 mode [\#2129](https://github.com/nlohmann/json/issues/2129)
- Intersection of two json files [\#2127](https://github.com/nlohmann/json/issues/2127)
- App crashes when dump method called for non ascii chars. [\#2126](https://github.com/nlohmann/json/issues/2126)
- iterator based erase method [\#2122](https://github.com/nlohmann/json/issues/2122)
- quick and convenient api to get/set nested json values [\#2120](https://github.com/nlohmann/json/issues/2120)
- assigning nullptr to std::string [\#2118](https://github.com/nlohmann/json/issues/2118)
- usless\_cast warnings with gcc 9.3 and 10.1 \(C++17\) [\#2114](https://github.com/nlohmann/json/issues/2114)
- clang 10 warning [\#2113](https://github.com/nlohmann/json/issues/2113)
- Possible incorrect \_MSC\_VER reference [\#2112](https://github.com/nlohmann/json/issues/2112)
- warning under gcc 10.1 [\#2110](https://github.com/nlohmann/json/issues/2110)
- Wdeprecated-declarations from GCC v10.1.0 [\#2109](https://github.com/nlohmann/json/issues/2109)
- Global std::vector from json [\#2108](https://github.com/nlohmann/json/issues/2108)
- heap-buffer-overflow when using nlohmann/json, ASAN, and gtest [\#2107](https://github.com/nlohmann/json/issues/2107)
- exception 0x770DC5AF when i read an special char in json file [\#2106](https://github.com/nlohmann/json/issues/2106)
- json::parse\(\) fails to parse a dump\(2,' '\) output, yet does successfully parse dump\(\) [\#2105](https://github.com/nlohmann/json/issues/2105)
- run test-udt error  in MSVC 19.16.27034.0 [\#2103](https://github.com/nlohmann/json/issues/2103)
- Unable to dump to stringstream [\#2102](https://github.com/nlohmann/json/issues/2102)
- Can't ad an object in another objet [\#2101](https://github.com/nlohmann/json/issues/2101)
- Implicit conversion causes "cannot use operator\[\] with a string argument with string" [\#2098](https://github.com/nlohmann/json/issues/2098)
- C++20: char8\_t [\#2097](https://github.com/nlohmann/json/issues/2097)
- Compilation issues when included in project [\#2094](https://github.com/nlohmann/json/issues/2094)
- string value with null character causes infinite loop [\#2093](https://github.com/nlohmann/json/issues/2093)
- corrupted size vs. prev\_size \(aborted\) [\#2092](https://github.com/nlohmann/json/issues/2092)
- Get string field content without return std::string copy  [\#2091](https://github.com/nlohmann/json/issues/2091)
- JSON Comments \(JSON 5\) [\#2090](https://github.com/nlohmann/json/issues/2090)
- Remove \#include \<ciso646\> [\#2089](https://github.com/nlohmann/json/issues/2089)
- JSON library as a git submodule [\#2088](https://github.com/nlohmann/json/issues/2088)
- Apple Clang 11.0.3 on MacOS Catalina 10.15.4 not compiling [\#2087](https://github.com/nlohmann/json/issues/2087)
- Value function return empty object even if it exist [\#2086](https://github.com/nlohmann/json/issues/2086)
- Cannot debug but Run works [\#2085](https://github.com/nlohmann/json/issues/2085)
- Question about serialization. [\#2084](https://github.com/nlohmann/json/issues/2084)
- How to include in an external project [\#2083](https://github.com/nlohmann/json/issues/2083)
- Missing tests for binary values [\#2082](https://github.com/nlohmann/json/issues/2082)
- How to override default string serialization? [\#2079](https://github.com/nlohmann/json/issues/2079)
- Can't have a json type as a property in an arbitrary type [\#2078](https://github.com/nlohmann/json/issues/2078)
- New release? [\#2075](https://github.com/nlohmann/json/issues/2075)
- CMake FetchContent \> Updating the documentation? [\#2073](https://github.com/nlohmann/json/issues/2073)
- How to convert STL Vector \(of user defined type\) to Json [\#2072](https://github.com/nlohmann/json/issues/2072)
- how to make an array of objects [\#2070](https://github.com/nlohmann/json/issues/2070)
- ‘\_\_int64’ was not declared [\#2068](https://github.com/nlohmann/json/issues/2068)
- \[json.exception.type\_error.317\] cannot serialize binary data to text JSON [\#2067](https://github.com/nlohmann/json/issues/2067)
- Unexpected end of input; expected '\[', '{', or a literal [\#2066](https://github.com/nlohmann/json/issues/2066)
- Json structure can be nested? [\#2065](https://github.com/nlohmann/json/issues/2065)
- Bug: returning reference to local temporary object [\#2064](https://github.com/nlohmann/json/issues/2064)
- Allow to use non strict parsing [\#2063](https://github.com/nlohmann/json/issues/2063)
- Crashing on json::at [\#2062](https://github.com/nlohmann/json/issues/2062)
- How to convert a const std::vector\<char8\_t\> message to a json, to be able to parse it and extract information from it? Can you point to any examples? [\#2058](https://github.com/nlohmann/json/issues/2058)
- Nice library [\#2057](https://github.com/nlohmann/json/issues/2057)
- json.hpp:15372:22: error: expected unqualified-id if \(not std::isfinite\(x\)\): Started getting this bug after updating my XCode [\#2056](https://github.com/nlohmann/json/issues/2056)
- Confused as how I can extract the values from the JSON object. [\#2055](https://github.com/nlohmann/json/issues/2055)
- Warnings with GCC 10 [\#2052](https://github.com/nlohmann/json/issues/2052)
- Warnings with Clang 10 [\#2049](https://github.com/nlohmann/json/issues/2049)
- Update doctest [\#2048](https://github.com/nlohmann/json/issues/2048)
- Unclear error message: "cannot use operator\[\] with a string argument with array" [\#2047](https://github.com/nlohmann/json/issues/2047)
- Serializing std::variant\<T, std::vector\<T\>\> [\#2045](https://github.com/nlohmann/json/issues/2045)
- Crash when parse big jsonfile [\#2042](https://github.com/nlohmann/json/issues/2042)
- How to check if a key exists without silently generating null objects on the path [\#2041](https://github.com/nlohmann/json/issues/2041)
- Crash when traversing over items\(\) of temporary json objects [\#2040](https://github.com/nlohmann/json/issues/2040)
- How to parse multiple line value ? [\#2039](https://github.com/nlohmann/json/issues/2039)
- SAX API uses unsigned std::size\_t but  -1 if element size is not known; [\#2037](https://github.com/nlohmann/json/issues/2037)
- How to parse big decimal data [\#2036](https://github.com/nlohmann/json/issues/2036)
- how use   template \<typename T\> struct adl\_serializer  [\#2035](https://github.com/nlohmann/json/issues/2035)
- auto iterator returned by find to handle value depending if is string or numeric. [\#2032](https://github.com/nlohmann/json/issues/2032)
- pass find returned iterator to numeric variable. [\#2031](https://github.com/nlohmann/json/issues/2031)
- Parse error on valid json file [\#2029](https://github.com/nlohmann/json/issues/2029)
- Is here any elegant way to combine serialization and deserialization code? [\#2028](https://github.com/nlohmann/json/issues/2028)
- Notes about dump function [\#2027](https://github.com/nlohmann/json/issues/2027)
- Different JSON printouts for empty dictionary on Linux and Mac. [\#2026](https://github.com/nlohmann/json/issues/2026)
- easier way to get exception reason out of json\_sax\_dom\_callback\_parser without exceptions [\#2024](https://github.com/nlohmann/json/issues/2024)
- Using fifo\_map with base class and derived class [\#2023](https://github.com/nlohmann/json/issues/2023)
- Error reading JSON File  [\#2022](https://github.com/nlohmann/json/issues/2022)
- Parse causing crash on android.  Cannot catch. [\#2021](https://github.com/nlohmann/json/issues/2021)
- Extra backslashes in nested json [\#2020](https://github.com/nlohmann/json/issues/2020)
- How to create patch for merge\_patch input ? [\#2018](https://github.com/nlohmann/json/issues/2018)
- CppUTest/include/CppUTestExt/MockSupport.h:40: error: default argument for ‘MockFailureReporter\* failureReporterForThisCall’ has type ‘void\*’ [\#2017](https://github.com/nlohmann/json/issues/2017)
- including another file [\#2016](https://github.com/nlohmann/json/issues/2016)
- GNU PREREQ Error with gcc 9.3.0 [\#2015](https://github.com/nlohmann/json/issues/2015)
- Parse error: json.exception.parse\_error.101 - invalid string: ill-formed UTF-8 byte [\#2014](https://github.com/nlohmann/json/issues/2014)
- Add more flexibility to basic\_json's ObjectType \(and ArrayType\) [\#2013](https://github.com/nlohmann/json/issues/2013)
- afl persistent mode [\#2012](https://github.com/nlohmann/json/issues/2012)
- Compiler Errors under VS2019 in Appveyor CI [\#2009](https://github.com/nlohmann/json/issues/2009)
- Another compilation failure with Visual Studio [\#2007](https://github.com/nlohmann/json/issues/2007)
- Implicit cast to std::string broken again with VS2019 16.5.0 [\#2006](https://github.com/nlohmann/json/issues/2006)
- error: no matching member function for call to 'AddRaw' [\#2005](https://github.com/nlohmann/json/issues/2005)
- When I re-create an object again after the network request, an error is reported [\#2003](https://github.com/nlohmann/json/issues/2003)
- How to merge \(and not replace\) different Json::Value objects in jsoncpp [\#2001](https://github.com/nlohmann/json/issues/2001)
- scalar transforms to list [\#2000](https://github.com/nlohmann/json/issues/2000)
- Dump JSON containing multibyte characters [\#1999](https://github.com/nlohmann/json/issues/1999)
- Build error  when modify value [\#1998](https://github.com/nlohmann/json/issues/1998)
- How do i include a vector of pointers in my json? [\#1997](https://github.com/nlohmann/json/issues/1997)
- Compiler error wrt incomplete types changed in gcc8.3.0-26 [\#1996](https://github.com/nlohmann/json/issues/1996)
- NaN-like comparison behavior of discarded is inconvenient [\#1988](https://github.com/nlohmann/json/issues/1988)
- Maintaining JSON package in my CMake [\#1987](https://github.com/nlohmann/json/issues/1987)
- reading int number and string number [\#1986](https://github.com/nlohmann/json/issues/1986)
- Build error: keyword is hidden by macro definition! [\#1985](https://github.com/nlohmann/json/issues/1985)
- JSON patch diff for op=add formation is not as per standard \(RFC 6902\) [\#1983](https://github.com/nlohmann/json/issues/1983)
- json\_pointer.contains\(\) exception is incorrectly raised [\#1982](https://github.com/nlohmann/json/issues/1982)
- Error with non existing key [\#1981](https://github.com/nlohmann/json/issues/1981)
- Closed [\#1978](https://github.com/nlohmann/json/issues/1978)
- Where is the library built and what is the name? [\#1977](https://github.com/nlohmann/json/issues/1977)
- The cmake\_import example does not build [\#1976](https://github.com/nlohmann/json/issues/1976)
- Dumping core when reading invalid file [\#1975](https://github.com/nlohmann/json/issues/1975)
- Abort in dump\(\) method [\#1973](https://github.com/nlohmann/json/issues/1973)
- Unclear docs regarding parser\_callback\_t callbacks [\#1972](https://github.com/nlohmann/json/issues/1972)
- Possible memory leak on push\_back [\#1971](https://github.com/nlohmann/json/issues/1971)
- Is it possible to get a safe mutable reference/pointer to internal variant used in nlohmann json?  [\#1970](https://github.com/nlohmann/json/issues/1970)
- Getting a flatten json to map\<string, string\> [\#1957](https://github.com/nlohmann/json/issues/1957)
- forced type conversion or lexical cast without exception. [\#1955](https://github.com/nlohmann/json/issues/1955)
- Add json\_view type support to avoid excessive copying [\#1954](https://github.com/nlohmann/json/issues/1954)
- Adding "examples" section for real-life usages [\#1953](https://github.com/nlohmann/json/issues/1953)
- Add nlohmann::json::key\_type [\#1951](https://github.com/nlohmann/json/issues/1951)
- cannot use operator\[\] with a string argument with string [\#1949](https://github.com/nlohmann/json/issues/1949)
- std::ifstream \>\> json error [\#1948](https://github.com/nlohmann/json/issues/1948)
- Cannot update json data in an iterator? [\#1947](https://github.com/nlohmann/json/issues/1947)
- How can i build this library in VS 2017? [\#1943](https://github.com/nlohmann/json/issues/1943)
- json\_pointer.contains\(\) exceptions when path not found [\#1942](https://github.com/nlohmann/json/issues/1942)
- Nested objects serialize/deserialize [\#1941](https://github.com/nlohmann/json/issues/1941)
- Compile warning on architectures that are not x86 [\#1939](https://github.com/nlohmann/json/issues/1939)
- Version of nlohmann-json-dev in debian packages [\#1938](https://github.com/nlohmann/json/issues/1938)
- Create a json object for every cycle  [\#1937](https://github.com/nlohmann/json/issues/1937)
- How to get the object name? [\#1936](https://github.com/nlohmann/json/issues/1936)
- Reserve and resize function for basic json [\#1935](https://github.com/nlohmann/json/issues/1935)
- How to use json parse in tsl::ordread\_map? [\#1934](https://github.com/nlohmann/json/issues/1934)
- C++14 support is not enabled with msvc2015 [\#1932](https://github.com/nlohmann/json/issues/1932)
- Need help with to\_json for derived class, keep getting "cannot use operator" [\#1931](https://github.com/nlohmann/json/issues/1931)
- How to handle std::vector\<std::uint8\_t\> [\#1930](https://github.com/nlohmann/json/issues/1930)
- Heap corruption issue [\#1929](https://github.com/nlohmann/json/issues/1929)
- Add `std::wistream` support. [\#1928](https://github.com/nlohmann/json/issues/1928)
- This i can write and read any file thanks [\#1927](https://github.com/nlohmann/json/issues/1927)
- How can I get this simple example working? [\#1926](https://github.com/nlohmann/json/issues/1926)
- emplace\_back does not seems to work with the int 0 [\#1925](https://github.com/nlohmann/json/issues/1925)
- Why nlohmann does not release memory [\#1924](https://github.com/nlohmann/json/issues/1924)
- Is it possible to have template `json::parse` with `noexcept` specifier? [\#1922](https://github.com/nlohmann/json/issues/1922)
- JSON to wstring? [\#1921](https://github.com/nlohmann/json/issues/1921)
- GCC 10 tests build failure [\#1920](https://github.com/nlohmann/json/issues/1920)
- Size of binary json representations [\#1919](https://github.com/nlohmann/json/issues/1919)
- Accessing strings \(for example in keys or values\) without having the lib create a copy of it. [\#1916](https://github.com/nlohmann/json/issues/1916)
- operator== documentation should show how to apply custom comparison function [\#1915](https://github.com/nlohmann/json/issues/1915)
- char8\_t and std::u8string support [\#1914](https://github.com/nlohmann/json/issues/1914)
- std::is\_pod is deprecated in C++20 [\#1913](https://github.com/nlohmann/json/issues/1913)
- Incomplete types reported by \(experimental\) GCC10 [\#1912](https://github.com/nlohmann/json/issues/1912)
- Compile warnings on MSVC 14.2 [\#1911](https://github.com/nlohmann/json/issues/1911)
- How to parse json file with   type composition  of std::optional  and std::variant [\#1910](https://github.com/nlohmann/json/issues/1910)
- why root\_schema be  implemented as unique\_ptr in json-validator.cpp，could I use it as shared\_ptr? [\#1908](https://github.com/nlohmann/json/issues/1908)
- compile error in gcc-6.3.0 [\#1906](https://github.com/nlohmann/json/issues/1906)
- Scalar constexpr is odr-used when used as json initializer [\#1905](https://github.com/nlohmann/json/issues/1905)
- install Slack app [\#1904](https://github.com/nlohmann/json/issues/1904)
- typo in a comment [\#1903](https://github.com/nlohmann/json/issues/1903)
- Watch JSON variables in Debug [\#1902](https://github.com/nlohmann/json/issues/1902)
- does Json sdk cares about dfc dfd utf8 issue? [\#1901](https://github.com/nlohmann/json/issues/1901)
- Allow multiple line string value in JSON [\#1897](https://github.com/nlohmann/json/issues/1897)
- Writing map to json file  [\#1896](https://github.com/nlohmann/json/issues/1896)
- Small documentation mistake [\#1895](https://github.com/nlohmann/json/issues/1895)
- why static function `parse` cann't find in visual studio 2019 [\#1894](https://github.com/nlohmann/json/issues/1894)
- Best way to handle json files with missing key value pairs. [\#1893](https://github.com/nlohmann/json/issues/1893)
- accessing json object as multimap [\#1892](https://github.com/nlohmann/json/issues/1892)
- What is the best way to parse vec3s into glm::vec3 [\#1891](https://github.com/nlohmann/json/issues/1891)
- Get array of items without using vector [\#1890](https://github.com/nlohmann/json/issues/1890)
- Build errors \(clang 11.0.0\) on macOS 10.15.2 [\#1889](https://github.com/nlohmann/json/issues/1889)
- Multiple arrays to vectors help [\#1888](https://github.com/nlohmann/json/issues/1888)
- json::parse\(begin, end\) parse error on first character using uchar\* [\#1887](https://github.com/nlohmann/json/issues/1887)
- issue in free\(\) [\#1886](https://github.com/nlohmann/json/issues/1886)
- is\_number\_unsigned\(\) returns false for positive integers \(int or 0 or 1 literals\) [\#1885](https://github.com/nlohmann/json/issues/1885)
- MSVC build failure with /Zc:\_\_cplusplus and C++17 [\#1883](https://github.com/nlohmann/json/issues/1883)
- RFC 6901 op:replace & arrays [\#1882](https://github.com/nlohmann/json/issues/1882)
- Problem with serialization of my custom template doubly-linked list [\#1881](https://github.com/nlohmann/json/issues/1881)
- is\_array\(\) is True, but raise 'cannot use operator\[\] for object iterators' [\#1880](https://github.com/nlohmann/json/issues/1880)
- Serialize dynamic array [\#1879](https://github.com/nlohmann/json/issues/1879)
- Serialization of struct object. [\#1877](https://github.com/nlohmann/json/issues/1877)
- warning:c4503 [\#1875](https://github.com/nlohmann/json/issues/1875)
- Why are flattened empty objects/arrays not representable? [\#1874](https://github.com/nlohmann/json/issues/1874)
- Container Overflow \(ASAN\) when using operator \>\> on an ifs [\#1873](https://github.com/nlohmann/json/issues/1873)
- Sub-array to vector or map object? [\#1870](https://github.com/nlohmann/json/issues/1870)
- WIP: QT \(cute\) type supports [\#1869](https://github.com/nlohmann/json/issues/1869)
- Compiler flags to disable features and shrink code size [\#1868](https://github.com/nlohmann/json/issues/1868)
- null strings [\#1867](https://github.com/nlohmann/json/issues/1867)
- Struct with array of struct and \_\_attribute\_\_\(\(packed\)\) [\#1866](https://github.com/nlohmann/json/issues/1866)
- Best way to extract numbers in the string? [\#1865](https://github.com/nlohmann/json/issues/1865)
- Displaying \\?\Volume{guid} from string to json giving error [\#1864](https://github.com/nlohmann/json/issues/1864)
- not working when compiling as x86 [\#1863](https://github.com/nlohmann/json/issues/1863)
- Skipping evaluation of log line expressions with a macro, is it possible? [\#1862](https://github.com/nlohmann/json/issues/1862)
- Suppress warnings [\#1861](https://github.com/nlohmann/json/issues/1861)
- conflit with g++ compile option -mwindows [\#1860](https://github.com/nlohmann/json/issues/1860)
- How to serialize nested classes to semi-flat JSON object? [\#1859](https://github.com/nlohmann/json/issues/1859)
- Memory Requirement for large json file [\#1858](https://github.com/nlohmann/json/issues/1858)
- Query a binary format \(BSON, CBOR, MessagePack, UBJSON\) [\#1856](https://github.com/nlohmann/json/issues/1856)
- Documentation on operator\[\] behavior with missing keys [\#1855](https://github.com/nlohmann/json/issues/1855)
- Problem in converting string into JSON; Can't parse successfully. [\#1854](https://github.com/nlohmann/json/issues/1854)
- json.at\_or\_default\(key, defaultval\) [\#1852](https://github.com/nlohmann/json/issues/1852)
- please improve the enum conversion documentation \(my example gist provided\) [\#1851](https://github.com/nlohmann/json/issues/1851)
- Default value returned on ValueType nlohmann::basic\_json::value \(const typename object\_t::key\_type& key, const ValueType& default\_value\) 	 [\#1850](https://github.com/nlohmann/json/issues/1850)
- Accounting for arbitrary precision numerical literals [\#1849](https://github.com/nlohmann/json/issues/1849)
- While trying to make a simple array, I get a nested array instead [\#1848](https://github.com/nlohmann/json/issues/1848)
- How to reuse the parser and serializer intermediate storage? [\#1847](https://github.com/nlohmann/json/issues/1847)
- Too much content in json.hpp leads to slow compilation [\#1845](https://github.com/nlohmann/json/issues/1845)
- Cannot read some data in json file [\#1843](https://github.com/nlohmann/json/issues/1843)
- Precompiled JSON library? [\#1842](https://github.com/nlohmann/json/issues/1842)
- Please change assert into throw\(maybe\) in line 17946 [\#1841](https://github.com/nlohmann/json/issues/1841)
- JSON for modern C++ ECCN information [\#1840](https://github.com/nlohmann/json/issues/1840)
- CI: reduce build time for Travis valgrind [\#1836](https://github.com/nlohmann/json/issues/1836)
- How do I traverse a json object and add new elements into the hierarchy [\#1834](https://github.com/nlohmann/json/issues/1834)
- Invalid UTF-8 byte at index 1: 0x65 [\#1831](https://github.com/nlohmann/json/issues/1831)
- Serialize big data in json [\#1828](https://github.com/nlohmann/json/issues/1828)
- Backslash '\' in value causes exception [\#1827](https://github.com/nlohmann/json/issues/1827)
- from\_json for non default constructible class with dependency injection [\#1819](https://github.com/nlohmann/json/issues/1819)
- Semi-frequent timeouts in `test-unicode_all` with 3.6.1 \(aarch64\) [\#1816](https://github.com/nlohmann/json/issues/1816)
- input\_adapter not user extensible [\#1813](https://github.com/nlohmann/json/issues/1813)
- crash at json::destroy on android  [\#1812](https://github.com/nlohmann/json/issues/1812)
- Logs are repeating while cmake [\#1809](https://github.com/nlohmann/json/issues/1809)
- Add a the possibility to add dynamic json objects [\#1795](https://github.com/nlohmann/json/issues/1795)
- Unnecessary test data file in the release [\#1790](https://github.com/nlohmann/json/issues/1790)
- Add support for parse stack limiting [\#1788](https://github.com/nlohmann/json/issues/1788)
- GCC -Wuseless-cast warnings [\#1777](https://github.com/nlohmann/json/issues/1777)
- compilation issue with NVCC 9.0 [\#1773](https://github.com/nlohmann/json/issues/1773)
- Unexpected behavior with fifo\_map json when copy and append  [\#1763](https://github.com/nlohmann/json/issues/1763)
- Parse error [\#1761](https://github.com/nlohmann/json/issues/1761)
- Assignment \(using value\(\)\) to nonexistent element behaves differently on Xcode 8 vs Xcode 10 [\#1758](https://github.com/nlohmann/json/issues/1758)
- Readme out of date [\#1756](https://github.com/nlohmann/json/issues/1756)
- cmake\_\* tests don't use the build system's compiler [\#1747](https://github.com/nlohmann/json/issues/1747)
- Static assertions for template type properties required [\#1729](https://github.com/nlohmann/json/issues/1729)
- Use float and possibly half in json::to\_cbor [\#1719](https://github.com/nlohmann/json/issues/1719)
- json::from\_cbor does not respect allow\_exceptions = false when input is string literal [\#1715](https://github.com/nlohmann/json/issues/1715)
- /Zc:\_\_cplusplus leads to C2416 [\#1695](https://github.com/nlohmann/json/issues/1695)
- `unflatten` vs objects with number-ish keys [\#1575](https://github.com/nlohmann/json/issues/1575)
- A "thinner" source code tar as part of release? [\#1572](https://github.com/nlohmann/json/issues/1572)
- Repository is almost 450MB [\#1497](https://github.com/nlohmann/json/issues/1497)
- Substantial performance penalty caused by polymorphic input adapter  [\#1457](https://github.com/nlohmann/json/issues/1457)
- Move tests to a separate repo [\#1235](https://github.com/nlohmann/json/issues/1235)
- reduce repos size [\#1185](https://github.com/nlohmann/json/issues/1185)
- CMakeLists.txt in release zips? [\#1184](https://github.com/nlohmann/json/issues/1184)
- Minimal branch? [\#1066](https://github.com/nlohmann/json/issues/1066)
- Move test blobs to a submodule? [\#732](https://github.com/nlohmann/json/issues/732)
- \[Question\] When using this as git submodule, will it clone the whole thing include test data and benchmark? [\#620](https://github.com/nlohmann/json/issues/620)
- Need to improve ignores.. [\#567](https://github.com/nlohmann/json/issues/567)
- Minimal repository \(current size very large\) [\#556](https://github.com/nlohmann/json/issues/556)
- For a header-only library you have to clone 214MB [\#482](https://github.com/nlohmann/json/issues/482)
- 17 MB / 90 MB repo size!? [\#96](https://github.com/nlohmann/json/issues/96)

- Improve parse\_ubjson\_fuzzer [\#2182](https://github.com/nlohmann/json/pull/2182) ([tanuj208](https://github.com/tanuj208))
- Add input adapter tests [\#2178](https://github.com/nlohmann/json/pull/2178) ([nlohmann](https://github.com/nlohmann))
- Fix warnings [\#2174](https://github.com/nlohmann/json/pull/2174) ([nlohmann](https://github.com/nlohmann))
- Fix PR\#1006 [\#2158](https://github.com/nlohmann/json/pull/2158) ([dota17](https://github.com/dota17))
- Fix issue\#1972 [\#2153](https://github.com/nlohmann/json/pull/2153) ([dota17](https://github.com/dota17))
- Update URLs to HTTPS [\#2152](https://github.com/nlohmann/json/pull/2152) ([TotalCaesar659](https://github.com/TotalCaesar659))
- Fix Issue\#1813: user defined input adapters [\#2145](https://github.com/nlohmann/json/pull/2145) ([FrancoisChabot](https://github.com/FrancoisChabot))
- Fix issue\#1939: Cast character to unsigned for comparison [\#2144](https://github.com/nlohmann/json/pull/2144) ([XyFreak](https://github.com/XyFreak))
- Fix issue\#2142: readme: fix typo in CMake FetchContent example [\#2143](https://github.com/nlohmann/json/pull/2143) ([quentin-dev](https://github.com/quentin-dev))
- Respect allow\_exceptions=false for binary formats [\#2140](https://github.com/nlohmann/json/pull/2140) ([nlohmann](https://github.com/nlohmann))
- Fix issue 2112 [\#2137](https://github.com/nlohmann/json/pull/2137) ([dota17](https://github.com/dota17))
- Add bleeding edge GCC to CI [\#2136](https://github.com/nlohmann/json/pull/2136) ([aokellermann](https://github.com/aokellermann))
- Clean up implementation of binary type [\#2125](https://github.com/nlohmann/json/pull/2125) ([nlohmann](https://github.com/nlohmann))
- Fixed a compilation error in MSVC [\#2121](https://github.com/nlohmann/json/pull/2121) ([gistrec](https://github.com/gistrec))
- Overwork CI [\#2119](https://github.com/nlohmann/json/pull/2119) ([nlohmann](https://github.com/nlohmann))
- Fix warnings from Clang 10 and GCC 9 [\#2116](https://github.com/nlohmann/json/pull/2116) ([nlohmann](https://github.com/nlohmann))
- Do not include \<ciso646\> when using C++17 [\#2115](https://github.com/nlohmann/json/pull/2115) ([nlohmann](https://github.com/nlohmann))
- Fix issue\#2086: disallow json::value\_t type parameter in value\(\) [\#2104](https://github.com/nlohmann/json/pull/2104) ([dota17](https://github.com/dota17))
- Fix Coveralls integration [\#2100](https://github.com/nlohmann/json/pull/2100) ([nlohmann](https://github.com/nlohmann))
- Add tests for binary values [\#2099](https://github.com/nlohmann/json/pull/2099) ([nlohmann](https://github.com/nlohmann))
- Use external test data [\#2081](https://github.com/nlohmann/json/pull/2081) ([nlohmann](https://github.com/nlohmann))
- Remove Doozer CI [\#2080](https://github.com/nlohmann/json/pull/2080) ([nlohmann](https://github.com/nlohmann))
- Fix README.md. Missing ``` [\#2077](https://github.com/nlohmann/json/pull/2077) ([ArthurSonzogni](https://github.com/ArthurSonzogni))
- Fix error message about invalid surrogate pairs [\#2076](https://github.com/nlohmann/json/pull/2076) ([rmisev](https://github.com/rmisev))
- Add CMake fetchcontent documentation and tests [\#2074](https://github.com/nlohmann/json/pull/2074) ([ArthurSonzogni](https://github.com/ArthurSonzogni))
- Properly pass serialize\_binary to dump function [\#2071](https://github.com/nlohmann/json/pull/2071) ([nlohmann](https://github.com/nlohmann))
- Fix returning reference to local temporary object [\#2069](https://github.com/nlohmann/json/pull/2069) ([nlohmann](https://github.com/nlohmann))
- updated wandbox link [\#2060](https://github.com/nlohmann/json/pull/2060) ([alexandermyasnikov](https://github.com/alexandermyasnikov))
- Fix bug in diff function [\#2054](https://github.com/nlohmann/json/pull/2054) ([nlohmann](https://github.com/nlohmann))
- Fix GCC compiler warnings [\#2053](https://github.com/nlohmann/json/pull/2053) ([nlohmann](https://github.com/nlohmann))
- Fix Clang compiler warnings [\#2051](https://github.com/nlohmann/json/pull/2051) ([nlohmann](https://github.com/nlohmann))
- Update doctest to 2.3.7 [\#2050](https://github.com/nlohmann/json/pull/2050) ([nlohmann](https://github.com/nlohmann))
- Fix issue\#1719 [\#2044](https://github.com/nlohmann/json/pull/2044) ([dota17](https://github.com/dota17))
- Add missing testcase about NaN in unit-constructor1.cpp [\#2043](https://github.com/nlohmann/json/pull/2043) ([dota17](https://github.com/dota17))
- Templatize basic\_json constructor from json\_ref [\#2034](https://github.com/nlohmann/json/pull/2034) ([ArtemSarmini](https://github.com/ArtemSarmini))
- Replace deprecated std::is\_pod [\#2033](https://github.com/nlohmann/json/pull/2033) ([nlohmann](https://github.com/nlohmann))
- Fixes \#1971 \(memory leak in basic\_json::push\_back\) [\#2025](https://github.com/nlohmann/json/pull/2025) ([ArtemSarmini](https://github.com/ArtemSarmini))
- fix \#1982:json\_pointer.contains\(\) exception is incorrectly raised [\#2019](https://github.com/nlohmann/json/pull/2019) ([dota17](https://github.com/dota17))
- Update LICENSE.MIT [\#2010](https://github.com/nlohmann/json/pull/2010) ([magamig](https://github.com/magamig))
- PR for \#2006 to test in AppVeyor. [\#2008](https://github.com/nlohmann/json/pull/2008) ([garethsb](https://github.com/garethsb))
- Added wsjcpp.yml [\#2004](https://github.com/nlohmann/json/pull/2004) ([sea-kg](https://github.com/sea-kg))
- fix error 'setw' is not a member of 'std' in Wandbox example [\#2002](https://github.com/nlohmann/json/pull/2002) ([alexandermyasnikov](https://github.com/alexandermyasnikov))
- catch exceptions for json\_pointer : ..../+99 [\#1990](https://github.com/nlohmann/json/pull/1990) ([dota17](https://github.com/dota17))
- Modify the document about operator== [\#1984](https://github.com/nlohmann/json/pull/1984) ([dota17](https://github.com/dota17))
- Rename argument array\_index to array\_indx in json\_pointer methods [\#1980](https://github.com/nlohmann/json/pull/1980) ([linev](https://github.com/linev))
- README: Fix string representation of `dump`ed `json` [\#1979](https://github.com/nlohmann/json/pull/1979) ([alexweej](https://github.com/alexweej))
- fix warnings in serializer.hpp for VS2019 [\#1969](https://github.com/nlohmann/json/pull/1969) ([dota17](https://github.com/dota17))
- Fix C26451 warnnings in to\_chars.hpp [\#1967](https://github.com/nlohmann/json/pull/1967) ([dota17](https://github.com/dota17))
- appveyor.yml: Compile and test with latest version for \_\_cplusplus ma… [\#1958](https://github.com/nlohmann/json/pull/1958) ([t-b](https://github.com/t-b))
- Fix typo in examples [\#1956](https://github.com/nlohmann/json/pull/1956) ([dota17](https://github.com/dota17))
- templated input adapters [\#1950](https://github.com/nlohmann/json/pull/1950) ([FrancoisChabot](https://github.com/FrancoisChabot))
- Update README.md : add a FAQ about memory release [\#1933](https://github.com/nlohmann/json/pull/1933) ([dota17](https://github.com/dota17))
- Some typos [\#1923](https://github.com/nlohmann/json/pull/1923) ([Coeur](https://github.com/Coeur))
- Fix link to parse function in README [\#1918](https://github.com/nlohmann/json/pull/1918) ([kastiglione](https://github.com/kastiglione))
- Readme: Updated links to hunter repo & docs [\#1917](https://github.com/nlohmann/json/pull/1917) ([jothepro](https://github.com/jothepro))
- Adds instruction for using Build2's package manager [\#1909](https://github.com/nlohmann/json/pull/1909) ([Klaim](https://github.com/Klaim))
- Update README.md [\#1907](https://github.com/nlohmann/json/pull/1907) ([pauljurczak](https://github.com/pauljurczak))
- Fix warning: ignoring return value [\#1871](https://github.com/nlohmann/json/pull/1871) ([sonulohani](https://github.com/sonulohani))
- docs: add central repository as conan source to readme [\#1857](https://github.com/nlohmann/json/pull/1857) ([gocarlos](https://github.com/gocarlos))
- README: Package in MSYS2 renamed to nlohmann-json [\#1853](https://github.com/nlohmann/json/pull/1853) ([podsvirov](https://github.com/podsvirov))
- Fix msvc warnings [\#1846](https://github.com/nlohmann/json/pull/1846) ([MBalszun](https://github.com/MBalszun))
- Update tests that generate CMake projects to use main project's C++ compiler [\#1844](https://github.com/nlohmann/json/pull/1844) ([Tridacnid](https://github.com/Tridacnid))
- make CMake's version config file architecture-independent [\#1746](https://github.com/nlohmann/json/pull/1746) ([uhoreg](https://github.com/uhoreg))
- Add binary type support to all binary file formats, as well as an internally represented binary type [\#1662](https://github.com/nlohmann/json/pull/1662) ([OmnipotentEntity](https://github.com/OmnipotentEntity))

## [v3.7.3](https://github.com/nlohmann/json/releases/tag/v3.7.3) (2019-11-17)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.7.2...v3.7.3)

- Project branches [\#1839](https://github.com/nlohmann/json/issues/1839)
- Quadratic destruction complexity introduced in \#1436 [\#1837](https://github.com/nlohmann/json/issues/1837)
- Trying to open a file [\#1814](https://github.com/nlohmann/json/issues/1814)
- Comparing data type with value\_t::number\_integer fails [\#1783](https://github.com/nlohmann/json/issues/1783)
- CMake version config file is architecture-dependent [\#1697](https://github.com/nlohmann/json/issues/1697)

- Fix quadratic destruction complexity [\#1838](https://github.com/nlohmann/json/pull/1838) ([nickaein](https://github.com/nickaein))

## [v3.7.2](https://github.com/nlohmann/json/releases/tag/v3.7.2) (2019-11-10)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.7.1...v3.7.2)

- Segmentation fault in destructor in case of large inputs [\#1835](https://github.com/nlohmann/json/issues/1835)
- type\_name\(\) is not consistent with type\(\) [\#1833](https://github.com/nlohmann/json/issues/1833)
- json::parse is not a member [\#1832](https://github.com/nlohmann/json/issues/1832)
- How do you deal with json\* ? [\#1829](https://github.com/nlohmann/json/issues/1829)
- Combined find\_package/add\_subdirectory not linking libraries [\#1771](https://github.com/nlohmann/json/issues/1771)
- example code for ifstream reading a json file results in no operator error [\#1766](https://github.com/nlohmann/json/issues/1766)
- Warning: unsequenced modification and access to 'range' [\#1674](https://github.com/nlohmann/json/issues/1674)
- Segmentation fault \(stack overflow\) due to unbounded recursion [\#1419](https://github.com/nlohmann/json/issues/1419)
- Stack-overflow \(OSS-Fuzz 4234\) [\#832](https://github.com/nlohmann/json/issues/832)

- Configure WhiteSource Bolt for GitHub [\#1830](https://github.com/nlohmann/json/pull/1830) ([mend-bolt-for-github[bot]](https://github.com/apps/mend-bolt-for-github))
- Prevent stackoverflow caused by recursive deconstruction [\#1436](https://github.com/nlohmann/json/pull/1436) ([nickaein](https://github.com/nickaein))

## [v3.7.1](https://github.com/nlohmann/json/releases/tag/v3.7.1) (2019-11-06)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.7.0...v3.7.1)

- std::is\_constructible is always true with tuple [\#1825](https://github.com/nlohmann/json/issues/1825)
- Can't compile from\_json\(std::valarray\<T\>\). [\#1824](https://github.com/nlohmann/json/issues/1824)
- json class should have a get\_or member function [\#1823](https://github.com/nlohmann/json/issues/1823)
- NLOHMANN\_JSON\_SERIALIZE\_ENUM macro capture's json objects by value [\#1822](https://github.com/nlohmann/json/issues/1822)
- Parse fails when number literals start with zero [\#1820](https://github.com/nlohmann/json/issues/1820)
- Weird behaviour of `contains` with `json_pointer` [\#1815](https://github.com/nlohmann/json/issues/1815)
- strange behaviour with json\_pointer and .contains\(\) [\#1811](https://github.com/nlohmann/json/issues/1811)
- Can \#1695 be re-opened? [\#1808](https://github.com/nlohmann/json/issues/1808)
- Merge two json objects [\#1807](https://github.com/nlohmann/json/issues/1807)
- std::is\_constructible\<json, std::unordered\_map\<std::string, Data\>\> when to\_json not defined [\#1805](https://github.com/nlohmann/json/issues/1805)
- Private data on parsing [\#1802](https://github.com/nlohmann/json/issues/1802)
- Capturing Line and Position when querying [\#1800](https://github.com/nlohmann/json/issues/1800)
- json error on parsing DBL\_MAX from string [\#1796](https://github.com/nlohmann/json/issues/1796)
- De/Serialisation of vector of tupple object with nested obect need Help please [\#1794](https://github.com/nlohmann/json/issues/1794)
- Output json is corrupted [\#1793](https://github.com/nlohmann/json/issues/1793)
- variable name byte sometimes used as a \#define [\#1792](https://github.com/nlohmann/json/issues/1792)
- Can't read json file [\#1791](https://github.com/nlohmann/json/issues/1791)
- Problems with special German letters [\#1789](https://github.com/nlohmann/json/issues/1789)
- Support for trailing commas [\#1787](https://github.com/nlohmann/json/issues/1787)
- json\_pointer construction bug [\#1786](https://github.com/nlohmann/json/issues/1786)
- Visual Studio 2017 warning [\#1784](https://github.com/nlohmann/json/issues/1784)
- ciso646 header become obsolete [\#1782](https://github.com/nlohmann/json/issues/1782)
- Migrate LGTM.com installation from OAuth to GitHub App [\#1781](https://github.com/nlohmann/json/issues/1781)
- JSON comparison, contains and operator& [\#1778](https://github.com/nlohmann/json/issues/1778)
- pass a json object to a class contructor adds an array around the object [\#1776](https://github.com/nlohmann/json/issues/1776)
- 'Float' number\_float\_function\_t template parameter name conflicts with C '\#define Float float' [\#1775](https://github.com/nlohmann/json/issues/1775)
- A weird building problem :-\( [\#1774](https://github.com/nlohmann/json/issues/1774)
- What is this json\_ref? [\#1772](https://github.com/nlohmann/json/issues/1772)
- Interoperability with other languages [\#1770](https://github.com/nlohmann/json/issues/1770)
- Json dump [\#1768](https://github.com/nlohmann/json/issues/1768)
- json\_pointer\<\>::back\(\) should be const [\#1764](https://github.com/nlohmann/json/issues/1764)
- How to get value from array [\#1762](https://github.com/nlohmann/json/issues/1762)
- Merge two jsons [\#1757](https://github.com/nlohmann/json/issues/1757)
- Unable to locate nlohmann\_jsonConfig.cmake [\#1755](https://github.com/nlohmann/json/issues/1755)
- json.hpp won;t compile VS2019 CLR/CLI app but does in console app [\#1754](https://github.com/nlohmann/json/issues/1754)
- \[Nested Json Objects\] Segmentation fault [\#1753](https://github.com/nlohmann/json/issues/1753)
- remove/replace assert with exceptions [\#1752](https://github.com/nlohmann/json/issues/1752)
- Add array support for update\(\) function [\#1751](https://github.com/nlohmann/json/issues/1751)
- Is there a reason the `get_to` method is defined in `include/nlohmann/json.hpp` but not in `single_include/nlohmann/json.hpp`? [\#1750](https://github.com/nlohmann/json/issues/1750)
- how to validate json object before calling dump\(\) [\#1748](https://github.com/nlohmann/json/issues/1748)
- Unable to invoke accessors on json objects in lldb [\#1745](https://github.com/nlohmann/json/issues/1745)
- Escaping string before parsing [\#1743](https://github.com/nlohmann/json/issues/1743)
- Construction in a member initializer list using curly braces is set as 'array' [\#1742](https://github.com/nlohmann/json/issues/1742)
- Read a subkey from json object [\#1740](https://github.com/nlohmann/json/issues/1740)
- Serialize vector of glm:vec2 [\#1739](https://github.com/nlohmann/json/issues/1739)
- Support nlohmann::basic\_json::value with JSON\_NOEXCEPTION [\#1738](https://github.com/nlohmann/json/issues/1738)
- how to know the parse is error [\#1737](https://github.com/nlohmann/json/issues/1737)
- How to check if a given key exists in a JSON object [\#1736](https://github.com/nlohmann/json/issues/1736)
- Allow The Colon Key-Value Delimiter To Have A Space Before It \[@ READ ONLY\] [\#1735](https://github.com/nlohmann/json/issues/1735)
- Allow Tail { "Key": "Value" } Comma \[@ READ ONLY\] [\#1734](https://github.com/nlohmann/json/issues/1734)
- No-throw json::value\(\) [\#1733](https://github.com/nlohmann/json/issues/1733)
- JsonObject.dump\(\)  [\#1732](https://github.com/nlohmann/json/issues/1732)
- basic\_json has no member "parse" [\#1731](https://github.com/nlohmann/json/issues/1731)
- Exception "type must be string, but is array" [\#1730](https://github.com/nlohmann/json/issues/1730)
- json::contains usage to find a path [\#1727](https://github.com/nlohmann/json/issues/1727)
- How to create JSON Object from my Structures of Data and Json File from that Object [\#1726](https://github.com/nlohmann/json/issues/1726)
- please provide an API to read JSON from file directly. [\#1725](https://github.com/nlohmann/json/issues/1725)
- How to modify a value stored at a key? [\#1723](https://github.com/nlohmann/json/issues/1723)
- CMake not correctly finding the configuration package for 3.7.0  [\#1721](https://github.com/nlohmann/json/issues/1721)
- name typo in the "spack package management" section of README.md [\#1720](https://github.com/nlohmann/json/issues/1720)
- How to add json to another json? [\#1718](https://github.com/nlohmann/json/issues/1718)
- json::parse\(\) ubsan regression with v3.7.0 [\#1716](https://github.com/nlohmann/json/issues/1716)
- What I am doing wrong?!? [\#1714](https://github.com/nlohmann/json/issues/1714)
- Potential memory leak detected by Valgrind [\#1713](https://github.com/nlohmann/json/issues/1713)
- json::parse is not thread safe? [\#1712](https://github.com/nlohmann/json/issues/1712)
- static analysis alarm by cppcheck [\#1711](https://github.com/nlohmann/json/issues/1711)
- The compilation time is slow [\#1710](https://github.com/nlohmann/json/issues/1710)
- not linking properly with cmake [\#1709](https://github.com/nlohmann/json/issues/1709)
- Error in dump\(\) with int64\_t minimum value [\#1708](https://github.com/nlohmann/json/issues/1708)
- Crash on trying to deserialize json string on 3ds homebrew [\#1707](https://github.com/nlohmann/json/issues/1707)
- Can't compile VS2019. 13 Errors  [\#1706](https://github.com/nlohmann/json/issues/1706)
- find an object that matches the search criteria [\#1705](https://github.com/nlohmann/json/issues/1705)
- IntelliSense goes crazy on VS2019 [\#1704](https://github.com/nlohmann/json/issues/1704)
- Installing on Ubuntu 16.04 [\#1703](https://github.com/nlohmann/json/issues/1703)
- Where is json::parse now? [\#1702](https://github.com/nlohmann/json/issues/1702)
- Forward header should't be amalgamated [\#1700](https://github.com/nlohmann/json/issues/1700)
- Json support for Cmake version 2.8.12 [\#1699](https://github.com/nlohmann/json/issues/1699)
- Intruisive scientific notation when using .dump\(\); [\#1698](https://github.com/nlohmann/json/issues/1698)
- Is there support for automatic serialization/deserialization? [\#1696](https://github.com/nlohmann/json/issues/1696)
- on MSVC dump\(\) will hard crash for larger json [\#1693](https://github.com/nlohmann/json/issues/1693)
- puzzled implicit conversions [\#1692](https://github.com/nlohmann/json/issues/1692)
- Information: My project uses this awesome library [\#1691](https://github.com/nlohmann/json/issues/1691)
- Consider listing files explicitly instead of using GLOB [\#1686](https://github.com/nlohmann/json/issues/1686)
- Failing tests on MSVC with VS2019 15.9.13 x64 [\#1685](https://github.com/nlohmann/json/issues/1685)
- Change from v2 to v3. Encoding with cp1252 [\#1680](https://github.com/nlohmann/json/issues/1680)
- How to add Fifo\_map into json using Cmake [\#1679](https://github.com/nlohmann/json/issues/1679)
- include.zip should contain meson.build [\#1672](https://github.com/nlohmann/json/issues/1672)
- \[Question\] How do I parse JSON into custom types? [\#1669](https://github.com/nlohmann/json/issues/1669)
- Binary \(0x05\) data type for BSON to JSON conversion [\#1668](https://github.com/nlohmann/json/issues/1668)
- Possible to call dump from lldb? [\#1666](https://github.com/nlohmann/json/issues/1666)
- Segmentation fault when linked with libunwind [\#1665](https://github.com/nlohmann/json/issues/1665)
- Should I include single-header after my to\_json and from\_json custom functions declaration? Why not? [\#1663](https://github.com/nlohmann/json/issues/1663)
- Errors/Warnings in VS 2019 when Including Header File [\#1659](https://github.com/nlohmann/json/issues/1659)
- Return null object from object's const operator\[\] as well. [\#1658](https://github.com/nlohmann/json/issues/1658)
- Can't stream json object in to std::basic\_stringstream\<wchar\_t\> [\#1656](https://github.com/nlohmann/json/issues/1656)
- C2440 in vs2015 cannot convert from 'initializer-list' to nlohmann::basic\_json [\#1655](https://github.com/nlohmann/json/issues/1655)
- Issues around get and pointers [\#1653](https://github.com/nlohmann/json/issues/1653)
- Non-member operator== breaks enum \(de\)serialization [\#1647](https://github.com/nlohmann/json/issues/1647)
- Valgrind: bytes in 1 blocks are definitely lost [\#1646](https://github.com/nlohmann/json/issues/1646)
- Convenient way to make 'basic\_json' accept 'QString' as an key type as well? [\#1640](https://github.com/nlohmann/json/issues/1640)
- mongodb: nan, inf [\#1599](https://github.com/nlohmann/json/issues/1599)
- Error in adl\_serializer [\#1590](https://github.com/nlohmann/json/issues/1590)
- Injecting class during serialization [\#1584](https://github.com/nlohmann/json/issues/1584)
- output\_adapter not user extensible [\#1534](https://github.com/nlohmann/json/issues/1534)
- Inclusion of nlohmann/json.hpp causes OS/ABI to change on Linux [\#1410](https://github.com/nlohmann/json/issues/1410)
- Add library versioning using inline namespaces [\#1394](https://github.com/nlohmann/json/issues/1394)
- CBOR byte string support [\#1129](https://github.com/nlohmann/json/issues/1129)
- How to deserialize array with derived objects [\#716](https://github.com/nlohmann/json/issues/716)

- Add restriction for tuple specialization of to\_json [\#1826](https://github.com/nlohmann/json/pull/1826) ([cbegue](https://github.com/cbegue))
- Fix for \#1647 [\#1821](https://github.com/nlohmann/json/pull/1821) ([AnthonyVH](https://github.com/AnthonyVH))
- Fix issue \#1805 [\#1806](https://github.com/nlohmann/json/pull/1806) ([cbegue](https://github.com/cbegue))
- Fix some spelling errors - mostly in comments & documentation. [\#1803](https://github.com/nlohmann/json/pull/1803) ([flopp](https://github.com/flopp))
- Update Hedley to v11. [\#1799](https://github.com/nlohmann/json/pull/1799) ([nemequ](https://github.com/nemequ))
- iteration\_proxy: Fix integer truncation from std::size\_t to int [\#1797](https://github.com/nlohmann/json/pull/1797) ([t-b](https://github.com/t-b))
- appveyor.yml: Add MSVC 16 2019 support [\#1780](https://github.com/nlohmann/json/pull/1780) ([t-b](https://github.com/t-b))
- test/CMakeLists.txt: Use an explicit list instead of GLOB [\#1779](https://github.com/nlohmann/json/pull/1779) ([t-b](https://github.com/t-b))
- Make json\_pointer::back const \(resolves \#1764\) [\#1769](https://github.com/nlohmann/json/pull/1769) ([chris0x44](https://github.com/chris0x44))
- did you mean 'serialization'? [\#1767](https://github.com/nlohmann/json/pull/1767) ([0xflotus](https://github.com/0xflotus))
- Allow items\(\) to be used with custom string [\#1765](https://github.com/nlohmann/json/pull/1765) ([crazyjul](https://github.com/crazyjul))
- Cppcheck fixes [\#1760](https://github.com/nlohmann/json/pull/1760) ([Xav83](https://github.com/Xav83))
- Fix and add test's for SFINAE problem [\#1741](https://github.com/nlohmann/json/pull/1741) ([tete17](https://github.com/tete17))
- Fix clang sanitizer invocation [\#1728](https://github.com/nlohmann/json/pull/1728) ([t-b](https://github.com/t-b))
- Add gcc 9 and compile with experimental C++20 support [\#1724](https://github.com/nlohmann/json/pull/1724) ([t-b](https://github.com/t-b))
- Fix int64 min issue [\#1722](https://github.com/nlohmann/json/pull/1722) ([t-b](https://github.com/t-b))
- release: add singleinclude and meson.build to include.zip [\#1694](https://github.com/nlohmann/json/pull/1694) ([eli-schwartz](https://github.com/eli-schwartz))

## [v3.7.0](https://github.com/nlohmann/json/releases/tag/v3.7.0) (2019-07-28)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.6.1...v3.7.0)

- How can I retrieve uknown strings from json file in my C++ program. [\#1684](https://github.com/nlohmann/json/issues/1684)
- contains\(\) is sometimes causing stack-based buffer overrun exceptions [\#1683](https://github.com/nlohmann/json/issues/1683)
- How to deserialize arrays  from json [\#1681](https://github.com/nlohmann/json/issues/1681)
- Compilation failed in VS2015 [\#1678](https://github.com/nlohmann/json/issues/1678)
- Why the compiled object file is so huge? [\#1677](https://github.com/nlohmann/json/issues/1677)
- From Version 2.1.1 to 3.6.1 serialize std::set [\#1676](https://github.com/nlohmann/json/issues/1676)
- Qt deprecation model halting compiltion [\#1675](https://github.com/nlohmann/json/issues/1675)
-  Build For Raspberry pi , Rapbery with new Compiler C++17 [\#1671](https://github.com/nlohmann/json/issues/1671)
- Build from Raspberry pi [\#1667](https://github.com/nlohmann/json/issues/1667)
- Can not translate map with integer key to dict string ?  [\#1664](https://github.com/nlohmann/json/issues/1664)
- Double type converts to scientific notation [\#1661](https://github.com/nlohmann/json/issues/1661)
- Missing v3.6.1 tag on master branch [\#1657](https://github.com/nlohmann/json/issues/1657)
- Support Fleese Binary Data Format [\#1654](https://github.com/nlohmann/json/issues/1654)
- Suggestion: replace alternative tokens for !, && and || with their symbols [\#1652](https://github.com/nlohmann/json/issues/1652)
- Build failure test-allocator.vcxproj [\#1651](https://github.com/nlohmann/json/issues/1651)
- How to provide function json& to\_json\(\) which is similar as 'void to\_json\(json&j, const CObject& obj\)'  ? [\#1650](https://github.com/nlohmann/json/issues/1650)
- Can't throw exception when starting file is a number [\#1649](https://github.com/nlohmann/json/issues/1649)
- to\_json / from\_json with nested type [\#1648](https://github.com/nlohmann/json/issues/1648)
- How to create a json object from a std::string, created by j.dump? [\#1645](https://github.com/nlohmann/json/issues/1645)
- Problem getting vector \(array\) of strings [\#1644](https://github.com/nlohmann/json/issues/1644)
- json.hpp compilation issue with other typedefs with same name [\#1642](https://github.com/nlohmann/json/issues/1642)
- nlohmann::adl\_serializer\<T,void\>::to\_json no matching overloaded function found [\#1641](https://github.com/nlohmann/json/issues/1641)
- overwrite adl\_serializer\<bool, void\> to change behaviour [\#1638](https://github.com/nlohmann/json/issues/1638)
- json.SelectToken\("Manufacturers.Products.Price"\); [\#1637](https://github.com/nlohmann/json/issues/1637)
- Add json type as value [\#1636](https://github.com/nlohmann/json/issues/1636)
- Unit conversion test error: conversion from 'nlohmann::json' to non-scalar type 'std::string\_view' requested [\#1634](https://github.com/nlohmann/json/issues/1634)
- nlohmann VS JsonCpp by C++17 [\#1633](https://github.com/nlohmann/json/issues/1633)
- To integrate an inline helper function that return type name as string [\#1632](https://github.com/nlohmann/json/issues/1632)
- Return JSON as reference [\#1631](https://github.com/nlohmann/json/issues/1631)
- Updating from an older version causes problems with assing a json object to a struct [\#1630](https://github.com/nlohmann/json/issues/1630)
- Can without default constructor function for user defined classes when only to\_json is needed? [\#1629](https://github.com/nlohmann/json/issues/1629)
- Compilation fails with clang 6.x-8.x in C++14 mode [\#1628](https://github.com/nlohmann/json/issues/1628)
- Treating floating point as string [\#1627](https://github.com/nlohmann/json/issues/1627)
- error parsing character å [\#1626](https://github.com/nlohmann/json/issues/1626)
- \[Help\] How to Improve Json Output Performance with Large Json Arrays [\#1624](https://github.com/nlohmann/json/issues/1624)
- Suggested link changes for reporting new issues \[blob/develop/REAME.md and blob/develop/.github/CONTRIBUTING.md\] [\#1623](https://github.com/nlohmann/json/issues/1623)
- Broken link to issue template in CONTRIBUTING.md [\#1622](https://github.com/nlohmann/json/issues/1622)
- Missing word in README.md file  [\#1621](https://github.com/nlohmann/json/issues/1621)
- Package manager instructions in README for brew is incorrect [\#1620](https://github.com/nlohmann/json/issues/1620)
- Building with Visual Studio 2019 [\#1619](https://github.com/nlohmann/json/issues/1619)
- Precedence of to\_json and builtin harmful [\#1617](https://github.com/nlohmann/json/issues/1617)
- The type json is missing from the html documentation [\#1616](https://github.com/nlohmann/json/issues/1616)
- variant is not support in Release 3.6.1? [\#1615](https://github.com/nlohmann/json/issues/1615)
- Replace assert with throw for const operator\[\] [\#1614](https://github.com/nlohmann/json/issues/1614)
- Memory Overhead is Too High \(10x or more\) [\#1613](https://github.com/nlohmann/json/issues/1613)
- program crash everytime, when other data type incomming in json stream as expected [\#1612](https://github.com/nlohmann/json/issues/1612)
- Improved Enum Support [\#1611](https://github.com/nlohmann/json/issues/1611)
- is it possible convert json object back to stl container ? [\#1610](https://github.com/nlohmann/json/issues/1610)
- Add C++17-like emplace.back\(\) for arrays. [\#1609](https://github.com/nlohmann/json/issues/1609)
- is\_nothrow\_copy\_constructible fails for json::const\_iterator on MSVC2015 x86 Debug build [\#1608](https://github.com/nlohmann/json/issues/1608)
- Reading and writing array elements [\#1607](https://github.com/nlohmann/json/issues/1607)
- Converting json::value to int [\#1605](https://github.com/nlohmann/json/issues/1605)
- I have a vector of keys and and a string of value and i want to create nested json array [\#1604](https://github.com/nlohmann/json/issues/1604)
- In compatible JSON object from nlohmann::json to nohman::json - unexpected end of input; expected '\[', '{', or a literal [\#1603](https://github.com/nlohmann/json/issues/1603)
- json parser crash if having a large number integer in message  [\#1602](https://github.com/nlohmann/json/issues/1602)
- Value method with undocumented throwing 302 exception [\#1601](https://github.com/nlohmann/json/issues/1601)
- Accessing value with json pointer adds key if not existing [\#1600](https://github.com/nlohmann/json/issues/1600)
- README.md broken link to project documentation [\#1597](https://github.com/nlohmann/json/issues/1597)
- Random Kudos: Thanks for your work on this! [\#1596](https://github.com/nlohmann/json/issues/1596)
- json::parse return value and errors [\#1595](https://github.com/nlohmann/json/issues/1595)
- initializer list constructor makes curly brace initialization fragile [\#1594](https://github.com/nlohmann/json/issues/1594)
- trying to log message for missing keyword, difference between \["foo"\] and at\("foo"\) [\#1593](https://github.com/nlohmann/json/issues/1593)
- std::string and std::wstring `to_json`  [\#1592](https://github.com/nlohmann/json/issues/1592)
- I have a C structure which I need to convert to a JSON. How do I do it? Haven't found proper examples so far. [\#1591](https://github.com/nlohmann/json/issues/1591)
- dump\_escaped possible error ? [\#1589](https://github.com/nlohmann/json/issues/1589)
- json::parse\(\) into a vector\<string\> results in unhandled exception [\#1587](https://github.com/nlohmann/json/issues/1587)
- push\_back\(\)/emplace\_back\(\) on array invalidates pointers to existing array items [\#1586](https://github.com/nlohmann/json/issues/1586)
- Getting nlohmann::detail::parse\_error on JSON generated by nlohmann::json not sure why [\#1583](https://github.com/nlohmann/json/issues/1583)
- getting error terminate called after throwing an instance of 'std::domain\_error'   what\(\):  cannot use at\(\) with string [\#1582](https://github.com/nlohmann/json/issues/1582)
- how i create json file  [\#1581](https://github.com/nlohmann/json/issues/1581)
- prevent rounding of double datatype values [\#1580](https://github.com/nlohmann/json/issues/1580)
- Documentation Container Overview Doesn't Reference Const Methods [\#1579](https://github.com/nlohmann/json/issues/1579)
- Writing an array into a nlohmann::json object [\#1578](https://github.com/nlohmann/json/issues/1578)
- compilation error when using with another library [\#1577](https://github.com/nlohmann/json/issues/1577)
- Homebrew on OSX doesn't install cmake config file [\#1576](https://github.com/nlohmann/json/issues/1576)
- JSON Parse Out of Range Error [\#1574](https://github.com/nlohmann/json/issues/1574)
- Integrating into existing CMake Project [\#1573](https://github.com/nlohmann/json/issues/1573)
- conversion to std::string failed [\#1571](https://github.com/nlohmann/json/issues/1571)
- jPtr operation does not throw [\#1569](https://github.com/nlohmann/json/issues/1569)
- How to generate dll file for this project [\#1568](https://github.com/nlohmann/json/issues/1568)
- how to pass variable data to json in c [\#1567](https://github.com/nlohmann/json/issues/1567)
- I want to achieve an upgraded function. [\#1566](https://github.com/nlohmann/json/issues/1566)
- How to determine the type of elements read from a JSON array? [\#1564](https://github.com/nlohmann/json/issues/1564)
- try\_get\_to [\#1563](https://github.com/nlohmann/json/issues/1563)
- example code  compile error [\#1562](https://github.com/nlohmann/json/issues/1562)
- How to iterate over nested json object [\#1561](https://github.com/nlohmann/json/issues/1561)
- Build Option/Separate Function to Allow to Throw on Duplicate Keys [\#1560](https://github.com/nlohmann/json/issues/1560)
- Compiler Switches -Weffc++ & -Wshadow are throwing errors [\#1558](https://github.com/nlohmann/json/issues/1558)
- warning: use of the 'nodiscard' attribute is a C++17 extension [\#1557](https://github.com/nlohmann/json/issues/1557)
- Import/Export compressed JSON files [\#1556](https://github.com/nlohmann/json/issues/1556)
- GDB renderers for json library [\#1554](https://github.com/nlohmann/json/issues/1554)
- Is it possible to construct a json string object from a binary buffer? [\#1553](https://github.com/nlohmann/json/issues/1553)
- json objects in list [\#1552](https://github.com/nlohmann/json/issues/1552)
- Matrix output [\#1550](https://github.com/nlohmann/json/issues/1550)
- Using json merge\_patch on ordered non-alphanumeric datasets [\#1549](https://github.com/nlohmann/json/issues/1549)
- Invalid parsed value for big integer [\#1548](https://github.com/nlohmann/json/issues/1548)
- Integrating with android ndk issues. [\#1547](https://github.com/nlohmann/json/issues/1547)
- add noexcept json::value\("key", default\) method variant? [\#1546](https://github.com/nlohmann/json/issues/1546)
- Thank you! 🙌 [\#1545](https://github.com/nlohmann/json/issues/1545)
- Output and input matrix [\#1544](https://github.com/nlohmann/json/issues/1544)
- Add regression tests for MSVC [\#1543](https://github.com/nlohmann/json/issues/1543)
- \[Help Needed!\] Season of Docs [\#1542](https://github.com/nlohmann/json/issues/1542)
- program still abort\(\) or exit\(\) with try catch [\#1541](https://github.com/nlohmann/json/issues/1541)
- Have a json::type\_error exception because of JSON object [\#1540](https://github.com/nlohmann/json/issues/1540)
- Quoted numbers [\#1538](https://github.com/nlohmann/json/issues/1538)
- Reading a JSON file into an object [\#1537](https://github.com/nlohmann/json/issues/1537)
- Releases 3.6.0 and 3.6.1 don't build on conda / windows [\#1536](https://github.com/nlohmann/json/issues/1536)
- \[Clang\] warning: use of the 'nodiscard' attribute is a C++17 extension \[-Wc++17-extensions\] [\#1535](https://github.com/nlohmann/json/issues/1535)
- wchar\_t/std::wstring json can be created but not accessed [\#1533](https://github.com/nlohmann/json/issues/1533)
- json stringify [\#1532](https://github.com/nlohmann/json/issues/1532)
- How can I use it from gcc on RPI [\#1528](https://github.com/nlohmann/json/issues/1528)
- std::pair treated as an array instead of key-value in `std::vector<std::pair<>>` [\#1520](https://github.com/nlohmann/json/issues/1520)
- Excessive Memory Usage for Large Json File [\#1516](https://github.com/nlohmann/json/issues/1516)
- SAX dumper [\#1512](https://github.com/nlohmann/json/issues/1512)
- Conversion to user type containing a std::vector not working with documented approach [\#1511](https://github.com/nlohmann/json/issues/1511)
- Inconsistent use of type alias. [\#1507](https://github.com/nlohmann/json/issues/1507)
- Is there a current way to represent strings as json int? [\#1503](https://github.com/nlohmann/json/issues/1503)
- Intermittent issues with loadJSON [\#1484](https://github.com/nlohmann/json/issues/1484)
- use json construct std::string [\#1462](https://github.com/nlohmann/json/issues/1462)
- JSON Creation [\#1461](https://github.com/nlohmann/json/issues/1461)
- Null bytes in files are treated like EOF [\#1095](https://github.com/nlohmann/json/issues/1095)
- Feature: to\_string\(const json& j\); [\#916](https://github.com/nlohmann/json/issues/916)

- Use GNUInstallDirs instead of hard-coded path. [\#1673](https://github.com/nlohmann/json/pull/1673) ([ghost](https://github.com/ghost))
- Package Manager: MSYS2 \(pacman\) [\#1670](https://github.com/nlohmann/json/pull/1670) ([podsvirov](https://github.com/podsvirov))
- Fix json.hpp compilation issue with other typedefs with same name \(Issue \#1642\) [\#1643](https://github.com/nlohmann/json/pull/1643) ([kevinlul](https://github.com/kevinlul))
- Add explicit conversion from json to std::string\_view in conversion unit test [\#1639](https://github.com/nlohmann/json/pull/1639) ([taylorhoward92](https://github.com/taylorhoward92))
- Minor fixes in docs [\#1625](https://github.com/nlohmann/json/pull/1625) ([nickaein](https://github.com/nickaein))
- Fix broken links to documentation [\#1598](https://github.com/nlohmann/json/pull/1598) ([nickaein](https://github.com/nickaein))
- Added to\_string and added basic tests [\#1585](https://github.com/nlohmann/json/pull/1585) ([Macr0Nerd](https://github.com/Macr0Nerd))
- Regression tests for MSVC [\#1570](https://github.com/nlohmann/json/pull/1570) ([nickaein](https://github.com/nickaein))
- Fix/1511 [\#1555](https://github.com/nlohmann/json/pull/1555) ([theodelrieu](https://github.com/theodelrieu))
- Remove C++17 extension warning from clang; \#1535 [\#1551](https://github.com/nlohmann/json/pull/1551) ([heavywatal](https://github.com/heavywatal))
- moved from Catch to doctest for unit tests [\#1439](https://github.com/nlohmann/json/pull/1439) ([onqtam](https://github.com/onqtam))

## [v3.6.1](https://github.com/nlohmann/json/releases/tag/v3.6.1) (2019-03-20)

[Full Changelog](https://github.com/nlohmann/json/compare/3.6.1...v3.6.1)

## [3.6.1](https://github.com/nlohmann/json/releases/tag/3.6.1) (2019-03-20)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.6.0...3.6.1)

- Failed to build with \<Windows.h\> [\#1531](https://github.com/nlohmann/json/issues/1531)
- Compiling 3.6.0 with GCC \> 7, array vs std::array \#590 is back [\#1530](https://github.com/nlohmann/json/issues/1530)
- 3.6.0: warning: missing initializer for member 'std::array\<char, 9ul\>::\_M\_elems' \[-Wmissing-field-initializers\] [\#1527](https://github.com/nlohmann/json/issues/1527)
- unable to parse json  [\#1525](https://github.com/nlohmann/json/issues/1525)

## [v3.6.0](https://github.com/nlohmann/json/releases/tag/v3.6.0) (2019-03-19)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.5.0...v3.6.0)

- How can I turn a string of a json array into a json array? [\#1526](https://github.com/nlohmann/json/issues/1526)
- Minor: missing a std:: namespace tag [\#1521](https://github.com/nlohmann/json/issues/1521)
- how to precision to four decimal for double when use to\_json [\#1519](https://github.com/nlohmann/json/issues/1519)
- error parse [\#1518](https://github.com/nlohmann/json/issues/1518)
- Compile error: template argument deduction/substitution failed [\#1515](https://github.com/nlohmann/json/issues/1515)
- std::complex type [\#1510](https://github.com/nlohmann/json/issues/1510)
- CBOR byte string support [\#1509](https://github.com/nlohmann/json/issues/1509)
- Compilation error getting a std::pair\<\> on latest VS 2017 compiler [\#1506](https://github.com/nlohmann/json/issues/1506)
- "Integration" section of documentation needs update? [\#1505](https://github.com/nlohmann/json/issues/1505)
- Json object from string from a TCP socket [\#1504](https://github.com/nlohmann/json/issues/1504)
- MSVC warning C4946 \("reinterpret\_cast used between related classes"\) compiling json.hpp [\#1502](https://github.com/nlohmann/json/issues/1502)
- How to programmatically fill an n-th dimensional JSON object? [\#1501](https://github.com/nlohmann/json/issues/1501)
- Error compiling with clang and `JSON_NOEXCEPTION`: need to include `cstdlib` [\#1500](https://github.com/nlohmann/json/issues/1500)
- The code compiles unsuccessfully with android-ndk-r10e [\#1499](https://github.com/nlohmann/json/issues/1499)
- Cmake 3.1 in develop, when is it likely to make it into a stable release? [\#1498](https://github.com/nlohmann/json/issues/1498)
- Some Help please object inside array [\#1494](https://github.com/nlohmann/json/issues/1494)
- How to get data into vector of user-defined type from a Json object [\#1493](https://github.com/nlohmann/json/issues/1493)
- how to find subelement  without loop [\#1490](https://github.com/nlohmann/json/issues/1490)
- json to std::map [\#1487](https://github.com/nlohmann/json/issues/1487)
- Type in README.md [\#1486](https://github.com/nlohmann/json/issues/1486)
- Error in parsing and reading msgpack-lite  [\#1485](https://github.com/nlohmann/json/issues/1485)
- Compiling issues with libc 2.12 [\#1483](https://github.com/nlohmann/json/issues/1483)
- How do I use reference or pointer binding values? [\#1482](https://github.com/nlohmann/json/issues/1482)
- Compilation fails in MSVC with the Microsoft Language Extensions disabled [\#1481](https://github.com/nlohmann/json/issues/1481)
- Functional visit [\#1480](https://github.com/nlohmann/json/issues/1480)
- \[Question\] Unescaped dump [\#1479](https://github.com/nlohmann/json/issues/1479)
- Some Help please [\#1478](https://github.com/nlohmann/json/issues/1478)
- Global variables are stored within the JSON file, how do I declare them as global variables when I read them out in my C++ program?  [\#1476](https://github.com/nlohmann/json/issues/1476)
- Unable to modify one of the values within the JSON file, and save it  [\#1475](https://github.com/nlohmann/json/issues/1475)
- Documentation of parse function has two identical @pre causes [\#1473](https://github.com/nlohmann/json/issues/1473)
- GCC 9.0 build failure [\#1472](https://github.com/nlohmann/json/issues/1472)
- Can we have an `exists()` method? [\#1471](https://github.com/nlohmann/json/issues/1471)
- How to parse multi object json from file? [\#1470](https://github.com/nlohmann/json/issues/1470)
- How to returns the name of the upper object? [\#1467](https://github.com/nlohmann/json/issues/1467)
- Error: "tuple\_size" has already been declared in the current scope [\#1466](https://github.com/nlohmann/json/issues/1466)
- Checking keys of two jsons against eachother [\#1465](https://github.com/nlohmann/json/issues/1465)
- Disable installation when used as meson subproject [\#1463](https://github.com/nlohmann/json/issues/1463)
- Unpack list of integers to a std::vector\<int\> [\#1460](https://github.com/nlohmann/json/issues/1460)
- Implement DRY definition of JSON representation of a c++ class  [\#1459](https://github.com/nlohmann/json/issues/1459)
- json.exception.type\_error.305 with GCC 4.9 when using C++ {} initializer [\#1458](https://github.com/nlohmann/json/issues/1458)
- API to convert an "uninitialized" json into an empty object or empty array [\#1456](https://github.com/nlohmann/json/issues/1456)
- How to parse a vector of objects with const attributes [\#1453](https://github.com/nlohmann/json/issues/1453)
- NLOHMANN\_JSON\_SERIALIZE\_ENUM potentially requires duplicate definitions [\#1450](https://github.com/nlohmann/json/issues/1450)
- Question about making json object from file directory [\#1449](https://github.com/nlohmann/json/issues/1449)
- .get\(\) throws error if used with userdefined structs in unordered\_map [\#1448](https://github.com/nlohmann/json/issues/1448)
- Integer Overflow \(OSS-Fuzz 12506\) [\#1447](https://github.com/nlohmann/json/issues/1447)
- If a string has too many invalid UTF-8 characters, json::dump attempts to index an array out of bounds. [\#1445](https://github.com/nlohmann/json/issues/1445)
- Setting values of .JSON file [\#1444](https://github.com/nlohmann/json/issues/1444)
- alias object\_t::key\_type in basic\_json [\#1442](https://github.com/nlohmann/json/issues/1442)
- Latest Ubuntu package is 2.1.1 [\#1438](https://github.com/nlohmann/json/issues/1438)
- lexer.hpp\(1363\)  '\_snprintf': is not a  member | Visualstudio 2017 [\#1437](https://github.com/nlohmann/json/issues/1437)
- Static method invites inadvertent logic error. [\#1433](https://github.com/nlohmann/json/issues/1433)
- EOS compilation produces "fatal error: 'nlohmann/json.hpp' file not found" [\#1432](https://github.com/nlohmann/json/issues/1432)
- Support for bad commas [\#1429](https://github.com/nlohmann/json/issues/1429)
- Please have one base exception class for all json exceptions [\#1427](https://github.com/nlohmann/json/issues/1427)
- Compilation warning: 'tuple\_size' defined as a class template here but previously declared as a struct template [\#1426](https://github.com/nlohmann/json/issues/1426)
- Which version can be used with GCC 4.8.2 ? [\#1424](https://github.com/nlohmann/json/issues/1424)
- Ignore nullptr values on constructing json object from a container [\#1422](https://github.com/nlohmann/json/issues/1422)
- Support for custom float precision via unquoted strings [\#1421](https://github.com/nlohmann/json/issues/1421)
- It is possible to call `json::find` with a json\_pointer as argument. This causes runtime UB/crash. [\#1418](https://github.com/nlohmann/json/issues/1418)
- Dump throwing exception [\#1416](https://github.com/nlohmann/json/issues/1416)
- Build error  [\#1415](https://github.com/nlohmann/json/issues/1415)
- Append version to include.zip [\#1412](https://github.com/nlohmann/json/issues/1412)
- error C2039: '\_snprintf': is not a member of 'std' - Windows [\#1408](https://github.com/nlohmann/json/issues/1408)
- Deserializing to vector [\#1407](https://github.com/nlohmann/json/issues/1407)
- Efficient way to set a `json` object as value into another `json` key [\#1406](https://github.com/nlohmann/json/issues/1406)
- Document return value of parse\(\) when allow\_exceptions == false and parsing fails [\#1405](https://github.com/nlohmann/json/issues/1405)
- Unexpected behaviour with structured binding [\#1404](https://github.com/nlohmann/json/issues/1404)
- Which native types does get\<type\>\(\) allow? [\#1403](https://github.com/nlohmann/json/issues/1403)
- Add something like Json::StaticString [\#1402](https://github.com/nlohmann/json/issues/1402)
- -Wmismatched-tags in 3.5.0? [\#1401](https://github.com/nlohmann/json/issues/1401)
- Coverity Scan reports an UNCAUGHT\_EXCEPT issue [\#1400](https://github.com/nlohmann/json/issues/1400)
- fff [\#1399](https://github.com/nlohmann/json/issues/1399)
- sorry this is not an issue, just a Question, How to change a key value in a file and save it ? [\#1398](https://github.com/nlohmann/json/issues/1398)
- appveyor x64 builds appear to be using Win32 toolset [\#1374](https://github.com/nlohmann/json/issues/1374)
- Serializing/Deserializing  a Class containing a vector of itself [\#1373](https://github.com/nlohmann/json/issues/1373)
- Retrieving array elements. [\#1369](https://github.com/nlohmann/json/issues/1369)
- Deserialize [\#1366](https://github.com/nlohmann/json/issues/1366)
- call of overloaded for push\_back and operator+= is ambiguous [\#1352](https://github.com/nlohmann/json/issues/1352)
- got an error and cann't figure it out [\#1351](https://github.com/nlohmann/json/issues/1351)
- Improve number-to-string conversion [\#1334](https://github.com/nlohmann/json/issues/1334)
- Implicit type conversion error on MSVC [\#1333](https://github.com/nlohmann/json/issues/1333)
- NuGet Package [\#1132](https://github.com/nlohmann/json/issues/1132)

- Change macros to numeric\_limits [\#1514](https://github.com/nlohmann/json/pull/1514) ([naszta](https://github.com/naszta))
- fix GCC 7.1.1 - 7.2.1 on CentOS [\#1496](https://github.com/nlohmann/json/pull/1496) ([lieff](https://github.com/lieff))
- Update Buckaroo instructions in README.md [\#1495](https://github.com/nlohmann/json/pull/1495) ([njlr](https://github.com/njlr))
- Fix gcc9 build error test/src/unit-allocator.cpp \(Issue \#1472\) [\#1492](https://github.com/nlohmann/json/pull/1492) ([stac47](https://github.com/stac47))
- Fix typo in README.md [\#1491](https://github.com/nlohmann/json/pull/1491) ([nickaein](https://github.com/nickaein))
- Do proper endian conversions [\#1489](https://github.com/nlohmann/json/pull/1489) ([andreas-schwab](https://github.com/andreas-schwab))
- Fix documentation [\#1477](https://github.com/nlohmann/json/pull/1477) ([nickaein](https://github.com/nickaein))
- Implement contains\(\) member function [\#1474](https://github.com/nlohmann/json/pull/1474) ([nickaein](https://github.com/nickaein))
- Add operator/= and operator/ to construct a JSON pointer by appending two JSON pointers [\#1469](https://github.com/nlohmann/json/pull/1469) ([garethsb](https://github.com/garethsb))
- Disable Clang -Wmismatched-tags warning on tuple\_size / tuple\_element [\#1468](https://github.com/nlohmann/json/pull/1468) ([past-due](https://github.com/past-due))
- Disable installation when used as meson subproject. \#1463 [\#1464](https://github.com/nlohmann/json/pull/1464) ([elvisoric](https://github.com/elvisoric))
- docs: README typo [\#1455](https://github.com/nlohmann/json/pull/1455) ([wythe](https://github.com/wythe))
- remove extra semicolon from readme [\#1451](https://github.com/nlohmann/json/pull/1451) ([Afforix](https://github.com/Afforix))
- attempt to fix \#1445, flush buffer in serializer::dump\_escaped in UTF8\_REJECT case. [\#1446](https://github.com/nlohmann/json/pull/1446) ([scinart](https://github.com/scinart))
- Use C++11 features supported by CMake 3.1. [\#1441](https://github.com/nlohmann/json/pull/1441) ([iwanders](https://github.com/iwanders))
- :rotating\_light: fixed unused variable warning [\#1435](https://github.com/nlohmann/json/pull/1435) ([pboettch](https://github.com/pboettch))
- allow push\_back\(\) and pop\_back\(\) calls on json\_pointer [\#1434](https://github.com/nlohmann/json/pull/1434) ([pboettch](https://github.com/pboettch))
- Add instructions about using nlohmann/json with the conda package manager [\#1430](https://github.com/nlohmann/json/pull/1430) ([nicoddemus](https://github.com/nicoddemus))
- Updated year in README.md [\#1425](https://github.com/nlohmann/json/pull/1425) ([jef](https://github.com/jef))
- Fixed broken links in the README file [\#1423](https://github.com/nlohmann/json/pull/1423) ([skypjack](https://github.com/skypjack))
- Fixed broken links in the README file [\#1420](https://github.com/nlohmann/json/pull/1420) ([skypjack](https://github.com/skypjack))
- docs: typo in README [\#1417](https://github.com/nlohmann/json/pull/1417) ([wythe](https://github.com/wythe))
- Fix x64 target platform for appveyor [\#1414](https://github.com/nlohmann/json/pull/1414) ([nickaein](https://github.com/nickaein))
- Improve dump\_integer performance [\#1411](https://github.com/nlohmann/json/pull/1411) ([nickaein](https://github.com/nickaein))
- buildsystem: relax requirement on cmake version [\#1409](https://github.com/nlohmann/json/pull/1409) ([yann-morin-1998](https://github.com/yann-morin-1998))
- CMake: Optional Install if Embedded [\#1330](https://github.com/nlohmann/json/pull/1330) ([ax3l](https://github.com/ax3l))

## [v3.5.0](https://github.com/nlohmann/json/releases/tag/v3.5.0) (2018-12-21)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.4.0...v3.5.0)

- Copyconstructor inserts original into array with single element [\#1397](https://github.com/nlohmann/json/issues/1397)
- Get value without explicit typecasting [\#1395](https://github.com/nlohmann/json/issues/1395)
- Big file parsing [\#1393](https://github.com/nlohmann/json/issues/1393)
- Adding Structured Binding Support [\#1388](https://github.com/nlohmann/json/issues/1388)
- map\<json::value\_t, string\> exhibits unexpected behavior [\#1387](https://github.com/nlohmann/json/issues/1387)
- Error Code Return [\#1386](https://github.com/nlohmann/json/issues/1386)
- using unordered\_map as object type [\#1385](https://github.com/nlohmann/json/issues/1385)
- float precision [\#1384](https://github.com/nlohmann/json/issues/1384)
- \[json.exception.type\_error.316\] invalid UTF-8 byte at index 1: 0xC3 [\#1383](https://github.com/nlohmann/json/issues/1383)
- Inconsistent Constructor \(GCC vs. Clang\) [\#1381](https://github.com/nlohmann/json/issues/1381)
- \#define or || [\#1379](https://github.com/nlohmann/json/issues/1379)
- How to iterate inside the values ? [\#1377](https://github.com/nlohmann/json/issues/1377)
- items\(\) unable to get the elements [\#1375](https://github.com/nlohmann/json/issues/1375)
- conversion json to std::map doesn't work for types \<int, double\>  [\#1372](https://github.com/nlohmann/json/issues/1372)
- A minor issue in the build instructions [\#1371](https://github.com/nlohmann/json/issues/1371)
- Using this library without stream ? [\#1370](https://github.com/nlohmann/json/issues/1370)
- Writing and reading BSON data [\#1368](https://github.com/nlohmann/json/issues/1368)
- Retrieving array elements from object type iterator. [\#1367](https://github.com/nlohmann/json/issues/1367)
- json::dump\(\) silently crashes if items contain accented letters [\#1365](https://github.com/nlohmann/json/issues/1365)
- warnings in MSVC \(2015\) in 3.4.0 related to bool... [\#1364](https://github.com/nlohmann/json/issues/1364)
- Cant compile with -C++17 and beyond compiler options [\#1362](https://github.com/nlohmann/json/issues/1362)
- json to concrete type conversion through reference or pointer fails [\#1361](https://github.com/nlohmann/json/issues/1361)
- the first attributes of JSON string is misplaced  [\#1360](https://github.com/nlohmann/json/issues/1360)
- Copy-construct using initializer-list converts objects to arrays [\#1359](https://github.com/nlohmann/json/issues/1359)
- About value\(key, default\_value\) and operator\[\]\(key\)  [\#1358](https://github.com/nlohmann/json/issues/1358)
- Problem with printing json response object [\#1356](https://github.com/nlohmann/json/issues/1356)
- Serializing pointer segfaults [\#1355](https://github.com/nlohmann/json/issues/1355)
- Read `long long int` data as a number. [\#1354](https://github.com/nlohmann/json/issues/1354)
- eclipse oxygen in ubuntu get\<size\_t\> is ambiguous  [\#1353](https://github.com/nlohmann/json/issues/1353)
- Can't build on Visual Studio 2017 v15.8.9 [\#1350](https://github.com/nlohmann/json/issues/1350)
- cannot parse from string? [\#1349](https://github.com/nlohmann/json/issues/1349)
- Error: out\_of\_range [\#1348](https://github.com/nlohmann/json/issues/1348)
- expansion pattern 'CompatibleObjectType' contains no argument packs, with CUDA 10 [\#1347](https://github.com/nlohmann/json/issues/1347)
- Unable to update a value for a nested\(multi-level\) json file [\#1344](https://github.com/nlohmann/json/issues/1344)
- Fails to compile when std::iterator\_traits is not SFINAE friendly. [\#1341](https://github.com/nlohmann/json/issues/1341)
- EOF flag not set on exhausted input streams. [\#1340](https://github.com/nlohmann/json/issues/1340)
- Shadowed Member in merge\_patch [\#1339](https://github.com/nlohmann/json/issues/1339)
- Periods/literal dots in keys? [\#1338](https://github.com/nlohmann/json/issues/1338)
- Protect macro expansion of commonly defined macros [\#1337](https://github.com/nlohmann/json/issues/1337)
- How to validate an input before parsing? [\#1336](https://github.com/nlohmann/json/issues/1336)
- Non-verifying dump\(\) alternative for debugging/logging needed [\#1335](https://github.com/nlohmann/json/issues/1335)
- Json Libarary is not responding for me in c++ [\#1332](https://github.com/nlohmann/json/issues/1332)
- Question - how to find an object in an array [\#1331](https://github.com/nlohmann/json/issues/1331)
- Nesting additional data in json object [\#1328](https://github.com/nlohmann/json/issues/1328)
- can to\_json\(\) be defined inside a class? [\#1324](https://github.com/nlohmann/json/issues/1324)
- CodeBlocks IDE can't find `json.hpp` header [\#1318](https://github.com/nlohmann/json/issues/1318)
- Change json\_pointer to provide an iterator begin/end/etc, don't use vectors, and also enable string\_view [\#1312](https://github.com/nlohmann/json/issues/1312)
- Xcode - adding it to library  [\#1300](https://github.com/nlohmann/json/issues/1300)
- unicode: accept char16\_t, char32\_t sequences [\#1298](https://github.com/nlohmann/json/issues/1298)
- unicode: char16\_t\* is compiler error, but char16\_t\[\] is accepted [\#1297](https://github.com/nlohmann/json/issues/1297)
- Dockerfile Project Help Needed [\#1296](https://github.com/nlohmann/json/issues/1296)
- Comparisons between large unsigned and negative signed integers [\#1295](https://github.com/nlohmann/json/issues/1295)
- CMake alias to `nlohmann::json` [\#1291](https://github.com/nlohmann/json/issues/1291)
- Release zips without tests [\#1285](https://github.com/nlohmann/json/issues/1285)
- separate object\_t::key\_type from basic\_json::key\_type, and use an allocator which returns object\_t::key\_type [\#1274](https://github.com/nlohmann/json/issues/1274)
- Is there a nice way to associate external values with json elements? [\#1256](https://github.com/nlohmann/json/issues/1256)
- Delete by json\_pointer [\#1248](https://github.com/nlohmann/json/issues/1248)
- Expose lexer, as a StAX parser [\#1219](https://github.com/nlohmann/json/issues/1219)
- Subclassing json\(\) & error on recursive load [\#1201](https://github.com/nlohmann/json/issues/1201)
- Check value for existence by json\_pointer [\#1194](https://github.com/nlohmann/json/issues/1194)

- Feature/add file input adapter [\#1392](https://github.com/nlohmann/json/pull/1392) ([dumarjo](https://github.com/dumarjo))
-  Added Support for Structured Bindings  [\#1391](https://github.com/nlohmann/json/pull/1391) ([pratikpc](https://github.com/pratikpc))
- Link to issue \#958 broken [\#1382](https://github.com/nlohmann/json/pull/1382) ([kjpus](https://github.com/kjpus))
- readme: fix typo [\#1380](https://github.com/nlohmann/json/pull/1380) ([manu-chroma](https://github.com/manu-chroma))
- recommend using explicit from JSON conversions [\#1363](https://github.com/nlohmann/json/pull/1363) ([theodelrieu](https://github.com/theodelrieu))
- Fix merge\_patch shadow warning [\#1346](https://github.com/nlohmann/json/pull/1346) ([ax3l](https://github.com/ax3l))
- Allow installation via Meson [\#1345](https://github.com/nlohmann/json/pull/1345) ([mpoquet](https://github.com/mpoquet))
- Set eofbit on exhausted input stream. [\#1343](https://github.com/nlohmann/json/pull/1343) ([mefyl](https://github.com/mefyl))
- Add a SFINAE friendly iterator\_traits and use that instead. [\#1342](https://github.com/nlohmann/json/pull/1342) ([dgavedissian](https://github.com/dgavedissian))
- Fix EOL Whitespaces & CMake Spelling [\#1329](https://github.com/nlohmann/json/pull/1329) ([ax3l](https://github.com/ax3l))

## [v3.4.0](https://github.com/nlohmann/json/releases/tag/v3.4.0) (2018-10-30)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.3.0...v3.4.0)

- Big uint64\_t values are serialized wrong [\#1327](https://github.com/nlohmann/json/issues/1327)
- \[Question\] Efficient check for equivalency? [\#1325](https://github.com/nlohmann/json/issues/1325)
- Can't use ifstream and .clear\(\) [\#1321](https://github.com/nlohmann/json/issues/1321)
- \[Warning\] -Wparentheses on line 555 on single\_include [\#1319](https://github.com/nlohmann/json/issues/1319)
- Compilation error using at and find with enum struct [\#1316](https://github.com/nlohmann/json/issues/1316)
- Parsing JSON from a web address [\#1311](https://github.com/nlohmann/json/issues/1311)
- How to convert JSON to Struct with embeded subject [\#1310](https://github.com/nlohmann/json/issues/1310)
- Null safety/coalescing function? [\#1309](https://github.com/nlohmann/json/issues/1309)
- Building fails using single include file: json.hpp [\#1308](https://github.com/nlohmann/json/issues/1308)
- json::parse\(std::string\) Exception inside packaged Lib [\#1306](https://github.com/nlohmann/json/issues/1306)
- Problem in Dockerfile with installation of library [\#1304](https://github.com/nlohmann/json/issues/1304)
- compile error in from\_json converting to container with std::pair [\#1299](https://github.com/nlohmann/json/issues/1299)
- Json that I am trying to parse, and I am lost Structure Array below top level [\#1293](https://github.com/nlohmann/json/issues/1293)
- Serializing std::variant causes stack overflow [\#1292](https://github.com/nlohmann/json/issues/1292)
- How do I go about customising from\_json to support \_\_int128\_t/\_\_uint128\_t? [\#1290](https://github.com/nlohmann/json/issues/1290)
- merge\_patch: inconsistent behaviour merging empty sub-object [\#1289](https://github.com/nlohmann/json/issues/1289)
- Buffer over/underrun using UBJson? [\#1288](https://github.com/nlohmann/json/issues/1288)
- Enable the latest C++ standard with Visual Studio [\#1287](https://github.com/nlohmann/json/issues/1287)
- truncation of constant value in to\_cbor\(\) [\#1286](https://github.com/nlohmann/json/issues/1286)
- eosio.wasmsdk error [\#1284](https://github.com/nlohmann/json/issues/1284)
- use the same interface for writing arrays and non-arrays [\#1283](https://github.com/nlohmann/json/issues/1283)
- How to read json file with optional  entries and entries with different types [\#1281](https://github.com/nlohmann/json/issues/1281)
- merge result not as espected [\#1279](https://github.com/nlohmann/json/issues/1279)
- how to get only "name" from below json [\#1278](https://github.com/nlohmann/json/issues/1278)
- syntax error  on right json string [\#1276](https://github.com/nlohmann/json/issues/1276)
- Parsing JSON Array where members have no key, using custom types [\#1267](https://github.com/nlohmann/json/issues/1267)
- I get a json exception periodically from json::parse for the same json  [\#1263](https://github.com/nlohmann/json/issues/1263)
- GCC 8.2.1. Compilation error: invalid conversion from... [\#1246](https://github.com/nlohmann/json/issues/1246)
- BSON support [\#1244](https://github.com/nlohmann/json/issues/1244)
- enum to json mapping [\#1208](https://github.com/nlohmann/json/issues/1208)
- Soften the landing when dumping non-UTF8 strings \(type\_error.316 exception\) [\#1198](https://github.com/nlohmann/json/issues/1198)

- Add macro to define enum/JSON mapping [\#1323](https://github.com/nlohmann/json/pull/1323) ([nlohmann](https://github.com/nlohmann))
- Add BSON support [\#1320](https://github.com/nlohmann/json/pull/1320) ([nlohmann](https://github.com/nlohmann))
- Properly convert constants to CharType [\#1315](https://github.com/nlohmann/json/pull/1315) ([nlohmann](https://github.com/nlohmann))
- Allow to set error handler for decoding errors [\#1314](https://github.com/nlohmann/json/pull/1314) ([nlohmann](https://github.com/nlohmann))
- Add Meson related info to README [\#1305](https://github.com/nlohmann/json/pull/1305) ([koponomarenko](https://github.com/koponomarenko))
- Improve diagnostic messages for binary formats [\#1303](https://github.com/nlohmann/json/pull/1303) ([nlohmann](https://github.com/nlohmann))
- add new is\_constructible\_\* traits used in from\_json [\#1301](https://github.com/nlohmann/json/pull/1301) ([theodelrieu](https://github.com/theodelrieu))
- add constraints for variadic json\_ref constructors [\#1294](https://github.com/nlohmann/json/pull/1294) ([theodelrieu](https://github.com/theodelrieu))
- Improve diagnostic messages [\#1282](https://github.com/nlohmann/json/pull/1282) ([nlohmann](https://github.com/nlohmann))
- Removed linter warnings [\#1280](https://github.com/nlohmann/json/pull/1280) ([nlohmann](https://github.com/nlohmann))
- Thirdparty benchmark: Fix Clang detection. [\#1277](https://github.com/nlohmann/json/pull/1277) ([Lord-Kamina](https://github.com/Lord-Kamina))

## [v3.3.0](https://github.com/nlohmann/json/releases/tag/v3.3.0) (2018-10-05)

[Full Changelog](https://github.com/nlohmann/json/compare/3.3.0...v3.3.0)

## [3.3.0](https://github.com/nlohmann/json/releases/tag/3.3.0) (2018-10-05)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.2.0...3.3.0)

- When key is not found print the key name into error too [\#1273](https://github.com/nlohmann/json/issues/1273)
- Visual Studio 2017 15.8.5 "conditional expression is constant" warning on Line 1851 in json.hpp [\#1268](https://github.com/nlohmann/json/issues/1268)
- how can we get this working on WSL? [\#1264](https://github.com/nlohmann/json/issues/1264)
- Help needed [\#1259](https://github.com/nlohmann/json/issues/1259)
- A way to get to a JSON values "key" [\#1258](https://github.com/nlohmann/json/issues/1258)
- While compiling got 76 errors [\#1255](https://github.com/nlohmann/json/issues/1255)
- Two blackslashes on json output file [\#1253](https://github.com/nlohmann/json/issues/1253)
- Including nlohmann the badwrong way. [\#1250](https://github.com/nlohmann/json/issues/1250)
- how to build with clang? [\#1247](https://github.com/nlohmann/json/issues/1247)
- Cmake target\_link\_libraries unable to find nlohmann\_json since version 3.2.0 [\#1243](https://github.com/nlohmann/json/issues/1243)
- \[Question\] Access to end\(\) iterator reference [\#1242](https://github.com/nlohmann/json/issues/1242)
- Parsing different json format [\#1241](https://github.com/nlohmann/json/issues/1241)
- Parsing Multiple JSON Files [\#1240](https://github.com/nlohmann/json/issues/1240)
- Doesn't compile under C++17 [\#1239](https://github.com/nlohmann/json/issues/1239)
- Conversion operator for nlohmann::json is not SFINAE friendly [\#1237](https://github.com/nlohmann/json/issues/1237)
- Custom deserialization of number\_float\_t [\#1236](https://github.com/nlohmann/json/issues/1236)
- deprecated-declarations warnings when compiling tests with GCC 8.2.1. [\#1233](https://github.com/nlohmann/json/issues/1233)
- Incomplete type with json\_fwd.hpp [\#1232](https://github.com/nlohmann/json/issues/1232)
- Parse Error [\#1229](https://github.com/nlohmann/json/issues/1229)
- json::get function with argument [\#1227](https://github.com/nlohmann/json/issues/1227)
- questions regarding from\_json [\#1226](https://github.com/nlohmann/json/issues/1226)
- Lambda in unevaluated context [\#1225](https://github.com/nlohmann/json/issues/1225)
- NLohmann doesn't compile when enabling strict warning policies [\#1224](https://github.com/nlohmann/json/issues/1224)
- Creating array of objects [\#1223](https://github.com/nlohmann/json/issues/1223)
- Somewhat unhelpful error message "cannot use operator\[\] with object" [\#1220](https://github.com/nlohmann/json/issues/1220)
- single\_include json.hpp [\#1218](https://github.com/nlohmann/json/issues/1218)
- Maps with enum class keys which are convertible to JSON strings should be converted to JSON dictionaries [\#1217](https://github.com/nlohmann/json/issues/1217)
- Adding JSON Array to the Array  [\#1216](https://github.com/nlohmann/json/issues/1216)
- Best way to output a vector of a given type to json [\#1215](https://github.com/nlohmann/json/issues/1215)
- compiler warning: double definition of macro JSON\_INTERNAL\_CATCH  [\#1213](https://github.com/nlohmann/json/issues/1213)
- Compilation error when using MOCK\_METHOD1 from GMock and nlohmann::json [\#1212](https://github.com/nlohmann/json/issues/1212)
- Issues parsing a previously encoded binary \(non-UTF8\) string. [\#1211](https://github.com/nlohmann/json/issues/1211)
- Yet another ordering question: char \* and parse\(\) [\#1209](https://github.com/nlohmann/json/issues/1209)
- Error using gcc 8.1.0 on Ubuntu 14.04 [\#1207](https://github.com/nlohmann/json/issues/1207)
- "type must be string, but is " std::string\(j.type\_name\(\)  [\#1206](https://github.com/nlohmann/json/issues/1206)
- Returning empty json object from a function of type const json& ? [\#1205](https://github.com/nlohmann/json/issues/1205)
- VS2017 compiler suggests using constexpr if [\#1204](https://github.com/nlohmann/json/issues/1204)
- Template instatiation error on compiling [\#1203](https://github.com/nlohmann/json/issues/1203)
- BUG - json dump field with unicode -\> array of ints \(instead of string\) [\#1197](https://github.com/nlohmann/json/issues/1197)
- Compile error using Code::Blocks // mingw-w64 GCC 8.1.0 - "Incomplete Type" [\#1193](https://github.com/nlohmann/json/issues/1193)
- SEGFAULT on arm target  [\#1190](https://github.com/nlohmann/json/issues/1190)
- Compiler crash with old Clang [\#1179](https://github.com/nlohmann/json/issues/1179)
- Custom Precision on floating point numbers [\#1170](https://github.com/nlohmann/json/issues/1170)
- Can we have a json\_view class like std::string\_view? [\#1158](https://github.com/nlohmann/json/issues/1158)
- improve error handling [\#1152](https://github.com/nlohmann/json/issues/1152)
- We should remove static\_asserts [\#960](https://github.com/nlohmann/json/issues/960)

- Fix warning C4127: conditional expression is constant [\#1272](https://github.com/nlohmann/json/pull/1272) ([antonioborondo](https://github.com/antonioborondo))
- Turn off additional deprecation warnings for GCC. [\#1271](https://github.com/nlohmann/json/pull/1271) ([chuckatkins](https://github.com/chuckatkins))
- docs: Add additional CMake documentation [\#1270](https://github.com/nlohmann/json/pull/1270) ([chuckatkins](https://github.com/chuckatkins))
- unit-testsuites.cpp: fix hangup if file not found [\#1262](https://github.com/nlohmann/json/pull/1262) ([knilch0r](https://github.com/knilch0r))
- Fix broken cmake imported target alias [\#1260](https://github.com/nlohmann/json/pull/1260) ([chuckatkins](https://github.com/chuckatkins))
- GCC 48 [\#1257](https://github.com/nlohmann/json/pull/1257) ([henryiii](https://github.com/henryiii))
- Add version and license to meson.build [\#1252](https://github.com/nlohmann/json/pull/1252) ([koponomarenko](https://github.com/koponomarenko))
- \#1179 Reordered the code. It seems to stop clang 3.4.2 in RHEL 7 from crash… [\#1249](https://github.com/nlohmann/json/pull/1249) ([LEgregius](https://github.com/LEgregius))
- Use a version check to provide backwards comatible CMake imported target names [\#1245](https://github.com/nlohmann/json/pull/1245) ([chuckatkins](https://github.com/chuckatkins))
- Fix issue \#1237 [\#1238](https://github.com/nlohmann/json/pull/1238) ([theodelrieu](https://github.com/theodelrieu))
- Add a get overload taking a parameter. [\#1231](https://github.com/nlohmann/json/pull/1231) ([theodelrieu](https://github.com/theodelrieu))
- Move lambda out of unevaluated context [\#1230](https://github.com/nlohmann/json/pull/1230) ([mandreyel](https://github.com/mandreyel))
- Remove static asserts [\#1228](https://github.com/nlohmann/json/pull/1228) ([theodelrieu](https://github.com/theodelrieu))
- Better error 305 [\#1221](https://github.com/nlohmann/json/pull/1221) ([rivertam](https://github.com/rivertam))
- Fix \#1213 [\#1214](https://github.com/nlohmann/json/pull/1214) ([simnalamburt](https://github.com/simnalamburt))
- Export package to allow builds without installing [\#1202](https://github.com/nlohmann/json/pull/1202) ([dennisfischer](https://github.com/dennisfischer))

## [v3.2.0](https://github.com/nlohmann/json/releases/tag/v3.2.0) (2018-08-20)

[Full Changelog](https://github.com/nlohmann/json/compare/3.2.0...v3.2.0)

## [3.2.0](https://github.com/nlohmann/json/releases/tag/3.2.0) (2018-08-20)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.1.2...3.2.0)

- Am I doing this wrong? Getting an empty string [\#1199](https://github.com/nlohmann/json/issues/1199)
- Incompatible Pointer Type [\#1196](https://github.com/nlohmann/json/issues/1196)
- json.exception.type\_error.316 [\#1195](https://github.com/nlohmann/json/issues/1195)
- Strange warnings in Code::Blocks 17.12, GNU GCC [\#1192](https://github.com/nlohmann/json/issues/1192)
- \[Question\] Current place in code to change floating point resolution [\#1191](https://github.com/nlohmann/json/issues/1191)
- Add key name when throwing type error [\#1189](https://github.com/nlohmann/json/issues/1189)
- Not able to include in visual studio code? [\#1188](https://github.com/nlohmann/json/issues/1188)
- Get an Index or row number of an element [\#1186](https://github.com/nlohmann/json/issues/1186)
- Difference between `merge_patch` and `update` [\#1183](https://github.com/nlohmann/json/issues/1183)
- Is there a way to get an element from a JSON without throwing an exception on failure? [\#1182](https://github.com/nlohmann/json/issues/1182)
- to\_string? [\#1181](https://github.com/nlohmann/json/issues/1181)
- How to cache a json object's pointer into a map? [\#1180](https://github.com/nlohmann/json/issues/1180)
- Can this library work within a Qt project for Android using Qt Creator? [\#1178](https://github.com/nlohmann/json/issues/1178)
- How to get all keys of one object? [\#1177](https://github.com/nlohmann/json/issues/1177)
- How can I only parse the first level and get the value as string? [\#1175](https://github.com/nlohmann/json/issues/1175)
- I have a query regarding nlohmann::basic\_json::basic\_json [\#1174](https://github.com/nlohmann/json/issues/1174)
- unordered\_map with vectors won't convert to json? [\#1173](https://github.com/nlohmann/json/issues/1173)
- return json objects from functions [\#1172](https://github.com/nlohmann/json/issues/1172)
- Problem when exporting to CBOR [\#1171](https://github.com/nlohmann/json/issues/1171)
- Roundtripping null to nullptr does not work [\#1169](https://github.com/nlohmann/json/issues/1169)
- MSVC fails to compile std::swap specialization for nlohmann::json [\#1168](https://github.com/nlohmann/json/issues/1168)
- Unexpected behaviour of is\_null - Part II [\#1167](https://github.com/nlohmann/json/issues/1167)
- Floating point imprecision [\#1166](https://github.com/nlohmann/json/issues/1166)
- Combine json objects into one? [\#1165](https://github.com/nlohmann/json/issues/1165)
- Is there any way to know if the object has changed? [\#1164](https://github.com/nlohmann/json/issues/1164)
- Value throws on null string [\#1163](https://github.com/nlohmann/json/issues/1163)
- Weird template issue in large project [\#1162](https://github.com/nlohmann/json/issues/1162)
- \_json returns a different result vs ::parse [\#1161](https://github.com/nlohmann/json/issues/1161)
- Showing difference between two json objects [\#1160](https://github.com/nlohmann/json/issues/1160)
- no instance of overloaded function "std::swap" matches the specified type	 [\#1159](https://github.com/nlohmann/json/issues/1159)
- resize\(...\)? [\#1157](https://github.com/nlohmann/json/issues/1157)
- Issue with struct nested in class' to\_json [\#1155](https://github.com/nlohmann/json/issues/1155)
- Deserialize std::map with std::nan [\#1154](https://github.com/nlohmann/json/issues/1154)
- Parse throwing errors [\#1149](https://github.com/nlohmann/json/issues/1149)
- cocoapod integration [\#1148](https://github.com/nlohmann/json/issues/1148)
- wstring parsing [\#1147](https://github.com/nlohmann/json/issues/1147)
- Is it possible to dump a two-dimensional array to "\[\[null\],\[1,2,3\]\]"? [\#1146](https://github.com/nlohmann/json/issues/1146)
- Want to write a class member variable and a struct variable \( this structure is inside the class\) to the json file [\#1145](https://github.com/nlohmann/json/issues/1145)
- Does json support converting an instance of a struct into json string? [\#1143](https://github.com/nlohmann/json/issues/1143)
- \#Most efficient way to search for child parameters \(recursive find?\) [\#1141](https://github.com/nlohmann/json/issues/1141)
-  could not find to\_json\(\) method in T's namespace [\#1140](https://github.com/nlohmann/json/issues/1140)
- chars get treated as JSON numbers not JSON strings [\#1139](https://github.com/nlohmann/json/issues/1139)
- How do I count number of objects in array? [\#1137](https://github.com/nlohmann/json/issues/1137)
- Serializing a vector of classes? [\#1136](https://github.com/nlohmann/json/issues/1136)
- Compile error. Unable convert form nullptr to nullptr&& [\#1135](https://github.com/nlohmann/json/issues/1135)
- std::unordered\_map in struct, serialization [\#1133](https://github.com/nlohmann/json/issues/1133)
- dump\(\) can't handle umlauts [\#1131](https://github.com/nlohmann/json/issues/1131)
- Add a way to get a key reference from the iterator [\#1127](https://github.com/nlohmann/json/issues/1127)
- can't not parse "\\“ string [\#1123](https://github.com/nlohmann/json/issues/1123)
-  if  json file  contain Internationalization   chars   ,  get  exception [\#1122](https://github.com/nlohmann/json/issues/1122)
- How to use a json::iterator dereferenced value in code? [\#1120](https://github.com/nlohmann/json/issues/1120)
- Disable implicit conversions from json to std::initializer\_list\<T\> for any T [\#1118](https://github.com/nlohmann/json/issues/1118)
- Implicit conversions to complex types can lead to surprising and confusing errors [\#1116](https://github.com/nlohmann/json/issues/1116)
- How can I write from\_json for a complex datatype that is not default constructible? [\#1115](https://github.com/nlohmann/json/issues/1115)
- Compile error in VS2015 when compiling unit-conversions.cpp [\#1114](https://github.com/nlohmann/json/issues/1114)
- ADL Serializer for std::any / boost::any [\#1113](https://github.com/nlohmann/json/issues/1113)
- Unexpected behaviour of is\_null [\#1112](https://github.com/nlohmann/json/issues/1112)
- How to resolve  " undefined reference to `std::\_\_throw\_bad\_cast\(\)'" [\#1111](https://github.com/nlohmann/json/issues/1111)
- cannot compile on ubuntu 18.04 and 16.04 [\#1110](https://github.com/nlohmann/json/issues/1110)
- JSON representation for floating point values has too many digits [\#1109](https://github.com/nlohmann/json/issues/1109)
- Not working for classes containing "\_declspec\(dllimport\)" in their declaration [\#1108](https://github.com/nlohmann/json/issues/1108)
- Get keys from json object [\#1107](https://github.com/nlohmann/json/issues/1107)
- Cannot deserialize types using std::ratio [\#1105](https://github.com/nlohmann/json/issues/1105)
-  i want to learn json [\#1104](https://github.com/nlohmann/json/issues/1104)
- Type checking during compile [\#1103](https://github.com/nlohmann/json/issues/1103)
- Iterate through sub items [\#1102](https://github.com/nlohmann/json/issues/1102)
- cppcheck failing for version 3.1.2 [\#1101](https://github.com/nlohmann/json/issues/1101)
- Deserializing std::map [\#1100](https://github.com/nlohmann/json/issues/1100)
- accessing key by reference [\#1098](https://github.com/nlohmann/json/issues/1098)
- clang 3.8.0 croaks while trying to compile with debug symbols [\#1097](https://github.com/nlohmann/json/issues/1097)
- Serialize a list of class objects with json [\#1096](https://github.com/nlohmann/json/issues/1096)
- Small question [\#1094](https://github.com/nlohmann/json/issues/1094)
- Upgrading to 3.x: to\_/from\_json with enum class [\#1093](https://github.com/nlohmann/json/issues/1093)
- Q: few questions about json construction [\#1092](https://github.com/nlohmann/json/issues/1092)
- general crayCC compilation failure [\#1091](https://github.com/nlohmann/json/issues/1091)
- Merge Patch clears original data [\#1090](https://github.com/nlohmann/json/issues/1090)
- \[Question\] how to use nlohmann/json in c++? [\#1088](https://github.com/nlohmann/json/issues/1088)
- C++17 decomposition declaration support [\#1087](https://github.com/nlohmann/json/issues/1087)
- \[Question\] Access multi-level json objects [\#1086](https://github.com/nlohmann/json/issues/1086)
- Serializing vector [\#1085](https://github.com/nlohmann/json/issues/1085)
- update nested value in multi hierarchy json object [\#1084](https://github.com/nlohmann/json/issues/1084)
- Overriding default values? [\#1083](https://github.com/nlohmann/json/issues/1083)
- detail namespace collision with Cereal? [\#1082](https://github.com/nlohmann/json/issues/1082)
- Error using json.dump\(\); [\#1081](https://github.com/nlohmann/json/issues/1081)
- Consuming TCP Stream [\#1080](https://github.com/nlohmann/json/issues/1080)
- Compilation error with strong typed enums in map in combination with namespaces [\#1079](https://github.com/nlohmann/json/issues/1079)
- cassert error [\#1076](https://github.com/nlohmann/json/issues/1076)
- Valid json data not being parsed [\#1075](https://github.com/nlohmann/json/issues/1075)
- Feature request :: Better testing for key existance without try/catch [\#1074](https://github.com/nlohmann/json/issues/1074)
- Hi, I have input like a.b.c and want to convert it to \"a\"{\"b\": \"c\"} form. Any suggestions how do I do this? Thanks. [\#1073](https://github.com/nlohmann/json/issues/1073)
- ADL deserializer not picked up for non default-constructible type [\#1072](https://github.com/nlohmann/json/issues/1072)
- Deserializing std::array doesn't compiler \(no insert\(\)\) [\#1071](https://github.com/nlohmann/json/issues/1071)
- Serializing OpenCV Mat problem [\#1070](https://github.com/nlohmann/json/issues/1070)
- Compilation error with ICPC compiler [\#1068](https://github.com/nlohmann/json/issues/1068)
- Not existing value, crash [\#1065](https://github.com/nlohmann/json/issues/1065)
- cyryllic symbols [\#1064](https://github.com/nlohmann/json/issues/1064)
- newbie usage question [\#1063](https://github.com/nlohmann/json/issues/1063)
- Trying j\["strTest"\] = "%A" produces "strTest": "-0X1.CCCCCCCCCCCCCP+205" [\#1062](https://github.com/nlohmann/json/issues/1062)
- convert json value to std::string??? [\#1061](https://github.com/nlohmann/json/issues/1061)
- Commented out test cases, should they be removed? [\#1060](https://github.com/nlohmann/json/issues/1060)
- different behaviour between clang and gcc with braced initialization [\#1059](https://github.com/nlohmann/json/issues/1059)
- json array:  initialize with prescribed size and `resize` method. [\#1057](https://github.com/nlohmann/json/issues/1057)
- Is it possible to use exceptions istead of assertions? [\#1056](https://github.com/nlohmann/json/issues/1056)
- when using assign operator in with json object a static assertion fails.. [\#1055](https://github.com/nlohmann/json/issues/1055)
- Iterate over leafs of a JSON data structure: enrich the JSON pointer API [\#1054](https://github.com/nlohmann/json/issues/1054)
- \[Feature request\] Access by path [\#1053](https://github.com/nlohmann/json/issues/1053)
- document that implicit js -\> primitive conversion does not work for std::string::value\_type and why [\#1052](https://github.com/nlohmann/json/issues/1052)
- error: ‘BasicJsonType’ in namespace ‘::’ does not name a type [\#1051](https://github.com/nlohmann/json/issues/1051)
- Destructor is called when filling object through assignement [\#1050](https://github.com/nlohmann/json/issues/1050)
- Is this thing thread safe for reads? [\#1049](https://github.com/nlohmann/json/issues/1049)
- clang-tidy: Call to virtual function during construction  [\#1046](https://github.com/nlohmann/json/issues/1046)
- Using STL algorithms with JSON containers with expected results? [\#1045](https://github.com/nlohmann/json/issues/1045)
- Usage with gtest/gmock not working as expected [\#1044](https://github.com/nlohmann/json/issues/1044)
- Consequences of from\_json / to\_json being in namespace of data struct. [\#1042](https://github.com/nlohmann/json/issues/1042)
- const\_reference operator\[\]\(const typename object\_t::key\_type& key\) const throw instead of assert [\#1039](https://github.com/nlohmann/json/issues/1039)
- Trying to retrieve data from nested objects [\#1038](https://github.com/nlohmann/json/issues/1038)
- Direct download link for json\_fwd.hpp? [\#1037](https://github.com/nlohmann/json/issues/1037)
- I know the library supports UTF-8, but failed to dump the value [\#1036](https://github.com/nlohmann/json/issues/1036)
- Putting a Vec3-like vector into a json object [\#1035](https://github.com/nlohmann/json/issues/1035)
- Ternary operator crash [\#1034](https://github.com/nlohmann/json/issues/1034)
- Issued with Clion Inspection Resolution since 2018.1 [\#1033](https://github.com/nlohmann/json/issues/1033)
- Some testcases fail and one never finishes [\#1032](https://github.com/nlohmann/json/issues/1032)
- Can this class work with wchar\_t / std::wstring? [\#1031](https://github.com/nlohmann/json/issues/1031)
- Makefile: Valgrind flags have no effect [\#1030](https://github.com/nlohmann/json/issues/1030)
- 「==」 Should be 「\>」 [\#1029](https://github.com/nlohmann/json/issues/1029)
- HOCON reader? [\#1027](https://github.com/nlohmann/json/issues/1027)
- add json string in previous string?? [\#1025](https://github.com/nlohmann/json/issues/1025)
- RFC: fluent parsing interface [\#1023](https://github.com/nlohmann/json/issues/1023)
- Does it support chinese character? [\#1022](https://github.com/nlohmann/json/issues/1022)
- to/from\_msgpack only works with standard typization [\#1021](https://github.com/nlohmann/json/issues/1021)
- Build failure using latest clang and GCC compilers [\#1020](https://github.com/nlohmann/json/issues/1020)
- can two json objects be concatenated? [\#1019](https://github.com/nlohmann/json/issues/1019)
- Erase by integer index [\#1018](https://github.com/nlohmann/json/issues/1018)
- Function find overload taking a json\_pointer [\#1017](https://github.com/nlohmann/json/issues/1017)
- I think should implement an parser function [\#1016](https://github.com/nlohmann/json/issues/1016)
- Readme gif [\#1015](https://github.com/nlohmann/json/issues/1015)
- Python bindings [\#1014](https://github.com/nlohmann/json/issues/1014)
- how to add two json string in single object?? [\#1012](https://github.com/nlohmann/json/issues/1012)
- how to serialize class Object \(convert data in object into json\)?? [\#1011](https://github.com/nlohmann/json/issues/1011)
- Enable forward declaration of json by making json a class instead of a using declaration [\#997](https://github.com/nlohmann/json/issues/997)
- compilation error while using intel c++ compiler 2018 [\#994](https://github.com/nlohmann/json/issues/994)
- How to create a json variable? [\#990](https://github.com/nlohmann/json/issues/990)
- istream \>\> json  --- 1st character skipped in stream [\#976](https://github.com/nlohmann/json/issues/976)
- Add a SAX parser [\#971](https://github.com/nlohmann/json/issues/971)
- How to solve large json file? [\#927](https://github.com/nlohmann/json/issues/927)
- json\_pointer public push\_back, pop\_back [\#837](https://github.com/nlohmann/json/issues/837)
- Using input\_adapter in a slightly unexpected way [\#834](https://github.com/nlohmann/json/issues/834)

- Fix -Wno-sometimes-uninitialized by initializing "result" in parse\_sax [\#1200](https://github.com/nlohmann/json/pull/1200) ([thyu](https://github.com/thyu))
- \[RFC\] Introduce a new macro function: JSON\_INTERNAL\_CATCH [\#1187](https://github.com/nlohmann/json/pull/1187) ([simnalamburt](https://github.com/simnalamburt))
- Fix unit tests that were silently skipped or crashed \(depending on the compiler\) [\#1176](https://github.com/nlohmann/json/pull/1176) ([grembo](https://github.com/grembo))
- Refactor/no virtual sax [\#1153](https://github.com/nlohmann/json/pull/1153) ([theodelrieu](https://github.com/theodelrieu))
- Fixed compiler error in VS 2015 for debug mode [\#1151](https://github.com/nlohmann/json/pull/1151) ([sonulohani](https://github.com/sonulohani))
- Fix links to cppreference named requirements \(formerly concepts\) [\#1144](https://github.com/nlohmann/json/pull/1144) ([jrakow](https://github.com/jrakow))
- meson: fix include directory [\#1142](https://github.com/nlohmann/json/pull/1142) ([jrakow](https://github.com/jrakow))
- Feature/unordered map conversion [\#1138](https://github.com/nlohmann/json/pull/1138) ([theodelrieu](https://github.com/theodelrieu))
- fixed compile error for \#1045 [\#1134](https://github.com/nlohmann/json/pull/1134) ([Daniel599](https://github.com/Daniel599))
-  test \(non\)equality for alt\_string implementation  [\#1130](https://github.com/nlohmann/json/pull/1130) ([agrianius](https://github.com/agrianius))
- remove stringstream dependency [\#1117](https://github.com/nlohmann/json/pull/1117) ([TinyTinni](https://github.com/TinyTinni))
- Provide a from\_json overload for std::map [\#1089](https://github.com/nlohmann/json/pull/1089) ([theodelrieu](https://github.com/theodelrieu))
- fix typo in README [\#1078](https://github.com/nlohmann/json/pull/1078) ([martin-mfg](https://github.com/martin-mfg))
- Fix typo [\#1058](https://github.com/nlohmann/json/pull/1058) ([dns13](https://github.com/dns13))
- Misc cmake packaging enhancements [\#1048](https://github.com/nlohmann/json/pull/1048) ([chuckatkins](https://github.com/chuckatkins))
- Fixed incorrect LLVM version number in README [\#1047](https://github.com/nlohmann/json/pull/1047) ([jammehcow](https://github.com/jammehcow))
- Fix trivial typo in comment. [\#1043](https://github.com/nlohmann/json/pull/1043) ([coryan](https://github.com/coryan))
- Package Manager: Spack [\#1041](https://github.com/nlohmann/json/pull/1041) ([ax3l](https://github.com/ax3l))
- CMake: 3.8+ is Sufficient [\#1040](https://github.com/nlohmann/json/pull/1040) ([ax3l](https://github.com/ax3l))
- Added support for string\_view in C++17 [\#1028](https://github.com/nlohmann/json/pull/1028) ([gracicot](https://github.com/gracicot))
- Added public target\_compile\_features for auto and constexpr [\#1026](https://github.com/nlohmann/json/pull/1026) ([ktonon](https://github.com/ktonon))

## [v3.1.2](https://github.com/nlohmann/json/releases/tag/v3.1.2) (2018-03-14)

[Full Changelog](https://github.com/nlohmann/json/compare/3.1.2...v3.1.2)

## [3.1.2](https://github.com/nlohmann/json/releases/tag/3.1.2) (2018-03-14)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.1.1...3.1.2)

- STL containers are always serialized to a nested array like \[\[1,2,3\]\] [\#1013](https://github.com/nlohmann/json/issues/1013)
- The library doesn't want to insert an unordered\_map [\#1010](https://github.com/nlohmann/json/issues/1010)
- Convert Json to uint8\_t [\#1008](https://github.com/nlohmann/json/issues/1008)
- How to compare two JSON objects? [\#1007](https://github.com/nlohmann/json/issues/1007)
- Syntax checking [\#1003](https://github.com/nlohmann/json/issues/1003)
- more than one operator '=' matches these operands [\#1002](https://github.com/nlohmann/json/issues/1002)
- How to check if key existed  [\#1000](https://github.com/nlohmann/json/issues/1000)
- nlohmann::json::parse exhaust memory in go binding [\#999](https://github.com/nlohmann/json/issues/999)
- Range-based iteration over a non-array object [\#998](https://github.com/nlohmann/json/issues/998)
- get\<T\> for types that are not default constructible [\#996](https://github.com/nlohmann/json/issues/996)
- Prevent Null values to appear in .dump\(\) [\#995](https://github.com/nlohmann/json/issues/995)
- number parsing [\#993](https://github.com/nlohmann/json/issues/993)
- C2664 \(C++/CLR\) cannot convert 'nullptr' to 'nullptr &&' [\#987](https://github.com/nlohmann/json/issues/987)
- Uniform initialization from another json object differs between gcc and clang. [\#985](https://github.com/nlohmann/json/issues/985)
- Problem with adding the lib as a submodule [\#983](https://github.com/nlohmann/json/issues/983)
- UTF-8/Unicode error [\#982](https://github.com/nlohmann/json/issues/982)
- "forcing MSVC stacktrace to show which T we're talking about." error [\#980](https://github.com/nlohmann/json/issues/980)
- reverse order of serialization  [\#979](https://github.com/nlohmann/json/issues/979)
- Assigning between different json types [\#977](https://github.com/nlohmann/json/issues/977)
- Support serialisation of `unique_ptr<>` and `shared_ptr<>` [\#975](https://github.com/nlohmann/json/issues/975)
- Unexpected end of input \(not same as one before\) [\#974](https://github.com/nlohmann/json/issues/974)
- Segfault on direct initializing json object [\#973](https://github.com/nlohmann/json/issues/973)
- Segmentation fault on G++ when trying to assign json string literal to custom json type. [\#972](https://github.com/nlohmann/json/issues/972)
- os\_defines.h:44:19: error: missing binary operator before token "\(" [\#970](https://github.com/nlohmann/json/issues/970)
- Passing an iteration object by reference to a function [\#967](https://github.com/nlohmann/json/issues/967)
- Json and fmt::lib's format\_arg\(\) [\#964](https://github.com/nlohmann/json/issues/964)

- Allowing for user-defined string type in lexer/parser [\#1009](https://github.com/nlohmann/json/pull/1009) ([nlohmann](https://github.com/nlohmann))
- dump to alternative string type, as defined in basic\_json template [\#1006](https://github.com/nlohmann/json/pull/1006) ([agrianius](https://github.com/agrianius))
- Fix memory leak during parser callback [\#1001](https://github.com/nlohmann/json/pull/1001) ([nlohmann](https://github.com/nlohmann))
- fixed misprinted condition detected by PVS Studio. [\#992](https://github.com/nlohmann/json/pull/992) ([bogemic](https://github.com/bogemic))
- Fix/basic json conversion [\#986](https://github.com/nlohmann/json/pull/986) ([theodelrieu](https://github.com/theodelrieu))
- Make integration section concise [\#981](https://github.com/nlohmann/json/pull/981) ([wla80](https://github.com/wla80))

## [v3.1.1](https://github.com/nlohmann/json/releases/tag/v3.1.1) (2018-02-13)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.1.0...v3.1.1)

- Updation of child object isn't reflected in parent Object [\#968](https://github.com/nlohmann/json/issues/968)
- How to add user defined C++ path to sublime text  [\#966](https://github.com/nlohmann/json/issues/966)
- fast number parsing [\#965](https://github.com/nlohmann/json/issues/965)
- With non-unique keys, later stored entries are not taken into account anymore [\#963](https://github.com/nlohmann/json/issues/963)
- Timeout \(OSS-Fuzz 6034\) [\#962](https://github.com/nlohmann/json/issues/962)
- Incorrect parsing of indefinite length CBOR strings. [\#961](https://github.com/nlohmann/json/issues/961)
- Reload a json file at runtime without emptying my std::ifstream [\#959](https://github.com/nlohmann/json/issues/959)
- Split headers should be part of the release [\#956](https://github.com/nlohmann/json/issues/956)
- Coveralls shows no coverage data [\#953](https://github.com/nlohmann/json/issues/953)
- Feature request: Implicit conversion to bool [\#951](https://github.com/nlohmann/json/issues/951)
- converting json to vector of type with templated constructor [\#924](https://github.com/nlohmann/json/issues/924)
- No structured bindings support? [\#901](https://github.com/nlohmann/json/issues/901)
- \[Request\] Macro generating from\_json\(\) and to\_json\(\) [\#895](https://github.com/nlohmann/json/issues/895)
- basic\_json::value throws exception instead of returning default value [\#871](https://github.com/nlohmann/json/issues/871)

- Fix constraints on from\_json\(CompatibleArrayType\) [\#969](https://github.com/nlohmann/json/pull/969) ([theodelrieu](https://github.com/theodelrieu))
- Make coveralls watch the include folder [\#957](https://github.com/nlohmann/json/pull/957) ([theodelrieu](https://github.com/theodelrieu))
- Fix links in README.md [\#955](https://github.com/nlohmann/json/pull/955) ([patrikhuber](https://github.com/patrikhuber))
- Add a note about installing the library with cget [\#954](https://github.com/nlohmann/json/pull/954) ([pfultz2](https://github.com/pfultz2))

## [v3.1.0](https://github.com/nlohmann/json/releases/tag/v3.1.0) (2018-02-01)

[Full Changelog](https://github.com/nlohmann/json/compare/3.1.0...v3.1.0)

## [3.1.0](https://github.com/nlohmann/json/releases/tag/3.1.0) (2018-02-01)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.0.1...3.1.0)

- I have a proposal [\#949](https://github.com/nlohmann/json/issues/949)
- VERSION define\(s\) [\#948](https://github.com/nlohmann/json/issues/948)
- v3.0.1 compile error in icc 16.0.4 [\#947](https://github.com/nlohmann/json/issues/947)
- Use in VS2017 15.5.5 [\#946](https://github.com/nlohmann/json/issues/946)
- Process for reporting Security Bugs? [\#945](https://github.com/nlohmann/json/issues/945)
- Please expose a NLOHMANN\_JSON\_VERSION macro [\#943](https://github.com/nlohmann/json/issues/943)
- Change header include directory to nlohmann/json [\#942](https://github.com/nlohmann/json/issues/942)
- string\_type in binary\_reader [\#941](https://github.com/nlohmann/json/issues/941)
- compile error with clang 5.0 -std=c++1z and no string\_view [\#939](https://github.com/nlohmann/json/issues/939)
- Allow overriding JSON\_THROW to something else than abort\(\) [\#938](https://github.com/nlohmann/json/issues/938)
- Handle invalid string in Json file [\#937](https://github.com/nlohmann/json/issues/937)
- Unused variable 'kMinExp' [\#935](https://github.com/nlohmann/json/issues/935)
- yytext is already defined [\#933](https://github.com/nlohmann/json/issues/933)
- Equality operator fails [\#931](https://github.com/nlohmann/json/issues/931)
- use in visual studio 2015 [\#929](https://github.com/nlohmann/json/issues/929)
- Relative includes of json\_fwd.hpp in detail/meta.hpp. \[Develop branch\] [\#928](https://github.com/nlohmann/json/issues/928)
- GCC 7.x issue [\#926](https://github.com/nlohmann/json/issues/926)
- json\_fwd.hpp not installed [\#923](https://github.com/nlohmann/json/issues/923)
- Use Google Benchmarks [\#921](https://github.com/nlohmann/json/issues/921)
- Move class json\_pointer to separate file [\#920](https://github.com/nlohmann/json/issues/920)
- Unable to locate 'to\_json\(\)' and 'from\_json\(\)' methods in the same namespace [\#917](https://github.com/nlohmann/json/issues/917)
- \[answered\]Read key1 from .value example  [\#914](https://github.com/nlohmann/json/issues/914)
- Don't use `define private public` in test files [\#913](https://github.com/nlohmann/json/issues/913)
- value\(\) template argument type deduction [\#912](https://github.com/nlohmann/json/issues/912)
- Installation path is incorrect [\#910](https://github.com/nlohmann/json/issues/910)
- H [\#909](https://github.com/nlohmann/json/issues/909)
- Build failure using clang 5 [\#908](https://github.com/nlohmann/json/issues/908)
- Amalgate [\#907](https://github.com/nlohmann/json/issues/907)
- Update documentation and tests wrt. split headers [\#906](https://github.com/nlohmann/json/issues/906)
- Lib not working on ubuntu 16.04 [\#905](https://github.com/nlohmann/json/issues/905)
- Problem when writing to file. [\#904](https://github.com/nlohmann/json/issues/904)
- C2864 error when compiling with VS2015 and VS 2017 [\#903](https://github.com/nlohmann/json/issues/903)
- \[json.exception.type\_error.304\] cannot use at\(\) with object [\#902](https://github.com/nlohmann/json/issues/902)
- How do I forward nlohmann::json declaration? [\#899](https://github.com/nlohmann/json/issues/899)
- How to effectively store binary data? [\#898](https://github.com/nlohmann/json/issues/898)
- How to get the length of a JSON string without retrieving its std::string? [\#897](https://github.com/nlohmann/json/issues/897)
- Regression Tests Failure using "ctest" [\#887](https://github.com/nlohmann/json/issues/887)
- Discuss: add JSON Merge Patch \(RFC 7396\)? [\#877](https://github.com/nlohmann/json/issues/877)
- Discuss: replace static "iterator\_wrapper" function with "items" member function [\#874](https://github.com/nlohmann/json/issues/874)
- Make optional user-data available in from\_json [\#864](https://github.com/nlohmann/json/issues/864)
- Casting to std::string not working in VS2015 [\#861](https://github.com/nlohmann/json/issues/861)
- Sequential reading of JSON arrays [\#851](https://github.com/nlohmann/json/issues/851)
- Idea: Handle Multimaps Better [\#816](https://github.com/nlohmann/json/issues/816)
- Floating point rounding [\#777](https://github.com/nlohmann/json/issues/777)
- Loss of precision when serializing \<double\> [\#360](https://github.com/nlohmann/json/issues/360)

- Templatize std::string in binary\_reader \#941 [\#950](https://github.com/nlohmann/json/pull/950) ([kaidokert](https://github.com/kaidokert))
- fix cmake install directory \(for real this time\) [\#944](https://github.com/nlohmann/json/pull/944) ([theodelrieu](https://github.com/theodelrieu))
- Allow overriding THROW/CATCH/TRY macros with no-exceptions \#938 [\#940](https://github.com/nlohmann/json/pull/940) ([kaidokert](https://github.com/kaidokert))
- Removed compiler warning about unused variable 'kMinExp' [\#936](https://github.com/nlohmann/json/pull/936) ([zerodefect](https://github.com/zerodefect))
- Fix a typo in README.md [\#930](https://github.com/nlohmann/json/pull/930) ([Pipeliner](https://github.com/Pipeliner))
- Howto installation of json\_fwd.hpp \(fixes \#923\) [\#925](https://github.com/nlohmann/json/pull/925) ([zerodefect](https://github.com/zerodefect))
- fix sfinae on basic\_json UDT constructor [\#919](https://github.com/nlohmann/json/pull/919) ([theodelrieu](https://github.com/theodelrieu))
- Floating-point formatting [\#915](https://github.com/nlohmann/json/pull/915) ([abolz](https://github.com/abolz))
- Fix/cmake install [\#911](https://github.com/nlohmann/json/pull/911) ([theodelrieu](https://github.com/theodelrieu))
- fix link to the documentation of the emplace function [\#900](https://github.com/nlohmann/json/pull/900) ([Dobiasd](https://github.com/Dobiasd))
- JSON Merge Patch \(RFC 7396\) [\#876](https://github.com/nlohmann/json/pull/876) ([nlohmann](https://github.com/nlohmann))
- Refactor/split it [\#700](https://github.com/nlohmann/json/pull/700) ([theodelrieu](https://github.com/theodelrieu))

## [v3.0.1](https://github.com/nlohmann/json/releases/tag/v3.0.1) (2017-12-29)

[Full Changelog](https://github.com/nlohmann/json/compare/3.0.1...v3.0.1)

## [3.0.1](https://github.com/nlohmann/json/releases/tag/3.0.1) (2017-12-29)

[Full Changelog](https://github.com/nlohmann/json/compare/v3.0.0...3.0.1)

- Problem parsing array to global vector [\#896](https://github.com/nlohmann/json/issues/896)
- Invalid RFC6902 copy operation succeeds [\#894](https://github.com/nlohmann/json/issues/894)
- How to rename a key during looping? [\#893](https://github.com/nlohmann/json/issues/893)
- clang++-6.0 \(6.0.0-svn321357-1\) warning [\#892](https://github.com/nlohmann/json/issues/892)
- Make json.hpp aware of the modules TS? [\#891](https://github.com/nlohmann/json/issues/891)
- All enum values not handled in switch cases. \( -Wswitch-enum \) [\#889](https://github.com/nlohmann/json/issues/889)
- JSON Pointer resolve failure resulting in incorrect exception code [\#888](https://github.com/nlohmann/json/issues/888)
- Unexpected nested arrays from std::vector [\#886](https://github.com/nlohmann/json/issues/886)
- erase multiple elements from a json object [\#884](https://github.com/nlohmann/json/issues/884)
- Container function overview in Doxygen is not updated [\#883](https://github.com/nlohmann/json/issues/883)
- How to use this for binary file uploads [\#881](https://github.com/nlohmann/json/issues/881)
- Allow setting JSON\_BuildTests=OFF from parent CMakeLists.txt [\#846](https://github.com/nlohmann/json/issues/846)
- Unit test fails for local-independent str-to-num [\#845](https://github.com/nlohmann/json/issues/845)
- Another idea about type support [\#774](https://github.com/nlohmann/json/issues/774)

- Includes CTest module/adds BUILD\_TESTING option [\#885](https://github.com/nlohmann/json/pull/885) ([TinyTinni](https://github.com/TinyTinni))
- Fix MSVC warning C4819 [\#882](https://github.com/nlohmann/json/pull/882) ([erengy](https://github.com/erengy))
- Merge branch 'develop' into coverity\_scan [\#880](https://github.com/nlohmann/json/pull/880) ([nlohmann](https://github.com/nlohmann))
- :wrench: Fix up a few more effc++ items [\#858](https://github.com/nlohmann/json/pull/858) ([mattismyname](https://github.com/mattismyname))

## [v3.0.0](https://github.com/nlohmann/json/releases/tag/v3.0.0) (2017-12-17)

[Full Changelog](https://github.com/nlohmann/json/compare/3.0.0...v3.0.0)

## [3.0.0](https://github.com/nlohmann/json/releases/tag/3.0.0) (2017-12-17)

[Full Changelog](https://github.com/nlohmann/json/compare/v2.1.1...3.0.0)

- unicode strings [\#878](https://github.com/nlohmann/json/issues/878)
- Visual Studio 2017 15.5 C++17 std::allocator deprecations [\#872](https://github.com/nlohmann/json/issues/872)
- Typo "excpetion" [\#869](https://github.com/nlohmann/json/issues/869)
- Explicit array example in README.md incorrect [\#867](https://github.com/nlohmann/json/issues/867)
- why don't you release this from Feb. ? [\#865](https://github.com/nlohmann/json/issues/865)
- json::parse throws std::invalid\_argument when processing string generated by json::dump\(\) [\#863](https://github.com/nlohmann/json/issues/863)
- code analysis: potential bug? [\#859](https://github.com/nlohmann/json/issues/859)
- MSVC2017, 15.5 new issues.   [\#857](https://github.com/nlohmann/json/issues/857)
- very basic: fetching string value/content without quotes [\#853](https://github.com/nlohmann/json/issues/853)
- Ambiguous function call to get with pointer type and constant json object in VS2015 \(15.4.4\) [\#852](https://github.com/nlohmann/json/issues/852)
- How to put  object in the array as a member? [\#850](https://github.com/nlohmann/json/issues/850)
- misclick, please ignore [\#849](https://github.com/nlohmann/json/issues/849)
- Make XML great again. [\#847](https://github.com/nlohmann/json/issues/847)
- Converting to array not working [\#843](https://github.com/nlohmann/json/issues/843)
- Iteration weirdness [\#842](https://github.com/nlohmann/json/issues/842)
- Use reference or pointer as Object value [\#841](https://github.com/nlohmann/json/issues/841)
- Ambiguity in parsing nested maps [\#840](https://github.com/nlohmann/json/issues/840)
- could not find from\_json\(\) method in T's namespace [\#839](https://github.com/nlohmann/json/issues/839)
- Incorrect parse error with binary data in keys? [\#838](https://github.com/nlohmann/json/issues/838)
- using dump\(\) when std::wstring is StringType with VS2017 [\#836](https://github.com/nlohmann/json/issues/836)
- Show the path of the currently parsed value when an error occurs [\#835](https://github.com/nlohmann/json/issues/835)
- Repetitive data type while reading [\#833](https://github.com/nlohmann/json/issues/833)
- Storing multiple types inside map [\#831](https://github.com/nlohmann/json/issues/831)
- Application terminating [\#830](https://github.com/nlohmann/json/issues/830)
- Missing CMake hunter package? [\#828](https://github.com/nlohmann/json/issues/828)
- std::map\<std::string, std::string\> from json object yields C2665: 'std::pair\<const \_Kty,\_Ty\>::pair': none of the 2 overloads could convert all the argument types [\#827](https://github.com/nlohmann/json/issues/827)
- object.dump gives quoted string, want to use .dump\(\) to generate javascripts. [\#826](https://github.com/nlohmann/json/issues/826)
- Assertion failed on \["NoExistKey"\] of an not existing key of const json& [\#825](https://github.com/nlohmann/json/issues/825)
- vs2015 error : static member will remain uninitialized at runtime but use in constant-expressions is supported [\#824](https://github.com/nlohmann/json/issues/824)
- Code Checking Warnings from json.hpp on VS2017 Community [\#821](https://github.com/nlohmann/json/issues/821)
- Missing iostream in try online [\#820](https://github.com/nlohmann/json/issues/820)
- Floating point value loses decimal point during dump [\#818](https://github.com/nlohmann/json/issues/818)
- Conan package for the library [\#817](https://github.com/nlohmann/json/issues/817)
- stream error  [\#815](https://github.com/nlohmann/json/issues/815)
- Link error when using find\(\) on the latest commit [\#814](https://github.com/nlohmann/json/issues/814)
- ABI issue with json object between 2 shared libraries [\#813](https://github.com/nlohmann/json/issues/813)
- scan\_string\(\) return token\_type::parse\_error; when parse ansi file [\#812](https://github.com/nlohmann/json/issues/812)
- segfault when using fifo\_map with json [\#810](https://github.com/nlohmann/json/issues/810)
- This shit is shit  [\#809](https://github.com/nlohmann/json/issues/809)
- \_finite and \_isnan are no members of "std" [\#808](https://github.com/nlohmann/json/issues/808)
- how to print out the line which causing exception? [\#806](https://github.com/nlohmann/json/issues/806)
- {} uses copy constructor, while = does not [\#805](https://github.com/nlohmann/json/issues/805)
- json.hpp:8955: multiple definition of function that is not defined twice or more. [\#804](https://github.com/nlohmann/json/issues/804)
- \[question\] to\_json for base and derived class [\#803](https://github.com/nlohmann/json/issues/803)
- Misleading error message - unexpected '"' - on incorrect utf-8 symbol [\#802](https://github.com/nlohmann/json/issues/802)
- json data = std::string\_view\("hi"\); doesn't work? [\#801](https://github.com/nlohmann/json/issues/801)
- Thread safety of parse\(\) [\#800](https://github.com/nlohmann/json/issues/800)
- Numbers as strings [\#799](https://github.com/nlohmann/json/issues/799)
- Tests failing on arm [\#797](https://github.com/nlohmann/json/issues/797)
- Using your library \(without modification\) in another library [\#796](https://github.com/nlohmann/json/issues/796)
- Iterating over sub-object [\#794](https://github.com/nlohmann/json/issues/794)
- how to get the json object again from which printed by the method of dump\(\) [\#792](https://github.com/nlohmann/json/issues/792)
- ppa to include source [\#791](https://github.com/nlohmann/json/issues/791)
- Different include paths in macOS and Ubuntu [\#790](https://github.com/nlohmann/json/issues/790)
- Missing break after line 12886 in switch/case [\#789](https://github.com/nlohmann/json/issues/789)
- All unit tests fail? [\#787](https://github.com/nlohmann/json/issues/787)
- More use of move semantics in deserialization [\#786](https://github.com/nlohmann/json/issues/786)
- warning C4706 - Visual Studio 2017 \(/W4\) [\#784](https://github.com/nlohmann/json/issues/784)
- Compile error in clang 5.0 [\#782](https://github.com/nlohmann/json/issues/782)
- Error Installing appium\_lib with Ruby v2.4.2 Due to JSON [\#781](https://github.com/nlohmann/json/issues/781)
- ::get\<int\>\(\) fails in new\(er\) release \[MSVC\] [\#780](https://github.com/nlohmann/json/issues/780)
- Type Conversion [\#779](https://github.com/nlohmann/json/issues/779)
- Segfault on nested parsing [\#778](https://github.com/nlohmann/json/issues/778)
- Build warnings: shadowing exception id [\#776](https://github.com/nlohmann/json/issues/776)
- multi-level JSON support. [\#775](https://github.com/nlohmann/json/issues/775)
- SIGABRT on dump\(\) [\#773](https://github.com/nlohmann/json/issues/773)
- \[Question\] Custom StringType template parameter \(possibility for a KeyType template parameter\) [\#772](https://github.com/nlohmann/json/issues/772)
- constexpr ALL the Things! [\#771](https://github.com/nlohmann/json/issues/771)
- error: ‘BasicJsonType’ in namespace ‘::’ does not name a type [\#770](https://github.com/nlohmann/json/issues/770)
- Program calls abort function [\#769](https://github.com/nlohmann/json/issues/769)
- \[Question\] Floating point resolution config during dump\(\) ? [\#768](https://github.com/nlohmann/json/issues/768)
- make check - no test ran [\#767](https://github.com/nlohmann/json/issues/767)
- The library cannot work properly with custom allocator based containers [\#766](https://github.com/nlohmann/json/issues/766)
- Documentation or feature request. [\#763](https://github.com/nlohmann/json/issues/763)
- warnings in msvc about mix/max macro while windows.h is used in the project [\#762](https://github.com/nlohmann/json/issues/762)
- std::signbit ambiguous [\#761](https://github.com/nlohmann/json/issues/761)
- How to use value for std::experimental::optional type? [\#760](https://github.com/nlohmann/json/issues/760)
- Cannot load json file properly [\#759](https://github.com/nlohmann/json/issues/759)
- Compilation error with unordered\_map\< int, int \> [\#758](https://github.com/nlohmann/json/issues/758)
- CBOR string [\#757](https://github.com/nlohmann/json/issues/757)
- Proposal: out\_of\_range should be a subclass of std::out\_of\_range [\#756](https://github.com/nlohmann/json/issues/756)
- Getter is setting the value to null if the key does not exist [\#754](https://github.com/nlohmann/json/issues/754)
- parsing works sometimes and crashes others [\#752](https://github.com/nlohmann/json/issues/752)
- Static\_assert failed "incompatible pointer type" with Xcode [\#751](https://github.com/nlohmann/json/issues/751)
- user-defined literal operator not found [\#750](https://github.com/nlohmann/json/issues/750)
- getting clean string from it.key\(\) [\#748](https://github.com/nlohmann/json/issues/748)
- Best method for exploring and obtaining values of nested json objects when the names are not known beforehand? [\#747](https://github.com/nlohmann/json/issues/747)
- null char at the end of string [\#746](https://github.com/nlohmann/json/issues/746)
- Incorrect sample for operator \>\> in docs [\#745](https://github.com/nlohmann/json/issues/745)
- User-friendly documentation [\#744](https://github.com/nlohmann/json/issues/744)
- Retrieve all values that match a json path [\#743](https://github.com/nlohmann/json/issues/743)
- Compilation issue with gcc 7.2 [\#742](https://github.com/nlohmann/json/issues/742)
- CMake target nlohmann\_json does not have src into its interface includes [\#741](https://github.com/nlohmann/json/issues/741)
- Error when serializing empty json: type must be string, but is object [\#740](https://github.com/nlohmann/json/issues/740)
- Conversion error for std::map\<int, std::string\>  [\#739](https://github.com/nlohmann/json/issues/739)
- Dumping Json to file as array [\#738](https://github.com/nlohmann/json/issues/738)
- nesting json objects [\#737](https://github.com/nlohmann/json/issues/737)
- where to find general help? [\#736](https://github.com/nlohmann/json/issues/736)
- Compilation Error on Clang 5.0 Upgrade [\#735](https://github.com/nlohmann/json/issues/735)
- Compilation error with std::map\<std::string, std::string\> on vs 2015 [\#734](https://github.com/nlohmann/json/issues/734)
- Benchmarks for Binary formats [\#733](https://github.com/nlohmann/json/issues/733)
- Support \n symbols in json string. [\#731](https://github.com/nlohmann/json/issues/731)
- Project's name is too generic and hard to search for [\#730](https://github.com/nlohmann/json/issues/730)
- Visual Studio 2015 IntelliTrace problems [\#729](https://github.com/nlohmann/json/issues/729)
- How to erase nested objects inside other objects? [\#728](https://github.com/nlohmann/json/issues/728)
- Serialization for CBOR [\#726](https://github.com/nlohmann/json/issues/726)
- Using json Object as value in a map [\#725](https://github.com/nlohmann/json/issues/725)
- std::regex and nlohmann::json value [\#724](https://github.com/nlohmann/json/issues/724)
- Warnings when compiling with VisualStudio 2015 [\#723](https://github.com/nlohmann/json/issues/723)
- Has this lib the unicode \(wstring\) support? [\#722](https://github.com/nlohmann/json/issues/722)
- When will be 3.0 in master? [\#721](https://github.com/nlohmann/json/issues/721)
- Determine the type from error message. [\#720](https://github.com/nlohmann/json/issues/720)
- Compile-Error C2100 \(MS VS2015\) in line 887 json.hpp [\#719](https://github.com/nlohmann/json/issues/719)
- from\_json not working for boost::optional example [\#718](https://github.com/nlohmann/json/issues/718)
- about from\_json and to\_json function [\#717](https://github.com/nlohmann/json/issues/717)
- How to detect parse failure? [\#715](https://github.com/nlohmann/json/issues/715)
- Parse throw std::ios\_base::failure exception when failbit set to true [\#714](https://github.com/nlohmann/json/issues/714)
- Is there a way of format just making a pretty print without changing the key's orders ? [\#713](https://github.com/nlohmann/json/issues/713)
- Serialization of array of not same model items [\#712](https://github.com/nlohmann/json/issues/712)
- pointer to json parse vector [\#711](https://github.com/nlohmann/json/issues/711)
- Gtest SEH Exception [\#709](https://github.com/nlohmann/json/issues/709)
- broken from\_json implementation for pair and tuple  [\#707](https://github.com/nlohmann/json/issues/707)
- Unevaluated lambda in assert breaks gcc 7 build [\#705](https://github.com/nlohmann/json/issues/705)
- Issues when adding values to firebase database [\#704](https://github.com/nlohmann/json/issues/704)
- Floating point equality - revisited [\#703](https://github.com/nlohmann/json/issues/703)
- Conversion from valarray\<double\> to json fails to build [\#702](https://github.com/nlohmann/json/issues/702)
- internal compiler error \(gcc7\)  [\#701](https://github.com/nlohmann/json/issues/701)
- One build system to rule them all [\#698](https://github.com/nlohmann/json/issues/698)
- Generated nlohmann\_jsonConfig.cmake does not set JSON\_INCLUDE\_DIR [\#695](https://github.com/nlohmann/json/issues/695)
- support the Chinese language in json string [\#694](https://github.com/nlohmann/json/issues/694)
- NaN problem within develop branch [\#693](https://github.com/nlohmann/json/issues/693)
- Please post example of specialization for boost::filesystem [\#692](https://github.com/nlohmann/json/issues/692)
- Impossible to do an array of composite objects [\#691](https://github.com/nlohmann/json/issues/691)
- How to save json to file? [\#690](https://github.com/nlohmann/json/issues/690)
- my simple json parser [\#689](https://github.com/nlohmann/json/issues/689)
- problem with new struct parsing syntax [\#688](https://github.com/nlohmann/json/issues/688)
- Parse error while parse the json string contains  UTF 8 encoded document bytes string [\#684](https://github.com/nlohmann/json/issues/684)
- \[question\] how to get a string value by pointer [\#683](https://github.com/nlohmann/json/issues/683)
- create json object from string variable [\#681](https://github.com/nlohmann/json/issues/681)
- adl\_serializer and CRTP [\#680](https://github.com/nlohmann/json/issues/680)
- Is there a way to control the precision of serialized floating point numbers? [\#677](https://github.com/nlohmann/json/issues/677)
- Is there a way to get the path of a value? [\#676](https://github.com/nlohmann/json/issues/676)
- Could the parser locate errors to line? [\#675](https://github.com/nlohmann/json/issues/675)
- There is performance inefficiency found by coverity tool json2.1.1/include/nlohmann/json.hpp [\#673](https://github.com/nlohmann/json/issues/673)
- include problem, when cmake on osx [\#672](https://github.com/nlohmann/json/issues/672)
- Operator= ambiguous in C++1z and GCC 7.1.1 [\#670](https://github.com/nlohmann/json/issues/670)
- should't the cmake install target be to nlohman/json.hpp [\#668](https://github.com/nlohmann/json/issues/668)
- deserialise from `std::vector` [\#667](https://github.com/nlohmann/json/issues/667)
- How to iterate? [\#665](https://github.com/nlohmann/json/issues/665)
- could this json lib work on windows? [\#664](https://github.com/nlohmann/json/issues/664)
- How does from\_json work? [\#662](https://github.com/nlohmann/json/issues/662)
- insert\(or merge\) object should replace same key , not ignore [\#661](https://github.com/nlohmann/json/issues/661)
- Parse method doesn't handle newlines. [\#659](https://github.com/nlohmann/json/issues/659)
- Compilation "note" on GCC 6 ARM [\#658](https://github.com/nlohmann/json/issues/658)
- Adding additional push\_back/operator+= rvalue overloads for JSON object [\#657](https://github.com/nlohmann/json/issues/657)
- dump's parameter "ensure\_ascii" creates too long sequences [\#656](https://github.com/nlohmann/json/issues/656)
- Question: parsing `void *` [\#655](https://github.com/nlohmann/json/issues/655)
- how should I check a string is valid JSON string ? [\#653](https://github.com/nlohmann/json/issues/653)
- Question: thread safety of read only accesses [\#651](https://github.com/nlohmann/json/issues/651)
- Eclipse: Method 'size' could not be resolved [\#649](https://github.com/nlohmann/json/issues/649)
- Update/Add object fields [\#648](https://github.com/nlohmann/json/issues/648)
- No exception raised for Out Of Range input of numbers [\#647](https://github.com/nlohmann/json/issues/647)
- Package Name [\#646](https://github.com/nlohmann/json/issues/646)
- What is the meaning of operator\[\]\(T\* key\) [\#645](https://github.com/nlohmann/json/issues/645)
- Which is the correct way to json objects as parameters to functions? [\#644](https://github.com/nlohmann/json/issues/644)
- Method to get string representations of values [\#642](https://github.com/nlohmann/json/issues/642)
-  CBOR serialization of a given JSON value does not serialize [\#641](https://github.com/nlohmann/json/issues/641)
- Are we forced to use "-fexceptions" flag in android ndk project [\#640](https://github.com/nlohmann/json/issues/640)
- Comparison of objects containing floats [\#639](https://github.com/nlohmann/json/issues/639)
- 'localeconv' is not supported by NDK for SDK  \<=20 [\#638](https://github.com/nlohmann/json/issues/638)
- \[Question\] cLion integration [\#637](https://github.com/nlohmann/json/issues/637)
- How to construct an iteratable usage in nlohmann json? [\#636](https://github.com/nlohmann/json/issues/636)
- \[Question\] copy assign json-container to vector [\#635](https://github.com/nlohmann/json/issues/635)
- Get size without .dump\(\) [\#634](https://github.com/nlohmann/json/issues/634)
- Segmentation fault when parsing invalid json file [\#633](https://github.com/nlohmann/json/issues/633)
- How to serialize from json to vector\<customType\>? [\#632](https://github.com/nlohmann/json/issues/632)
- no member named 'thousands\_sep' in 'lconv' [\#631](https://github.com/nlohmann/json/issues/631)
- \[Question\] Any fork for \(the unsupported\) Visual Studio 2012 version? [\#628](https://github.com/nlohmann/json/issues/628)
- Dependency injection in serializer [\#627](https://github.com/nlohmann/json/issues/627)
- from\_json for std::array [\#625](https://github.com/nlohmann/json/issues/625)
- Discussion: How to structure the parsing function families [\#623](https://github.com/nlohmann/json/issues/623)
- Question: How to erase subtree [\#622](https://github.com/nlohmann/json/issues/622)
- Insertion into nested json field [\#621](https://github.com/nlohmann/json/issues/621)
- Question: return static json object from function [\#618](https://github.com/nlohmann/json/issues/618)
- icc16 error [\#617](https://github.com/nlohmann/json/issues/617)
- \[-Wdeprecated-declarations\] in row `j >> ss;` in file `json.hpp:7405:26` and FAILED unit tests with MinGWx64! [\#616](https://github.com/nlohmann/json/issues/616)
- to\_json for pairs, tuples [\#614](https://github.com/nlohmann/json/issues/614)
- Using uninitialized memory 'buf' in line 11173 v2.1.1? [\#613](https://github.com/nlohmann/json/issues/613)
- How to parse multiple same Keys of JSON and save them? [\#612](https://github.com/nlohmann/json/issues/612)
- "Multiple declarations" error when using types defined with `typedef` [\#611](https://github.com/nlohmann/json/issues/611)
- 2.1.1+ breaks compilation of shared\_ptr\<json\> == 0 [\#610](https://github.com/nlohmann/json/issues/610)
- a bug of inheritance ?  [\#608](https://github.com/nlohmann/json/issues/608)
- std::map key conversion with to\_json [\#607](https://github.com/nlohmann/json/issues/607)
- json.hpp:6384:62: error: wrong number of template arguments \(1, should be 2\) [\#606](https://github.com/nlohmann/json/issues/606)
- Incremental parsing: Where's the push version? [\#605](https://github.com/nlohmann/json/issues/605)
- Is there a way to validate the structure of a json object ? [\#604](https://github.com/nlohmann/json/issues/604)
- \[Question\] Issue when using Appveyor when compiling library [\#603](https://github.com/nlohmann/json/issues/603)
- BOM not skipped when using json:parse\(iterator\) [\#602](https://github.com/nlohmann/json/issues/602)
- Use of the binary type in CBOR and Message Pack [\#601](https://github.com/nlohmann/json/issues/601)
- Newbie issue: how does one convert a map in Json back to std::map? [\#600](https://github.com/nlohmann/json/issues/600)
- Plugin system [\#599](https://github.com/nlohmann/json/issues/599)
- Using custom types for scalars? [\#596](https://github.com/nlohmann/json/issues/596)
- Issues with the arithmetic in iterator and reverse iterator [\#593](https://github.com/nlohmann/json/issues/593)
- not enough examples [\#592](https://github.com/nlohmann/json/issues/592)
- in-class initialization for type 'const T' is not yet implemented [\#591](https://github.com/nlohmann/json/issues/591)
- compiling with gcc 7 -\> error on bool operator \< [\#590](https://github.com/nlohmann/json/issues/590)
- Parsing from stream leads to an array [\#589](https://github.com/nlohmann/json/issues/589)
- Buggy support for binary string data [\#587](https://github.com/nlohmann/json/issues/587)
- C++17's ambiguous conversion [\#586](https://github.com/nlohmann/json/issues/586)
- How does the messagepack encoding/decoding compare to msgpack-cpp in terms of performance? [\#585](https://github.com/nlohmann/json/issues/585)
- is it possible to check existence of a value deep in hierarchy? [\#584](https://github.com/nlohmann/json/issues/584)
- loading from a stream and exceptions [\#582](https://github.com/nlohmann/json/issues/582)
- Visual Studio seems not to have all min\(\) function versions [\#581](https://github.com/nlohmann/json/issues/581)
- Supporting of the json schema [\#580](https://github.com/nlohmann/json/issues/580)
- Stack-overflow \(OSS-Fuzz 1444\) [\#577](https://github.com/nlohmann/json/issues/577)
- Heap-buffer-overflow \(OSS-Fuzz 1400\) [\#575](https://github.com/nlohmann/json/issues/575)
- JSON escape quotes [\#574](https://github.com/nlohmann/json/issues/574)
- error: static\_assert failed [\#573](https://github.com/nlohmann/json/issues/573)
- Storing floats, and round trip serialisation/deserialisation diffs [\#572](https://github.com/nlohmann/json/issues/572)
- JSON.getLong produces inconsistent results [\#571](https://github.com/nlohmann/json/issues/571)
- Request: Object.at\(\) with default return value [\#570](https://github.com/nlohmann/json/issues/570)
- Internal structure gets corrupted while parsing [\#569](https://github.com/nlohmann/json/issues/569)
- create template \<typename Iter\> basic\_json from\_cbor\(Iter begin, Iter end\) [\#568](https://github.com/nlohmann/json/issues/568)
- Conan.io [\#566](https://github.com/nlohmann/json/issues/566)
- contradictory documentation regarding json::find [\#565](https://github.com/nlohmann/json/issues/565)
- Unexpected '\"' in middle of array [\#564](https://github.com/nlohmann/json/issues/564)
- Support parse std::pair to Json object [\#563](https://github.com/nlohmann/json/issues/563)
- json and Microsoft Visual c++ Compiler Nov 2012 CTP [\#562](https://github.com/nlohmann/json/issues/562)
- from\_json declaration order and exceptions [\#561](https://github.com/nlohmann/json/issues/561)
- Tip: Don't upgrade to VS2017 if using json initializer list constructs [\#559](https://github.com/nlohmann/json/issues/559)
- parse error - unexpected end of input [\#558](https://github.com/nlohmann/json/issues/558)
- Cant modify existing numbers inside a json object [\#557](https://github.com/nlohmann/json/issues/557)
- Better support for SAX style serialize and deserialize in new version? [\#554](https://github.com/nlohmann/json/issues/554)
- Cannot convert from json array to std::array [\#553](https://github.com/nlohmann/json/issues/553)
- Do not define an unnamed namespace in a header file \(DCL59-CPP\) [\#552](https://github.com/nlohmann/json/issues/552)
- Parse error on known good json file [\#551](https://github.com/nlohmann/json/issues/551)
- Warning on Intel compiler \(icc 17\) [\#550](https://github.com/nlohmann/json/issues/550)
- multiple versions of 'vsnprintf' [\#549](https://github.com/nlohmann/json/issues/549)
- illegal indirection [\#548](https://github.com/nlohmann/json/issues/548)
- Ambiguous compare operators with clang-5.0 [\#547](https://github.com/nlohmann/json/issues/547)
- Using tsl::ordered\_map [\#546](https://github.com/nlohmann/json/issues/546)
- Compiler support errors are inconvenient [\#544](https://github.com/nlohmann/json/issues/544)
- Duplicate symbols error happens while to\_json/from\_json method implemented inside entity definition header file [\#542](https://github.com/nlohmann/json/issues/542)
- consider adding a bool json::is\_valid\(std::string const&\) non-member function [\#541](https://github.com/nlohmann/json/issues/541)
- Help request [\#539](https://github.com/nlohmann/json/issues/539)
- How to deal with missing keys in `from_json`? [\#538](https://github.com/nlohmann/json/issues/538)
- recursive from\_msgpack implementation will stack overflow [\#537](https://github.com/nlohmann/json/issues/537)
- Exception objects must be nothrow copy constructible \(ERR60-CPP\) [\#531](https://github.com/nlohmann/json/issues/531)
- Support for multiple root elements [\#529](https://github.com/nlohmann/json/issues/529)
- Port has\_shape from dropbox/json11 [\#528](https://github.com/nlohmann/json/issues/528)
- dump\_float: truncation from ptrdiff\_t to long [\#527](https://github.com/nlohmann/json/issues/527)
- Make exception base class visible in basic\_json [\#525](https://github.com/nlohmann/json/issues/525)
- msgpack unit test failures on ppc64 arch [\#524](https://github.com/nlohmann/json/issues/524)
- How about split the implementation out, and only leave the interface? [\#523](https://github.com/nlohmann/json/issues/523)
- VC++2017 not enough actual parameters for macro 'max' [\#522](https://github.com/nlohmann/json/issues/522)
- crash on empty ifstream [\#521](https://github.com/nlohmann/json/issues/521)
- Suggestion: Support tabs for indentation when serializing to stream. [\#520](https://github.com/nlohmann/json/issues/520)
- Abrt in get\_number \(OSS-Fuzz 885\) [\#519](https://github.com/nlohmann/json/issues/519)
- Abrt on unknown address \(OSS-Fuzz 884\) [\#518](https://github.com/nlohmann/json/issues/518)
- Stack-overflow \(OSS-Fuzz 869\) [\#517](https://github.com/nlohmann/json/issues/517)
- Assertion error \(OSS-Fuzz 868\) [\#516](https://github.com/nlohmann/json/issues/516)
- NaN to json and back [\#515](https://github.com/nlohmann/json/issues/515)
- Comparison of NaN [\#514](https://github.com/nlohmann/json/issues/514)
- why it's not possible to serialize c++11 enums directly [\#513](https://github.com/nlohmann/json/issues/513)
- clang compile error: use of overloaded operator '\<=' is ambiguous   with \(nlohmann::json{{"a", 5}}\)\["a"\] \<= 10 [\#512](https://github.com/nlohmann/json/issues/512)
- Why not also look inside the type for \(static\) to\_json and from\_json funtions? [\#511](https://github.com/nlohmann/json/issues/511)
- Parser issues [\#509](https://github.com/nlohmann/json/issues/509)
- I may not understand [\#507](https://github.com/nlohmann/json/issues/507)
- VS2017 min / max problem for 2.1.1 [\#506](https://github.com/nlohmann/json/issues/506)
- CBOR/MessagePack is not read until the end [\#505](https://github.com/nlohmann/json/issues/505)
- Assertion error \(OSS-Fuzz 856\) [\#504](https://github.com/nlohmann/json/issues/504)
- Return position in parse error exceptions [\#503](https://github.com/nlohmann/json/issues/503)
- conversion from/to C array is not supported [\#502](https://github.com/nlohmann/json/issues/502)
- error C2338: could not find to\_json\(\) method in T's namespace [\#501](https://github.com/nlohmann/json/issues/501)
- Test suite fails in en\_GB.UTF-8 [\#500](https://github.com/nlohmann/json/issues/500)
- cannot use operator\[\] with number [\#499](https://github.com/nlohmann/json/issues/499)
- consider using \_\_cpp\_exceptions and/or \_\_EXCEPTIONS to disable/enable exception support [\#498](https://github.com/nlohmann/json/issues/498)
- Stack-overflow \(OSS-Fuzz issue 814\) [\#497](https://github.com/nlohmann/json/issues/497)
- Using in Unreal Engine - handling custom types conversion [\#495](https://github.com/nlohmann/json/issues/495)
- Conversion from vector\<bool\> to json fails to build [\#494](https://github.com/nlohmann/json/issues/494)
- fill\_line\_buffer incorrectly tests m\_stream for eof but not fail or bad bits [\#493](https://github.com/nlohmann/json/issues/493)
- Compiling with \_GLIBCXX\_DEBUG yields iterator-comparison warnings during tests [\#492](https://github.com/nlohmann/json/issues/492)
- crapy interface [\#491](https://github.com/nlohmann/json/issues/491)
- Fix Visual Studo 2013 builds. [\#490](https://github.com/nlohmann/json/issues/490)
- Failed to compile with -D\_GLIBCXX\_PARALLEL [\#489](https://github.com/nlohmann/json/issues/489)
- Input several field with the same name [\#488](https://github.com/nlohmann/json/issues/488)
- read in .json file yields strange sizes [\#487](https://github.com/nlohmann/json/issues/487)
- json::value\_t can't be a map's key type in VC++ 2015 [\#486](https://github.com/nlohmann/json/issues/486)
- Using fifo\_map [\#485](https://github.com/nlohmann/json/issues/485)
- Cannot get float pointer for value stored as `0` [\#484](https://github.com/nlohmann/json/issues/484)
- byte string support [\#483](https://github.com/nlohmann/json/issues/483)
- https://github.com/nlohmann/json\#execute-unit-tests [\#481](https://github.com/nlohmann/json/issues/481)
- Remove deprecated constructor basic\_json\(std::istream&\) [\#480](https://github.com/nlohmann/json/issues/480)
- writing the binary json file? [\#479](https://github.com/nlohmann/json/issues/479)
- CBOR/MessagePack from uint8\_t \* and size [\#478](https://github.com/nlohmann/json/issues/478)
- Streaming binary representations  [\#477](https://github.com/nlohmann/json/issues/477)
- Reuse memory in to\_cbor and to\_msgpack functions [\#476](https://github.com/nlohmann/json/issues/476)
- Error Using JSON Library with arrays C++ [\#475](https://github.com/nlohmann/json/issues/475)
- Moving forward to version 3.0.0 [\#474](https://github.com/nlohmann/json/issues/474)
- Inconsistent behavior in conversion to array type [\#473](https://github.com/nlohmann/json/issues/473)
- Create a \[key:member\_pointer\] map to ease parsing custom types [\#471](https://github.com/nlohmann/json/issues/471)
- MSVC 2015 update 2 [\#469](https://github.com/nlohmann/json/issues/469)
- VS2017 implicit to std::string conversion fix. [\#464](https://github.com/nlohmann/json/issues/464)
- How to make sure a string or string literal is a valid JSON? [\#458](https://github.com/nlohmann/json/issues/458)
- basic\_json templated on a "policy" class [\#456](https://github.com/nlohmann/json/issues/456)
- json::value\(const json\_pointer&, ValueType\) requires exceptions to return the default value. [\#440](https://github.com/nlohmann/json/issues/440)
- is it possible merge two json object [\#428](https://github.com/nlohmann/json/issues/428)
- Is it possible to turn this into a shared library? [\#420](https://github.com/nlohmann/json/issues/420)
- Further thoughts on performance improvements [\#418](https://github.com/nlohmann/json/issues/418)
- nan number stored as null [\#388](https://github.com/nlohmann/json/issues/388)
- Behavior of operator\>\> should more closely resemble that of built-in overloads. [\#367](https://github.com/nlohmann/json/issues/367)
- Request: range-based-for over a json-object to expose .first/.second [\#350](https://github.com/nlohmann/json/issues/350)
- feature wish: JSONPath [\#343](https://github.com/nlohmann/json/issues/343)
- UTF-8/Unicode escape and dump [\#330](https://github.com/nlohmann/json/issues/330)
- Serialized value not always can be parsed. [\#329](https://github.com/nlohmann/json/issues/329)
- Is there a way to forward declare nlohmann::json? [\#314](https://github.com/nlohmann/json/issues/314)
- Exception line [\#301](https://github.com/nlohmann/json/issues/301)
- Do not throw exception when default\_value's type does not match the actual type [\#278](https://github.com/nlohmann/json/issues/278)
- dump\(\) method doesn't work with a custom allocator [\#268](https://github.com/nlohmann/json/issues/268)
- Readme documentation enhancements [\#248](https://github.com/nlohmann/json/issues/248)
- Use user-defined exceptions [\#244](https://github.com/nlohmann/json/issues/244)
- Incorrect C++11 allocator model support [\#161](https://github.com/nlohmann/json/issues/161)

- :white\_check\_mark: re-added tests for algorithms [\#879](https://github.com/nlohmann/json/pull/879) ([nlohmann](https://github.com/nlohmann))
- Overworked library toward 3.0.0 release [\#875](https://github.com/nlohmann/json/pull/875) ([nlohmann](https://github.com/nlohmann))
- :rotating\_light: remove C4996 warnings \#872 [\#873](https://github.com/nlohmann/json/pull/873) ([nlohmann](https://github.com/nlohmann))
- :boom: throwing an exception in case dump encounters a non-UTF-8 string \#838 [\#870](https://github.com/nlohmann/json/pull/870) ([nlohmann](https://github.com/nlohmann))
- :memo: fixing documentation \#867 [\#868](https://github.com/nlohmann/json/pull/868) ([nlohmann](https://github.com/nlohmann))
- iter\_impl template conformance with C++17 [\#860](https://github.com/nlohmann/json/pull/860) ([bogemic](https://github.com/bogemic))
- Std allocator conformance cpp17 [\#856](https://github.com/nlohmann/json/pull/856) ([bogemic](https://github.com/bogemic))
- cmake: use BUILD\_INTERFACE/INSTALL\_INTERFACE [\#855](https://github.com/nlohmann/json/pull/855) ([theodelrieu](https://github.com/theodelrieu))
- to/from\_json: add a MSVC-specific static\_assert to force a stacktrace [\#854](https://github.com/nlohmann/json/pull/854) ([theodelrieu](https://github.com/theodelrieu))
- Add .natvis for MSVC debug view [\#844](https://github.com/nlohmann/json/pull/844) ([TinyTinni](https://github.com/TinyTinni))
- Updated hunter package links [\#829](https://github.com/nlohmann/json/pull/829) ([jowr](https://github.com/jowr))
- Typos README [\#811](https://github.com/nlohmann/json/pull/811) ([Itja](https://github.com/Itja))
- add forwarding references to json\_ref constructor [\#807](https://github.com/nlohmann/json/pull/807) ([theodelrieu](https://github.com/theodelrieu))
- Add transparent comparator and perfect forwarding support to find\(\) and count\(\) [\#795](https://github.com/nlohmann/json/pull/795) ([jseward](https://github.com/jseward))
- Error : 'identifier "size\_t" is undefined' in linux [\#793](https://github.com/nlohmann/json/pull/793) ([sonulohani](https://github.com/sonulohani))
- Fix Visual Studio 2017 warnings [\#788](https://github.com/nlohmann/json/pull/788) ([jseward](https://github.com/jseward))
- Fix warning C4706 on Visual Studio 2017 [\#785](https://github.com/nlohmann/json/pull/785) ([jseward](https://github.com/jseward))
- Set GENERATE\_TAGFILE in Doxyfile [\#783](https://github.com/nlohmann/json/pull/783) ([eld00d](https://github.com/eld00d))
- using more CMake [\#765](https://github.com/nlohmann/json/pull/765) ([nlohmann](https://github.com/nlohmann))
- Simplified istream handing \#367 [\#764](https://github.com/nlohmann/json/pull/764) ([pjkundert](https://github.com/pjkundert))
- Add info for the vcpkg package. [\#753](https://github.com/nlohmann/json/pull/753) ([gregmarr](https://github.com/gregmarr))
- fix from\_json implementation for pair/tuple [\#708](https://github.com/nlohmann/json/pull/708) ([theodelrieu](https://github.com/theodelrieu))
- Update json.hpp [\#686](https://github.com/nlohmann/json/pull/686) ([GoWebProd](https://github.com/GoWebProd))
- Remove duplicate word [\#685](https://github.com/nlohmann/json/pull/685) ([daixtrose](https://github.com/daixtrose))
- To fix compilation issue for intel OSX compiler [\#682](https://github.com/nlohmann/json/pull/682) ([kbthomp1](https://github.com/kbthomp1))
- Digraph warning [\#679](https://github.com/nlohmann/json/pull/679) ([traits](https://github.com/traits))
- massage -\> message [\#678](https://github.com/nlohmann/json/pull/678) ([DmitryKuk](https://github.com/DmitryKuk))
- Fix "not constraint" grammar in docs [\#674](https://github.com/nlohmann/json/pull/674) ([wincent](https://github.com/wincent))
- Add documentation for integration with CMake and hunter [\#671](https://github.com/nlohmann/json/pull/671) ([dan-42](https://github.com/dan-42))
- REFACTOR: rewrite CMakeLists.txt for better inlcude and reuse [\#669](https://github.com/nlohmann/json/pull/669) ([dan-42](https://github.com/dan-42))
- enable\_testing only if the JSON\_BuildTests is ON [\#666](https://github.com/nlohmann/json/pull/666) ([effolkronium](https://github.com/effolkronium))
- Support moving from rvalues in std::initializer\_list [\#663](https://github.com/nlohmann/json/pull/663) ([himikof](https://github.com/himikof))
- add ensure\_ascii parameter to dump. \#330 [\#654](https://github.com/nlohmann/json/pull/654) ([ryanjmulder](https://github.com/ryanjmulder))
- Rename BuildTests to JSON\_BuildTests [\#652](https://github.com/nlohmann/json/pull/652) ([olegendo](https://github.com/olegendo))
- Don't include \<iostream\>, use std::make\_shared [\#650](https://github.com/nlohmann/json/pull/650) ([olegendo](https://github.com/olegendo))
- Refacto/split basic json [\#643](https://github.com/nlohmann/json/pull/643) ([theodelrieu](https://github.com/theodelrieu))
- fix typo in operator\_\_notequal example [\#630](https://github.com/nlohmann/json/pull/630) ([Chocobo1](https://github.com/Chocobo1))
- Fix MSVC warning C4819 [\#629](https://github.com/nlohmann/json/pull/629) ([Chocobo1](https://github.com/Chocobo1))
- \[BugFix\] Add parentheses around std::min [\#626](https://github.com/nlohmann/json/pull/626) ([koemeet](https://github.com/koemeet))
- add pair/tuple conversions [\#624](https://github.com/nlohmann/json/pull/624) ([theodelrieu](https://github.com/theodelrieu))
- remove std::pair support [\#615](https://github.com/nlohmann/json/pull/615) ([theodelrieu](https://github.com/theodelrieu))
- Add pair support, fix CompatibleObject conversions \(fixes \#600\) [\#609](https://github.com/nlohmann/json/pull/609) ([theodelrieu](https://github.com/theodelrieu))
- \#550 Fix iterator related compiling issues for Intel icc [\#598](https://github.com/nlohmann/json/pull/598) ([HenryRLee](https://github.com/HenryRLee))
- Issue \#593 Fix the arithmetic operators in the iterator and reverse iterator [\#595](https://github.com/nlohmann/json/pull/595) ([HenryRLee](https://github.com/HenryRLee))
- fix doxygen error of basic\_json::get\(\) [\#583](https://github.com/nlohmann/json/pull/583) ([zhaohuaxishi](https://github.com/zhaohuaxishi))
- Fixing assignement for iterator wrapper second, and adding unit test [\#579](https://github.com/nlohmann/json/pull/579) ([Type1J](https://github.com/Type1J))
- Adding first and second properties to iteration\_proxy\_internal [\#578](https://github.com/nlohmann/json/pull/578) ([Type1J](https://github.com/Type1J))
- Adding support for Meson. [\#576](https://github.com/nlohmann/json/pull/576) ([Type1J](https://github.com/Type1J))
- add enum class default conversions [\#545](https://github.com/nlohmann/json/pull/545) ([theodelrieu](https://github.com/theodelrieu))
- Properly pop diagnostics [\#540](https://github.com/nlohmann/json/pull/540) ([tinloaf](https://github.com/tinloaf))
- Add Visual Studio 17 image to appveyor build matrix [\#536](https://github.com/nlohmann/json/pull/536) ([vpetrigo](https://github.com/vpetrigo))
- UTF8 encoding enhancement [\#534](https://github.com/nlohmann/json/pull/534) ([TedLyngmo](https://github.com/TedLyngmo))
- Fix typo [\#530](https://github.com/nlohmann/json/pull/530) ([berkus](https://github.com/berkus))
- Make exception base class visible in basic\_json [\#526](https://github.com/nlohmann/json/pull/526) ([ghost](https://github.com/ghost))
- :art: Namespace `uint8_t` from the C++ stdlib [\#510](https://github.com/nlohmann/json/pull/510) ([alexweej](https://github.com/alexweej))
- add to\_json method for C arrays [\#508](https://github.com/nlohmann/json/pull/508) ([theodelrieu](https://github.com/theodelrieu))
- Fix -Weffc++ warnings \(GNU 6.3.1\) [\#496](https://github.com/nlohmann/json/pull/496) ([TedLyngmo](https://github.com/TedLyngmo))

## [v2.1.1](https://github.com/nlohmann/json/releases/tag/v2.1.1) (2017-02-25)

[Full Changelog](https://github.com/nlohmann/json/compare/2.1.1...v2.1.1)

## [2.1.1](https://github.com/nlohmann/json/releases/tag/2.1.1) (2017-02-25)

[Full Changelog](https://github.com/nlohmann/json/compare/v2.1.0...2.1.1)

- warning in the library [\#472](https://github.com/nlohmann/json/issues/472)
- How to create an array of Objects? [\#470](https://github.com/nlohmann/json/issues/470)
- \[Bug?\] Cannot get int pointer, but int64\_t works [\#468](https://github.com/nlohmann/json/issues/468)
- Illegal indirection [\#467](https://github.com/nlohmann/json/issues/467)
- in vs can't find linkageId   [\#466](https://github.com/nlohmann/json/issues/466)
- Roundtrip error while parsing "1000000000000000010E5" [\#465](https://github.com/nlohmann/json/issues/465)
- C4996 error and warning with Visual Studio [\#463](https://github.com/nlohmann/json/issues/463)
- Support startIndex for from\_cbor/from\_msgpack [\#462](https://github.com/nlohmann/json/issues/462)
- question: monospace font used in feature slideshow? [\#460](https://github.com/nlohmann/json/issues/460)
- Object.keys\(\) [\#459](https://github.com/nlohmann/json/issues/459)
- Use “, “ as delimiter for json-objects. [\#457](https://github.com/nlohmann/json/issues/457)
- Enum -\> string during serialization and vice versa [\#455](https://github.com/nlohmann/json/issues/455)
- doubles are printed as integers [\#454](https://github.com/nlohmann/json/issues/454)
- Warnings with Visual Studio c++ \(VS2015 Update 3\) [\#453](https://github.com/nlohmann/json/issues/453)
- Heap-buffer-overflow \(OSS-Fuzz issue 585\) [\#452](https://github.com/nlohmann/json/issues/452)
- use of undeclared identifier 'UINT8\_MAX' [\#451](https://github.com/nlohmann/json/issues/451)
- Question on the lifetime managment of objects at the lower levels [\#449](https://github.com/nlohmann/json/issues/449)
- Json should not be constructible with 'json\*' [\#448](https://github.com/nlohmann/json/issues/448)
- Move value\_t to namespace scope [\#447](https://github.com/nlohmann/json/issues/447)
- Typo in README.md [\#446](https://github.com/nlohmann/json/issues/446)
- make check compilation is unneccesarily slow [\#445](https://github.com/nlohmann/json/issues/445)
- Problem in dump\(\) in json.h caused by ss.imbue [\#444](https://github.com/nlohmann/json/issues/444)
- I want to create Windows Application in Visual Studio 2015 c++, and i have a problem [\#443](https://github.com/nlohmann/json/issues/443)
- Implicit conversion issues [\#442](https://github.com/nlohmann/json/issues/442)
- Parsing of floats locale dependent [\#302](https://github.com/nlohmann/json/issues/302)

- Speedup CI builds using cotire [\#461](https://github.com/nlohmann/json/pull/461) ([tusharpm](https://github.com/tusharpm))
- TurpentineDistillery feature/locale independent str to num [\#450](https://github.com/nlohmann/json/pull/450) ([nlohmann](https://github.com/nlohmann))
- README: adjust boost::optional example [\#439](https://github.com/nlohmann/json/pull/439) ([jaredgrubb](https://github.com/jaredgrubb))
- fix \#414 - comparing to 0 literal [\#415](https://github.com/nlohmann/json/pull/415) ([stanmihai4](https://github.com/stanmihai4))
- locale-independent num-to-str [\#378](https://github.com/nlohmann/json/pull/378) ([TurpentineDistillery](https://github.com/TurpentineDistillery))

## [v2.1.0](https://github.com/nlohmann/json/releases/tag/v2.1.0) (2017-01-28)

[Full Changelog](https://github.com/nlohmann/json/compare/2.1.0...v2.1.0)

## [2.1.0](https://github.com/nlohmann/json/releases/tag/2.1.0) (2017-01-28)

[Full Changelog](https://github.com/nlohmann/json/compare/v2.0.10...2.1.0)

- Parsing multiple JSON objects from a string or stream [\#438](https://github.com/nlohmann/json/issues/438)
- Use-of-uninitialized-value \(OSS-Fuzz issue 477\) [\#437](https://github.com/nlohmann/json/issues/437)
- add `reserve` function for array to reserve memory before adding json values into it [\#436](https://github.com/nlohmann/json/issues/436)
- Typo in examples page [\#434](https://github.com/nlohmann/json/issues/434)
- avoid malformed json [\#433](https://github.com/nlohmann/json/issues/433)
- How to add json objects to a map? [\#432](https://github.com/nlohmann/json/issues/432)
- create json instance from raw json \(unsigned char\*\) [\#431](https://github.com/nlohmann/json/issues/431)
- Getting std::invalid\_argument: stream error when following example [\#429](https://github.com/nlohmann/json/issues/429)
- Forward declare-only header? [\#427](https://github.com/nlohmann/json/issues/427)
- Implicit conversion from array to object [\#425](https://github.com/nlohmann/json/issues/425)
- error C4996: 'strerror' when reading file [\#422](https://github.com/nlohmann/json/issues/422)
- Get an error - JSON pointer must be empty or begin with '/' [\#421](https://github.com/nlohmann/json/issues/421)
- size parameter for parse\(\) [\#419](https://github.com/nlohmann/json/issues/419)
- json.hpp forcibly defines GCC\_VERSION [\#417](https://github.com/nlohmann/json/issues/417)
- Use-of-uninitialized-value \(OSS-Fuzz issue 377\) [\#416](https://github.com/nlohmann/json/issues/416)
- comparing to 0 literal [\#414](https://github.com/nlohmann/json/issues/414)
- Single char converted to ASCII code instead of string [\#413](https://github.com/nlohmann/json/issues/413)
- How to know if a string  was parsed as utf-8? [\#406](https://github.com/nlohmann/json/issues/406)
- Overloaded += to add objects to an array makes no sense? [\#404](https://github.com/nlohmann/json/issues/404)
- Finding a value in an array [\#399](https://github.com/nlohmann/json/issues/399)
- add release information in static function [\#397](https://github.com/nlohmann/json/issues/397)
- Optimize memory usage of json objects in combination with binary serialization [\#373](https://github.com/nlohmann/json/issues/373)
- Conversion operators not considered [\#369](https://github.com/nlohmann/json/issues/369)
- Append ".0" to serialized floating\_point values that are digits-only. [\#362](https://github.com/nlohmann/json/issues/362)
- Add a customization point for user-defined types [\#328](https://github.com/nlohmann/json/issues/328)
- Conformance report for reference [\#307](https://github.com/nlohmann/json/issues/307)
- Document the best way to serialize/deserialize user defined types to json [\#298](https://github.com/nlohmann/json/issues/298)
- Add StringView template typename to basic\_json [\#297](https://github.com/nlohmann/json/issues/297)
- \[Improvement\] Add option to remove exceptions [\#296](https://github.com/nlohmann/json/issues/296)
- Performance in miloyip/nativejson-benchmark [\#202](https://github.com/nlohmann/json/issues/202)

- conversion from/to user-defined types [\#435](https://github.com/nlohmann/json/pull/435) ([nlohmann](https://github.com/nlohmann))
- Fix documentation error [\#430](https://github.com/nlohmann/json/pull/430) ([vjon](https://github.com/vjon))

## [v2.0.10](https://github.com/nlohmann/json/releases/tag/v2.0.10) (2017-01-02)

[Full Changelog](https://github.com/nlohmann/json/compare/2.0.10...v2.0.10)

## [2.0.10](https://github.com/nlohmann/json/releases/tag/2.0.10) (2017-01-02)

[Full Changelog](https://github.com/nlohmann/json/compare/v2.0.9...2.0.10)

- Heap-buffer-overflow \(OSS-Fuzz issue 367\) [\#412](https://github.com/nlohmann/json/issues/412)
- Heap-buffer-overflow \(OSS-Fuzz issue 366\) [\#411](https://github.com/nlohmann/json/issues/411)
- Use-of-uninitialized-value \(OSS-Fuzz issue 347\) [\#409](https://github.com/nlohmann/json/issues/409)
- Heap-buffer-overflow \(OSS-Fuzz issue 344\) [\#408](https://github.com/nlohmann/json/issues/408)
- Heap-buffer-overflow \(OSS-Fuzz issue 343\) [\#407](https://github.com/nlohmann/json/issues/407)
- Heap-buffer-overflow \(OSS-Fuzz issue 342\) [\#405](https://github.com/nlohmann/json/issues/405)
- strerror throwing error in compiler VS2015 [\#403](https://github.com/nlohmann/json/issues/403)
- json::parse of std::string being underlined by Visual Studio [\#402](https://github.com/nlohmann/json/issues/402)
- Explicitly getting string without .dump\(\)  [\#401](https://github.com/nlohmann/json/issues/401)
- Possible to speed up json::parse? [\#398](https://github.com/nlohmann/json/issues/398)
- the alphabetic order in the code influence console\_output. [\#396](https://github.com/nlohmann/json/issues/396)
- Execute tests with clang sanitizers [\#394](https://github.com/nlohmann/json/issues/394)
- Check if library can be used with ETL [\#361](https://github.com/nlohmann/json/issues/361)

- Feature/clang sanitize [\#410](https://github.com/nlohmann/json/pull/410) ([Daniel599](https://github.com/Daniel599))
- Add Doozer build badge [\#400](https://github.com/nlohmann/json/pull/400) ([andoma](https://github.com/andoma))

## [v2.0.9](https://github.com/nlohmann/json/releases/tag/v2.0.9) (2016-12-16)

[Full Changelog](https://github.com/nlohmann/json/compare/2.0.9...v2.0.9)

## [2.0.9](https://github.com/nlohmann/json/releases/tag/2.0.9) (2016-12-16)

[Full Changelog](https://github.com/nlohmann/json/compare/v2.0.8...2.0.9)

- \#pragma GCC diagnostic ignored "-Wdocumentation" [\#393](https://github.com/nlohmann/json/issues/393)
- How to parse this json file and write separate sub object as json files? [\#392](https://github.com/nlohmann/json/issues/392)
- Integer-overflow \(OSS-Fuzz issue 267\) [\#389](https://github.com/nlohmann/json/issues/389)
- Implement indefinite-length types from RFC 7049 [\#387](https://github.com/nlohmann/json/issues/387)
- template parameter "T" is not used in declaring the parameter types of function template [\#386](https://github.com/nlohmann/json/issues/386)
- Serializing json instances containing already serialized string values without escaping [\#385](https://github.com/nlohmann/json/issues/385)
- Add test cases from RFC 7049 [\#384](https://github.com/nlohmann/json/issues/384)
- Add a table of contents to the README file [\#383](https://github.com/nlohmann/json/issues/383)
- Update FAQ section in the guidelines for contributing [\#382](https://github.com/nlohmann/json/issues/382)
- Allow for forward declaring nlohmann::json [\#381](https://github.com/nlohmann/json/issues/381)
- Bug in overflow detection when parsing integers [\#380](https://github.com/nlohmann/json/issues/380)
- A unique name to mention the library? [\#377](https://github.com/nlohmann/json/issues/377)
- Non-unique keys in objects. [\#375](https://github.com/nlohmann/json/issues/375)
- Request: binary serialization/deserialization [\#358](https://github.com/nlohmann/json/issues/358)

- Replace class iterator and const\_iterator by using a single template class to reduce code. [\#395](https://github.com/nlohmann/json/pull/395) ([Bosswestfalen](https://github.com/Bosswestfalen))
- Clang: quiet a warning [\#391](https://github.com/nlohmann/json/pull/391) ([jaredgrubb](https://github.com/jaredgrubb))
- Fix issue \#380: Signed integer overflow check [\#390](https://github.com/nlohmann/json/pull/390) ([qwename](https://github.com/qwename))

## [v2.0.8](https://github.com/nlohmann/json/releases/tag/v2.0.8) (2016-12-02)

[Full Changelog](https://github.com/nlohmann/json/compare/2.0.8...v2.0.8)

## [2.0.8](https://github.com/nlohmann/json/releases/tag/2.0.8) (2016-12-02)

[Full Changelog](https://github.com/nlohmann/json/compare/v2.0.7...2.0.8)

- Reading from file [\#374](https://github.com/nlohmann/json/issues/374)
- Compiler warnings? [\#372](https://github.com/nlohmann/json/issues/372)
- docs: how to release a json object in memory? [\#371](https://github.com/nlohmann/json/issues/371)
- crash in dump [\#370](https://github.com/nlohmann/json/issues/370)
- Coverity issue \(FORWARD\_NULL\) in lexer\(std::istream& s\) [\#368](https://github.com/nlohmann/json/issues/368)
- json::parse on failed stream gets stuck [\#366](https://github.com/nlohmann/json/issues/366)
- Performance improvements [\#365](https://github.com/nlohmann/json/issues/365)
- 'to\_string' is not a member of 'std'  [\#364](https://github.com/nlohmann/json/issues/364)
- Crash in dump\(\) from a static object [\#359](https://github.com/nlohmann/json/issues/359)
- json::parse\(...\) vs json j; j.parse\(...\) [\#357](https://github.com/nlohmann/json/issues/357)
- Hi, is there any method to dump  json to string with the insert order rather than alphabets [\#356](https://github.com/nlohmann/json/issues/356)
- Provide an example of reading from an json with only a key that has an array of strings. [\#354](https://github.com/nlohmann/json/issues/354)
- Request: access with default value. [\#353](https://github.com/nlohmann/json/issues/353)
- {} and \[\] causes parser error. [\#352](https://github.com/nlohmann/json/issues/352)
- Reading a JSON file into a JSON object [\#351](https://github.com/nlohmann/json/issues/351)
- Request: 'emplace\_back' [\#349](https://github.com/nlohmann/json/issues/349)
- Is it possible to stream data through the json parser without storing everything in memory? [\#347](https://github.com/nlohmann/json/issues/347)
- pure virtual conversion operator [\#346](https://github.com/nlohmann/json/issues/346)
- Floating point precision lost [\#345](https://github.com/nlohmann/json/issues/345)
- unit-conversions SIGSEGV on armv7hl [\#303](https://github.com/nlohmann/json/issues/303)
- Coverity scan fails [\#299](https://github.com/nlohmann/json/issues/299)
- Using QString as string type [\#274](https://github.com/nlohmann/json/issues/274)

## [v2.0.7](https://github.com/nlohmann/json/releases/tag/v2.0.7) (2016-11-02)

[Full Changelog](https://github.com/nlohmann/json/compare/v2.0.6...v2.0.7)

- JSON5 [\#348](https://github.com/nlohmann/json/issues/348)
- Check "Parsing JSON is a Minefield" [\#344](https://github.com/nlohmann/json/issues/344)
- Allow hex numbers [\#342](https://github.com/nlohmann/json/issues/342)
- Convert strings to numbers [\#341](https://github.com/nlohmann/json/issues/341)
- ""-operators ignore the length parameter [\#340](https://github.com/nlohmann/json/issues/340)
- JSON into std::tuple [\#339](https://github.com/nlohmann/json/issues/339)
- JSON into vector [\#335](https://github.com/nlohmann/json/issues/335)
- Installing with Homebrew on Mac Errors \(El Capitan\) [\#331](https://github.com/nlohmann/json/issues/331)
- g++ make check results in error [\#312](https://github.com/nlohmann/json/issues/312)
- Cannot convert from 'json' to 'char' [\#276](https://github.com/nlohmann/json/issues/276)
- Please add a Pretty-Print option for arrays to stay always in one line [\#229](https://github.com/nlohmann/json/issues/229)
- Conversion to STL map\<string, vector\<int\>\> gives error [\#220](https://github.com/nlohmann/json/issues/220)
- std::unorderd\_map cannot be used as ObjectType [\#164](https://github.com/nlohmann/json/issues/164)

- fix minor grammar/style issue in README.md [\#336](https://github.com/nlohmann/json/pull/336) ([seeekr](https://github.com/seeekr))

## [v2.0.6](https://github.com/nlohmann/json/releases/tag/v2.0.6) (2016-10-15)

[Full Changelog](https://github.com/nlohmann/json/compare/v2.0.5...v2.0.6)

- How to handle json files? [\#333](https://github.com/nlohmann/json/issues/333)
- This file requires compiler and library support .... [\#332](https://github.com/nlohmann/json/issues/332)
- Segmentation fault on saving json to file [\#326](https://github.com/nlohmann/json/issues/326)
- parse error - unexpected \<uninitialized\> with 2.0.5 [\#325](https://github.com/nlohmann/json/issues/325)
- Add nested object capability to pointers [\#323](https://github.com/nlohmann/json/issues/323)
- Fix usage examples' comments for std::multiset [\#322](https://github.com/nlohmann/json/issues/322)
- json\_unit runs forever when executed in build directory [\#319](https://github.com/nlohmann/json/issues/319)
- Visual studio 2015 update3 true != TRUE [\#317](https://github.com/nlohmann/json/issues/317)
- releasing single header file in compressed format [\#316](https://github.com/nlohmann/json/issues/316)
- json object from std::ifstream [\#315](https://github.com/nlohmann/json/issues/315)

- make has\_mapped\_type struct friendly [\#324](https://github.com/nlohmann/json/pull/324) ([vpetrigo](https://github.com/vpetrigo))
- Fix usage examples' comments for std::multiset [\#321](https://github.com/nlohmann/json/pull/321) ([vasild](https://github.com/vasild))
- Include dir relocation [\#318](https://github.com/nlohmann/json/pull/318) ([ChristophJud](https://github.com/ChristophJud))
- trivial documentation fix [\#313](https://github.com/nlohmann/json/pull/313) ([5tefan](https://github.com/5tefan))

## [v2.0.5](https://github.com/nlohmann/json/releases/tag/v2.0.5) (2016-09-14)

[Full Changelog](https://github.com/nlohmann/json/compare/v2.0.4...v2.0.5)

- \[feature request\]: schema validator and comments [\#311](https://github.com/nlohmann/json/issues/311)
- make json\_benchmarks no longer working in 2.0.4 [\#310](https://github.com/nlohmann/json/issues/310)
- Segmentation fault \(core dumped\) [\#309](https://github.com/nlohmann/json/issues/309)
- No matching member function for call to 'get\_impl' [\#308](https://github.com/nlohmann/json/issues/308)

## [v2.0.4](https://github.com/nlohmann/json/releases/tag/v2.0.4) (2016-09-11)

[Full Changelog](https://github.com/nlohmann/json/compare/v2.0.3...v2.0.4)

- Parsing fails without space at end of file [\#306](https://github.com/nlohmann/json/issues/306)
- json schema validator [\#305](https://github.com/nlohmann/json/issues/305)
- Unused variable warning [\#304](https://github.com/nlohmann/json/issues/304)

## [v2.0.3](https://github.com/nlohmann/json/releases/tag/v2.0.3) (2016-08-31)

[Full Changelog](https://github.com/nlohmann/json/compare/v2.0.2...v2.0.3)

- warning C4706: assignment within conditional expression [\#295](https://github.com/nlohmann/json/issues/295)
- Q: Is it possible to build json tree from already UTF8 encoded values? [\#293](https://github.com/nlohmann/json/issues/293)
- Equality operator results in array when assigned object [\#292](https://github.com/nlohmann/json/issues/292)
- Support for integers not from the range \[-\(2\*\*53\)+1, \(2\*\*53\)-1\] in parser [\#291](https://github.com/nlohmann/json/issues/291)
- Support for iterator-range parsing [\#290](https://github.com/nlohmann/json/issues/290)
- Horribly inconsistent behavior between const/non-const reference in operator \[\] \(\) [\#289](https://github.com/nlohmann/json/issues/289)
- Silently get numbers into smaller types [\#288](https://github.com/nlohmann/json/issues/288)
- Incorrect parsing of large int64\_t numbers [\#287](https://github.com/nlohmann/json/issues/287)
- \[question\]: macro to disable floating point support [\#284](https://github.com/nlohmann/json/issues/284)

- unit-constructor1.cpp: Fix floating point truncation warning [\#300](https://github.com/nlohmann/json/pull/300) ([t-b](https://github.com/t-b))

## [v2.0.2](https://github.com/nlohmann/json/releases/tag/v2.0.2) (2016-07-31)

[Full Changelog](https://github.com/nlohmann/json/compare/v2.0.1...v2.0.2)

- can function dump\(\)  return string in the order I push in the json object ? [\#286](https://github.com/nlohmann/json/issues/286)
- Error on the Mac: Undefined symbols for architecture x86\_64 [\#285](https://github.com/nlohmann/json/issues/285)
- value\(\) does not work with \_json\_pointer types [\#283](https://github.com/nlohmann/json/issues/283)
- Build error for std::int64 [\#282](https://github.com/nlohmann/json/issues/282)
- strings can't be accessed after dump\(\)-\>parse\(\) - type is lost [\#281](https://github.com/nlohmann/json/issues/281)
- Easy serialization of classes [\#280](https://github.com/nlohmann/json/issues/280)
- recursive data structures [\#277](https://github.com/nlohmann/json/issues/277)
- hexify\(\) function emits conversion warning [\#270](https://github.com/nlohmann/json/issues/270)

- let the makefile choose the correct sed [\#279](https://github.com/nlohmann/json/pull/279) ([murinicanor](https://github.com/murinicanor))
- Update hexify to use array lookup instead of ternary \(\#270\) [\#275](https://github.com/nlohmann/json/pull/275) ([dtoma](https://github.com/dtoma))

## [v2.0.1](https://github.com/nlohmann/json/releases/tag/v2.0.1) (2016-06-28)

[Full Changelog](https://github.com/nlohmann/json/compare/v2.0.0...v2.0.1)

- Compilation error. [\#273](https://github.com/nlohmann/json/issues/273)
- dump\(\) performance degradation in v2 [\#272](https://github.com/nlohmann/json/issues/272)

- fixed a tiny typo [\#271](https://github.com/nlohmann/json/pull/271) ([feroldi](https://github.com/feroldi))

## [v2.0.0](https://github.com/nlohmann/json/releases/tag/v2.0.0) (2016-06-23)

[Full Changelog](https://github.com/nlohmann/json/compare/v1.1.0...v2.0.0)

- json::diff generates incorrect patch when removing multiple array elements. [\#269](https://github.com/nlohmann/json/issues/269)
- Docs - What does Json\[key\] return? [\#267](https://github.com/nlohmann/json/issues/267)
- Compiler Errors With JSON.hpp [\#265](https://github.com/nlohmann/json/issues/265)
- Ambiguous push\_back and operator+= overloads [\#263](https://github.com/nlohmann/json/issues/263)
- Preseving order of items in json [\#262](https://github.com/nlohmann/json/issues/262)
- '\' char problem in strings [\#261](https://github.com/nlohmann/json/issues/261)
- VS2015 compile fail [\#260](https://github.com/nlohmann/json/issues/260)
- -Wconversion warning [\#259](https://github.com/nlohmann/json/issues/259)
- Maybe a bug [\#258](https://github.com/nlohmann/json/issues/258)
- Few tests failed on Visual C++ 2015 [\#257](https://github.com/nlohmann/json/issues/257)
- Access keys when iteration with new for loop C++11 [\#256](https://github.com/nlohmann/json/issues/256)
- multiline text values [\#255](https://github.com/nlohmann/json/issues/255)
- Error when using json in g++ [\#254](https://github.com/nlohmann/json/issues/254)
- is the release 2.0? [\#253](https://github.com/nlohmann/json/issues/253)
- concatenate objects [\#252](https://github.com/nlohmann/json/issues/252)
- Encoding [\#251](https://github.com/nlohmann/json/issues/251)
- Unable to build example for constructing json object with stringstreams [\#250](https://github.com/nlohmann/json/issues/250)
- Hexadecimal support [\#249](https://github.com/nlohmann/json/issues/249)
- Update long-term goals [\#246](https://github.com/nlohmann/json/issues/246)
- Contribution To This Json Project [\#245](https://github.com/nlohmann/json/issues/245)
- Trouble using parser with initial dictionary [\#243](https://github.com/nlohmann/json/issues/243)
- Unit test fails when doing a CMake out-of-tree build [\#241](https://github.com/nlohmann/json/issues/241)
- -Wconversion warnings [\#239](https://github.com/nlohmann/json/issues/239)
- Additional integration options [\#237](https://github.com/nlohmann/json/issues/237)
- .get\<std::string\>\(\) works for non spaced string but returns as array for spaced/longer strings [\#236](https://github.com/nlohmann/json/issues/236)
- ambiguous overload for 'push\_back' and 'operator+=' [\#235](https://github.com/nlohmann/json/issues/235)
- Can't use basic\_json::iterator as a base iterator for std::move\_iterator [\#233](https://github.com/nlohmann/json/issues/233)
- json object's creation can freezes execution [\#231](https://github.com/nlohmann/json/issues/231)
- Incorrect dumping of parsed numbers with exponents, but without decimal places [\#230](https://github.com/nlohmann/json/issues/230)
- double values are serialized with commas as decimal points [\#228](https://github.com/nlohmann/json/issues/228)
- Move semantics with std::initializer\_list [\#225](https://github.com/nlohmann/json/issues/225)
- replace emplace [\#224](https://github.com/nlohmann/json/issues/224)
- abort during getline in yyfill [\#223](https://github.com/nlohmann/json/issues/223)
- free\(\): invalid pointer error in GCC 5.2.1 [\#221](https://github.com/nlohmann/json/issues/221)
- Error compile Android NDK  error: 'strtof' is not a member of 'std' [\#219](https://github.com/nlohmann/json/issues/219)
- Wrong link in the README.md [\#217](https://github.com/nlohmann/json/issues/217)
- Wide character strings not supported [\#216](https://github.com/nlohmann/json/issues/216)
- Memory allocations using range-based for loops [\#214](https://github.com/nlohmann/json/issues/214)
- would you like to support gcc 4.8.1?  [\#211](https://github.com/nlohmann/json/issues/211)
- Reading concatenated json's from an istream [\#210](https://github.com/nlohmann/json/issues/210)
- Conflicting typedef of ssize\_t on Windows 32 bit when using Boost.Python [\#204](https://github.com/nlohmann/json/issues/204)
- Inconsistency between operator\[\] and push\_back [\#203](https://github.com/nlohmann/json/issues/203)
- Small bugs in json.hpp \(get\_number\) and unit.cpp \(non-standard integer type test\) [\#199](https://github.com/nlohmann/json/issues/199)
- GCC/clang floating point parsing bug in strtod\(\) [\#195](https://github.com/nlohmann/json/issues/195)
- What is within scope? [\#192](https://github.com/nlohmann/json/issues/192)
- Bugs in miloyip/nativejson-benchmark: roundtrips [\#187](https://github.com/nlohmann/json/issues/187)
- Floating point exceptions [\#181](https://github.com/nlohmann/json/issues/181)
- Integer conversion to unsigned [\#178](https://github.com/nlohmann/json/issues/178)
- map string string fails to compile [\#176](https://github.com/nlohmann/json/issues/176)
- In basic\_json::basic\_json\(const CompatibleArrayType& val\), the requirement of CompatibleArrayType is not strict enough. [\#174](https://github.com/nlohmann/json/issues/174)
- Provide a FAQ [\#163](https://github.com/nlohmann/json/issues/163)
- Implicit assignment to std::string fails [\#144](https://github.com/nlohmann/json/issues/144)

- Fix Issue \#265 [\#266](https://github.com/nlohmann/json/pull/266) ([06needhamt](https://github.com/06needhamt))
- Define CMake/CTest tests [\#247](https://github.com/nlohmann/json/pull/247) ([robertmrk](https://github.com/robertmrk))
- Out of tree builds and a few other miscellaneous CMake cleanups. [\#242](https://github.com/nlohmann/json/pull/242) ([ChrisKitching](https://github.com/ChrisKitching))
- Implement additional integration options [\#238](https://github.com/nlohmann/json/pull/238) ([robertmrk](https://github.com/robertmrk))
- make serialization locale-independent [\#232](https://github.com/nlohmann/json/pull/232) ([nlohmann](https://github.com/nlohmann))
- fixes \#223 by updating README.md [\#227](https://github.com/nlohmann/json/pull/227) ([kevin--](https://github.com/kevin--))
- Use namespace std for int64\_t and uint64\_t [\#226](https://github.com/nlohmann/json/pull/226) ([lv-zheng](https://github.com/lv-zheng))
- Added missing cerrno header to fix ERANGE compile error on android [\#222](https://github.com/nlohmann/json/pull/222) ([Teemperor](https://github.com/Teemperor))
- Corrected readme [\#218](https://github.com/nlohmann/json/pull/218) ([Annihil](https://github.com/Annihil))
- Create PULL\_REQUEST\_TEMPLATE.md [\#213](https://github.com/nlohmann/json/pull/213) ([whackashoe](https://github.com/whackashoe))
- fixed noexcept; added constexpr [\#208](https://github.com/nlohmann/json/pull/208) ([nlohmann](https://github.com/nlohmann))
- Add support for afl-fuzz testing [\#207](https://github.com/nlohmann/json/pull/207) ([mykter](https://github.com/mykter))
- replaced ssize\_t occurrences with auto \(addresses \#204\) [\#205](https://github.com/nlohmann/json/pull/205) ([nlohmann](https://github.com/nlohmann))
- Fixed issue \#199 - Small bugs in json.hpp \(get\_number\) and unit.cpp \(non-standard integer type test\) [\#200](https://github.com/nlohmann/json/pull/200) ([twelsby](https://github.com/twelsby))
- Fix broken link [\#197](https://github.com/nlohmann/json/pull/197) ([vog](https://github.com/vog))
- Issue \#195 - update Travis to Trusty due to gcc/clang strtod\(\) bug [\#196](https://github.com/nlohmann/json/pull/196) ([twelsby](https://github.com/twelsby))
- Issue \#178 - Extending support to full uint64\_t/int64\_t range and unsigned type \(updated\) [\#193](https://github.com/nlohmann/json/pull/193) ([twelsby](https://github.com/twelsby))

## [v1.1.0](https://github.com/nlohmann/json/releases/tag/v1.1.0) (2016-01-24)

[Full Changelog](https://github.com/nlohmann/json/compare/v1.0.0...v1.1.0)

- Small error in pull \#185 [\#194](https://github.com/nlohmann/json/issues/194)
- Bugs in miloyip/nativejson-benchmark: floating-point parsing [\#186](https://github.com/nlohmann/json/issues/186)
- Floating point equality [\#185](https://github.com/nlohmann/json/issues/185)
- Unused variables in catch [\#180](https://github.com/nlohmann/json/issues/180)
- Typo in documentation [\#179](https://github.com/nlohmann/json/issues/179)
- JSON performance benchmark comparision [\#177](https://github.com/nlohmann/json/issues/177)
- Since re2c is often ignored in pull requests, it may make sense to make a contributing.md file [\#175](https://github.com/nlohmann/json/issues/175)
- Question about exceptions [\#173](https://github.com/nlohmann/json/issues/173)
- Android? [\#172](https://github.com/nlohmann/json/issues/172)
- Cannot index by key of type static constexpr const char\* [\#171](https://github.com/nlohmann/json/issues/171)
- Add assertions [\#168](https://github.com/nlohmann/json/issues/168)
- MSVC 2015 build fails when attempting to compare object\_t [\#167](https://github.com/nlohmann/json/issues/167)
- Member detector is not portable [\#166](https://github.com/nlohmann/json/issues/166)
- Unnecessary const\_cast [\#162](https://github.com/nlohmann/json/issues/162)
- Question about get\_ref\(\) [\#128](https://github.com/nlohmann/json/issues/128)
- range based for loop for objects [\#83](https://github.com/nlohmann/json/issues/83)
- Consider submitting this to the Boost Library Incubator [\#66](https://github.com/nlohmann/json/issues/66)

- Fixed Issue \#186 - add strto\(f|d|ld\) overload wrappers, "-0.0" special case and FP trailing zero [\#191](https://github.com/nlohmann/json/pull/191) ([twelsby](https://github.com/twelsby))
- Issue \#185 - remove approx\(\) and use \#pragma to kill warnings [\#190](https://github.com/nlohmann/json/pull/190) ([twelsby](https://github.com/twelsby))
- Fixed Issue \#171 - added two extra template overloads of operator\[\] for T\* arguments [\#189](https://github.com/nlohmann/json/pull/189) ([twelsby](https://github.com/twelsby))
- Fixed issue \#167 - removed operator ValueType\(\) condition for VS2015 [\#188](https://github.com/nlohmann/json/pull/188) ([twelsby](https://github.com/twelsby))
- Implementation of get\_ref\(\) [\#184](https://github.com/nlohmann/json/pull/184) ([dariomt](https://github.com/dariomt))
- Fixed some typos in CONTRIBUTING.md [\#182](https://github.com/nlohmann/json/pull/182) ([nibroc](https://github.com/nibroc))

## [v1.0.0](https://github.com/nlohmann/json/releases/tag/v1.0.0) (2015-12-27)

[Full Changelog](https://github.com/nlohmann/json/compare/v1.0.0-rc1...v1.0.0)

- add key name to exception [\#160](https://github.com/nlohmann/json/issues/160)
- Getting member discarding qualifyer [\#159](https://github.com/nlohmann/json/issues/159)
- basic\_json::iterator::value\(\) output includes quotes while basic\_json::iterator::key\(\) doesn't [\#158](https://github.com/nlohmann/json/issues/158)
- Indexing `const basic_json<>` with `const basic_string<char>` [\#157](https://github.com/nlohmann/json/issues/157)
- token\_type\_name\(token\_type t\): not all control paths return a value [\#156](https://github.com/nlohmann/json/issues/156)
- prevent json.hpp from emitting compiler warnings [\#154](https://github.com/nlohmann/json/issues/154)
- json::parse\(string\) does not check utf8 bom [\#152](https://github.com/nlohmann/json/issues/152)
- unsigned 64bit values output as signed [\#151](https://github.com/nlohmann/json/issues/151)
- Wish feature: json5 [\#150](https://github.com/nlohmann/json/issues/150)
- Unable to compile on MSVC 2015 with SDL checking enabled: This function or variable may be unsafe. [\#149](https://github.com/nlohmann/json/issues/149)
- "Json Object" type does not keep object order [\#148](https://github.com/nlohmann/json/issues/148)
- dump\(\)  convert strings encoded by utf-8 to shift-jis on windows 10.  [\#147](https://github.com/nlohmann/json/issues/147)
- Unable to get field names in a json object [\#145](https://github.com/nlohmann/json/issues/145)
- Question: Is the use of incomplete type correct? [\#138](https://github.com/nlohmann/json/issues/138)
- json.hpp:5746:32: error: 'to\_string' is not a member of 'std' [\#136](https://github.com/nlohmann/json/issues/136)
- Bug in basic\_json::operator\[\] const overload [\#135](https://github.com/nlohmann/json/issues/135)
- wrong enable\_if for const pointer \(instead of pointer-to-const\) [\#134](https://github.com/nlohmann/json/issues/134)
- overload of at\(\) with default value [\#133](https://github.com/nlohmann/json/issues/133)
- Splitting source [\#132](https://github.com/nlohmann/json/issues/132)
- Question about get\_ptr\(\) [\#127](https://github.com/nlohmann/json/issues/127)
- Visual Studio 14 Debug assertion failed [\#125](https://github.com/nlohmann/json/issues/125)
- Memory leak in face of exceptions [\#118](https://github.com/nlohmann/json/issues/118)
- Find and Count for arrays [\#117](https://github.com/nlohmann/json/issues/117)
- dynamically constructing an arbitrarily nested object [\#114](https://github.com/nlohmann/json/issues/114)
- Returning any data type [\#113](https://github.com/nlohmann/json/issues/113)
- Compile error with g++ 4.9.3 cygwin 64-bit [\#112](https://github.com/nlohmann/json/issues/112)
- insert json array issue with gcc4.8.2 [\#110](https://github.com/nlohmann/json/issues/110)
- error: unterminated raw string [\#109](https://github.com/nlohmann/json/issues/109)
- vector\<json\> copy constructor really weird [\#108](https://github.com/nlohmann/json/issues/108)
- \[clang-3.6.2\] string/sstream with number to json issue [\#107](https://github.com/nlohmann/json/issues/107)
- object field accessors [\#103](https://github.com/nlohmann/json/issues/103)
- v8pp and json [\#95](https://github.com/nlohmann/json/issues/95)
- Wishlist [\#65](https://github.com/nlohmann/json/issues/65)
- Windows/Visual Studio \(through 2013\) is unsupported [\#62](https://github.com/nlohmann/json/issues/62)

- Replace sprintf with hex function, this fixes \#149 [\#153](https://github.com/nlohmann/json/pull/153) ([whackashoe](https://github.com/whackashoe))
- Fix character skipping after a surrogate pair [\#146](https://github.com/nlohmann/json/pull/146) ([robertmrk](https://github.com/robertmrk))
- Detect correctly pointer-to-const [\#137](https://github.com/nlohmann/json/pull/137) ([dariomt](https://github.com/dariomt))
- disabled "CopyAssignable" test for MSVC in Debug mode, see \#125 [\#131](https://github.com/nlohmann/json/pull/131) ([dariomt](https://github.com/dariomt))
- removed stream operator for iterator, resolution for \#125 [\#130](https://github.com/nlohmann/json/pull/130) ([dariomt](https://github.com/dariomt))
- fixed typos in comments for examples [\#129](https://github.com/nlohmann/json/pull/129) ([dariomt](https://github.com/dariomt))
- Remove superfluous inefficiency [\#126](https://github.com/nlohmann/json/pull/126) ([d-frey](https://github.com/d-frey))
- remove invalid parameter '-stdlib=libc++' in CMakeLists.txt [\#124](https://github.com/nlohmann/json/pull/124) ([emvivre](https://github.com/emvivre))
- exception-safe object creation, fixes \#118 [\#122](https://github.com/nlohmann/json/pull/122) ([d-frey](https://github.com/d-frey))
- Fix small oversight. [\#121](https://github.com/nlohmann/json/pull/121) ([ColinH](https://github.com/ColinH))
- Overload parse\(\) to accept an rvalue reference [\#120](https://github.com/nlohmann/json/pull/120) ([silverweed](https://github.com/silverweed))
- Use the right variable name in doc string [\#115](https://github.com/nlohmann/json/pull/115) ([whoshuu](https://github.com/whoshuu))

## [v1.0.0-rc1](https://github.com/nlohmann/json/releases/tag/v1.0.0-rc1) (2015-07-26)

[Full Changelog](https://github.com/nlohmann/json/compare/4502e7e51c0569419c26e75fbdd5748170603e54...v1.0.0-rc1)

- Finish documenting the public interface in Doxygen [\#102](https://github.com/nlohmann/json/issues/102)
- Binary string causes numbers to be dumped as hex [\#101](https://github.com/nlohmann/json/issues/101)
- failed to iterator json object with reverse\_iterator [\#100](https://github.com/nlohmann/json/issues/100)
- 'noexcept' : unknown override specifier [\#99](https://github.com/nlohmann/json/issues/99)
- json float parsing problem [\#98](https://github.com/nlohmann/json/issues/98)
- Adjust wording to JSON RFC [\#97](https://github.com/nlohmann/json/issues/97)
- static analysis warnings [\#94](https://github.com/nlohmann/json/issues/94)
- reverse\_iterator operator inheritance problem [\#93](https://github.com/nlohmann/json/issues/93)
- init error [\#92](https://github.com/nlohmann/json/issues/92)
- access by \(const\) reference [\#91](https://github.com/nlohmann/json/issues/91)
- is\_integer and is\_float tests [\#90](https://github.com/nlohmann/json/issues/90)
- Nonstandard integer type [\#89](https://github.com/nlohmann/json/issues/89)
- static library build [\#84](https://github.com/nlohmann/json/issues/84)
- lexer::get\_number return NAN [\#82](https://github.com/nlohmann/json/issues/82)
- MinGW have no std::to\_string [\#80](https://github.com/nlohmann/json/issues/80)
- Incorrect behaviour of basic\_json::count method [\#78](https://github.com/nlohmann/json/issues/78)
- Invoking is\_array\(\) function creates "null" value [\#77](https://github.com/nlohmann/json/issues/77)
- dump\(\) / parse\(\) not idempotent [\#76](https://github.com/nlohmann/json/issues/76)
- Handle infinity and NaN cases [\#70](https://github.com/nlohmann/json/issues/70)
- errors in g++-4.8.1 [\#68](https://github.com/nlohmann/json/issues/68)
- Keys when iterating over objects [\#67](https://github.com/nlohmann/json/issues/67)
- Compilation results in tons of warnings [\#64](https://github.com/nlohmann/json/issues/64)
- Complete brief documentation [\#61](https://github.com/nlohmann/json/issues/61)
- Double quotation mark is not parsed correctly [\#60](https://github.com/nlohmann/json/issues/60)
- Get coverage back to 100% [\#58](https://github.com/nlohmann/json/issues/58)
- erase elements using iterators [\#57](https://github.com/nlohmann/json/issues/57)
- Removing item from array [\#56](https://github.com/nlohmann/json/issues/56)
- Serialize/Deserialize like PHP? [\#55](https://github.com/nlohmann/json/issues/55)
- Numbers as keys [\#54](https://github.com/nlohmann/json/issues/54)
- Why are elements alphabetized on key while iterating? [\#53](https://github.com/nlohmann/json/issues/53)
- Document erase, count, and iterators key and value [\#52](https://github.com/nlohmann/json/issues/52)
- Do not use std::to\_string [\#51](https://github.com/nlohmann/json/issues/51)
- Supported compilers [\#50](https://github.com/nlohmann/json/issues/50)
- Confused about iterating through json objects [\#49](https://github.com/nlohmann/json/issues/49)
- Use non-member begin/end [\#48](https://github.com/nlohmann/json/issues/48)
- Erase key [\#47](https://github.com/nlohmann/json/issues/47)
- Key iterator [\#46](https://github.com/nlohmann/json/issues/46)
- Add count member function [\#45](https://github.com/nlohmann/json/issues/45)
- Problem getting vector \(array\) of strings [\#44](https://github.com/nlohmann/json/issues/44)
- Compilation error due to assuming that private=public [\#43](https://github.com/nlohmann/json/issues/43)
- Use of deprecated implicit copy constructor [\#42](https://github.com/nlohmann/json/issues/42)
- Printing attribute names [\#39](https://github.com/nlohmann/json/issues/39)
- dumping a small number\_float just outputs 0.000000 [\#37](https://github.com/nlohmann/json/issues/37)
- find is error [\#32](https://github.com/nlohmann/json/issues/32)
- Avoid using spaces when encoding without pretty print [\#31](https://github.com/nlohmann/json/issues/31)
- Cannot encode long numbers [\#30](https://github.com/nlohmann/json/issues/30)
- segmentation fault when iterating over empty arrays/objects [\#28](https://github.com/nlohmann/json/issues/28)
- Creating an empty array [\#27](https://github.com/nlohmann/json/issues/27)
- Custom allocator support [\#25](https://github.com/nlohmann/json/issues/25)
- make the type of the used string container customizable [\#20](https://github.com/nlohmann/json/issues/20)
- Improper parsing of JSON string "\\" [\#17](https://github.com/nlohmann/json/issues/17)
- create a header-only version [\#16](https://github.com/nlohmann/json/issues/16)
- Don't return "const values" [\#15](https://github.com/nlohmann/json/issues/15)
- Add to\_string overload for indentation [\#13](https://github.com/nlohmann/json/issues/13)
- string parser does not recognize uncompliant strings [\#12](https://github.com/nlohmann/json/issues/12)
- possible double-free in find function [\#11](https://github.com/nlohmann/json/issues/11)
- UTF-8 encoding/deconding/testing [\#10](https://github.com/nlohmann/json/issues/10)
- move code into namespace [\#9](https://github.com/nlohmann/json/issues/9)
- free functions for explicit objects and arrays in initializer lists [\#8](https://github.com/nlohmann/json/issues/8)
- unique\_ptr for ownership [\#7](https://github.com/nlohmann/json/issues/7)
- Add unit tests [\#4](https://github.com/nlohmann/json/issues/4)
- Drop C++98 support [\#3](https://github.com/nlohmann/json/issues/3)
- Test case coverage [\#2](https://github.com/nlohmann/json/issues/2)
- Runtime error in Travis job [\#1](https://github.com/nlohmann/json/issues/1)

- Keyword 'inline' is useless when member functions are defined in headers [\#87](https://github.com/nlohmann/json/pull/87) ([ahamez](https://github.com/ahamez))
- Remove useless typename [\#86](https://github.com/nlohmann/json/pull/86) ([ahamez](https://github.com/ahamez))
- Avoid warning with Xcode's clang [\#85](https://github.com/nlohmann/json/pull/85) ([ahamez](https://github.com/ahamez))
-  Fix typos [\#73](https://github.com/nlohmann/json/pull/73) ([aqnouch](https://github.com/aqnouch))
- Replace `default_callback` function with `nullptr` and check for null… [\#72](https://github.com/nlohmann/json/pull/72) ([aburgh](https://github.com/aburgh))
- support enum [\#71](https://github.com/nlohmann/json/pull/71) ([likebeta](https://github.com/likebeta))
- Fix performance regression introduced with the parsing callback feature. [\#69](https://github.com/nlohmann/json/pull/69) ([aburgh](https://github.com/aburgh))
- Improve the implementations of the comparission-operators [\#63](https://github.com/nlohmann/json/pull/63) ([Florianjw](https://github.com/Florianjw))
- Fix compilation of json\_unit with GCC 5 [\#59](https://github.com/nlohmann/json/pull/59) ([dkopecek](https://github.com/dkopecek))
- Parse streams incrementally. [\#40](https://github.com/nlohmann/json/pull/40) ([aburgh](https://github.com/aburgh))
- Feature/small float serialization [\#38](https://github.com/nlohmann/json/pull/38) ([jrandall](https://github.com/jrandall))
- template version with re2c scanner [\#36](https://github.com/nlohmann/json/pull/36) ([nlohmann](https://github.com/nlohmann))
- more descriptive documentation in example [\#33](https://github.com/nlohmann/json/pull/33) ([luxe](https://github.com/luxe))
- Fix string conversion under Clang [\#26](https://github.com/nlohmann/json/pull/26) ([wancw](https://github.com/wancw))
- Fixed dumping of strings [\#24](https://github.com/nlohmann/json/pull/24) ([Teemperor](https://github.com/Teemperor))
- Added a remark to the readme that coverage is GCC only for now [\#23](https://github.com/nlohmann/json/pull/23) ([Teemperor](https://github.com/Teemperor))
- Unicode escaping [\#22](https://github.com/nlohmann/json/pull/22) ([Teemperor](https://github.com/Teemperor))
- Implemented the JSON spec for string parsing for everything but the \uXXXX escaping [\#21](https://github.com/nlohmann/json/pull/21) ([Teemperor](https://github.com/Teemperor))
- add the std iterator typedefs to iterator and const\_iterator [\#19](https://github.com/nlohmann/json/pull/19) ([kirkshoop](https://github.com/kirkshoop))
- Fixed escaped quotes [\#18](https://github.com/nlohmann/json/pull/18) ([Teemperor](https://github.com/Teemperor))
- Fix double delete on std::bad\_alloc exception [\#14](https://github.com/nlohmann/json/pull/14) ([elliotgoodrich](https://github.com/elliotgoodrich))
- Added CMake and lcov [\#6](https://github.com/nlohmann/json/pull/6) ([Teemperor](https://github.com/Teemperor))
- Version 2.0 [\#5](https://github.com/nlohmann/json/pull/5) ([nlohmann](https://github.com/nlohmann))



\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
