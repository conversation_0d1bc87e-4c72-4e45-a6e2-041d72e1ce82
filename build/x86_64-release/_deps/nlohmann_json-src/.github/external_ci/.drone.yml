kind: pipeline
name: test-on-arm64

platform:
  arch: arm64

steps:
- name: build
  image: gcc
  commands:
  - wget https://github.com/Kitware/CMake/releases/download/v3.20.2/cmake-3.20.2.tar.gz
  - tar xfz cmake-3.20.2.tar.gz
  - cd cmake-3.20.2
  - ./configure
  - make cmake ctest -j10
  - cd ..
  - mkdir build
  - cd build
  - ../cmake-3.20.2/bin/cmake .. -DJSON_FastTests=ON
  - make -j10
  - cd tests
  - ../../cmake-3.20.2/bin/ctest -j10
