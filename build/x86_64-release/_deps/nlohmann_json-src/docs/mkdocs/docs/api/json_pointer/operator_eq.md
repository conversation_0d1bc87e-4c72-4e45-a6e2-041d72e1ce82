# <small>nlohmann::json_pointer::</small>operator==

```cpp
// until C++20
template<typename RefStringTypeLhs, typename RefStringTypeRhs>
bool operator==(
    const json_pointer<RefStringTypeLhs>& lhs,
    const json_pointer<RefStringTypeRhs>& rhs) noexcept;            // (1)

template<typename RefStringTypeLhs, typename StringType>
bool operator==(
    const json_pointer<RefStringTypeLhs>& lhs,
    const StringType& rhs);                                         // (2)

template<typename RefStringTypeRhs, typename StringType>
bool operator==(
    const StringType& lhs,
    const json_pointer<RefStringTypeRhs>& rhs);                     // (2)

// since C++20
class json_pointer {
    template<typename RefStringTypeRhs>
    bool operator==(
        const json_pointer<RefStringTypeRhs>& rhs) const noexcept;  // (1)

    bool operator==(const string_t& rhs) const;                     // (2)
};
```

1. Compares two JSON pointers for equality by comparing their reference tokens.

2. Compares a JSON pointer and a string or a string and a JSON pointer for equality by converting the string to a JSON
   pointer and comparing the JSON pointers according to 1.

## Template parameters

`RefStringTypeLhs`, `RefStringTypeRhs`
:   the string type of the left-hand side or right-hand side JSON pointer, respectively

`StringType`
:   the string type derived from the `json_pointer` operand ([`json_pointer::string_t`](string_t.md))

## Parameters

`lhs` (in)
:   first value to consider

`rhs` (in)
:   second value to consider

## Return value

whether the values `lhs`/`*this` and `rhs` are equal

## Exception safety

1. No-throw guarantee: this function never throws exceptions.
2. Strong exception safety: if an exception occurs, the original value stays intact.

## Exceptions

1. (none)
2. The function can throw the following exceptions:
   - Throws [parse_error.107](../../home/<USER>
     nonempty and does not begin with a slash (`/`); see example below.
   - Throws [parse_error.108](../../home/<USER>
     pointer `s` is not followed by `0` (representing `~`) or `1` (representing `/`); see example below.

## Complexity

Constant if `lhs` and `rhs` differ in the number of reference tokens, otherwise linear in the number of reference
tokens.

## Notes

!!! warning "Deprecation"

    Overload 2 is deprecated and will be removed in a future major version release.

## Examples

??? example "Example: (1) Comparing JSON pointers"

    The example demonstrates comparing JSON pointers.
        
    ```cpp
    --8<-- "examples/json_pointer__operator__equal.cpp"
    ```
    
    Output:
    
    ```
    --8<-- "examples/json_pointer__operator__equal.output"
    ```

??? example "Example: (2) Comparing JSON pointers and strings"

    The example demonstrates comparing JSON pointers and strings, and when doing so may raise an exception.
        
    ```cpp
    --8<-- "examples/json_pointer__operator__equal_stringtype.cpp"
    ```
    
    Output:
    
    ```
    --8<-- "examples/json_pointer__operator__equal_stringtype.output"
    ```

## Version history

1. Added in version 2.1.0. Added C++20 member functions in version 3.11.2.
2. Added for backward compatibility and deprecated in version 3.11.2.
