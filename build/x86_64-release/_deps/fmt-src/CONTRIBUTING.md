Contributing to {fmt}
=====================

By submitting a pull request or a patch, you represent that you have the right
to license your contribution to the {fmt} project owners and the community,
agree that your contributions are licensed under the {fmt} license, and agree
to future changes to the licensing.

All C++ code must adhere to [Google C++ Style Guide](
https://google.github.io/styleguide/cppguide.html) with the following
exceptions:

* Exceptions are permitted
* snake_case should be used instead of UpperCamelCase for function and type
  names

All documentation must adhere to the [Google Developer Documentation Style
Guide](https://developers.google.com/style).

Thanks for contributing!
