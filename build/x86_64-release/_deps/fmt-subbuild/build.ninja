# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.31

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: fmt-populate
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild/

#############################################
# Utility command for fmt-populate

build fmt-populate: phony CMakeFiles/fmt-populate CMakeFiles/fmt-populate-complete fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-done fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-build fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-configure fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-download fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-install fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-mkdir fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-patch fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-test fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-update


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild && /opt/homebrew/bin/ccmake -S/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild && /opt/homebrew/bin/cmake --regenerate-during-build -S/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Phony custom command for CMakeFiles/fmt-populate

build CMakeFiles/fmt-populate | ${cmake_ninja_workdir}CMakeFiles/fmt-populate: phony CMakeFiles/fmt-populate-complete


#############################################
# Custom command for CMakeFiles/fmt-populate-complete

build CMakeFiles/fmt-populate-complete fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-done | ${cmake_ninja_workdir}CMakeFiles/fmt-populate-complete ${cmake_ninja_workdir}fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-done: CUSTOM_COMMAND fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-install fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-mkdir fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-download fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-update fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-patch fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-configure fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-build fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-install fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-test
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild && /opt/homebrew/bin/cmake -E make_directory /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild/CMakeFiles && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild/CMakeFiles/fmt-populate-complete && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-done
  DESC = Completed 'fmt-populate'
  restat = 1


#############################################
# Custom command for fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-build

build fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-build | ${cmake_ninja_workdir}fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-build: CUSTOM_COMMAND fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-configure
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-build && /opt/homebrew/bin/cmake -E echo_append && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-build
  DESC = No build step for 'fmt-populate'
  restat = 1


#############################################
# Custom command for fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-configure

build fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-configure | ${cmake_ninja_workdir}fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-configure: CUSTOM_COMMAND fmt-populate-prefix/tmp/fmt-populate-cfgcmd.txt fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-patch
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-build && /opt/homebrew/bin/cmake -E echo_append && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-configure
  DESC = No configure step for 'fmt-populate'
  restat = 1


#############################################
# Custom command for fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-download

build fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-download | ${cmake_ninja_workdir}fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-download: CUSTOM_COMMAND fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-gitinfo.txt fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-mkdir
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps && /opt/homebrew/bin/cmake -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild/fmt-populate-prefix/tmp/fmt-populate-gitclone.cmake && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-download
  DESC = Performing download step (git clone) for 'fmt-populate'
  pool = console
  restat = 1


#############################################
# Custom command for fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-install

build fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-install | ${cmake_ninja_workdir}fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-install: CUSTOM_COMMAND fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-build
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-build && /opt/homebrew/bin/cmake -E echo_append && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-install
  DESC = No install step for 'fmt-populate'
  restat = 1


#############################################
# Custom command for fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-mkdir

build fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-mkdir | ${cmake_ninja_workdir}fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-mkdir: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild && /opt/homebrew/bin/cmake -Dcfgdir= -P /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild/fmt-populate-prefix/tmp/fmt-populate-mkdirs.cmake && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-mkdir
  DESC = Creating directories for 'fmt-populate'
  restat = 1


#############################################
# Custom command for fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-patch

build fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-patch | ${cmake_ninja_workdir}fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-patch: CUSTOM_COMMAND fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-patch-info.txt fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-update
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild && /opt/homebrew/bin/cmake -E echo_append && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-patch
  DESC = No patch step for 'fmt-populate'
  pool = console
  restat = 1


#############################################
# Custom command for fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-test

build fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-test | ${cmake_ninja_workdir}fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-test: CUSTOM_COMMAND fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-install
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-build && /opt/homebrew/bin/cmake -E echo_append && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-test
  DESC = No test step for 'fmt-populate'
  restat = 1


#############################################
# Custom command for fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-update

build fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-update | ${cmake_ninja_workdir}fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-update: CUSTOM_COMMAND fmt-populate-prefix/tmp/fmt-populate-gitupdate.cmake fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-update-info.txt fmt-populate-prefix/src/fmt-populate-stamp/fmt-populate-download
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src && /opt/homebrew/bin/cmake -Dcan_fetch=YES -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild/fmt-populate-prefix/tmp/fmt-populate-gitupdate.cmake
  DESC = Performing update step for 'fmt-populate'
  pool = console

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild

build codegen: phony

# =============================================================================

#############################################
# Folder: /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-subbuild

build all: phony fmt-populate

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake /opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /opt/homebrew/share/cmake/Modules/ExternalProject.cmake /opt/homebrew/share/cmake/Modules/ExternalProject/PatchInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/RepositoryInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/UpdateInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/cfgcmd.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/gitclone.cmake.in /opt/homebrew/share/cmake/Modules/ExternalProject/gitupdate.cmake.in /opt/homebrew/share/cmake/Modules/ExternalProject/mkdirs.cmake.in /opt/homebrew/share/cmake/Modules/ExternalProject/shared_internal_commands.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin-Initialize.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake /opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.31.5/CMakeSystem.cmake CMakeLists.txt fmt-populate-prefix/tmp/fmt-populate-mkdirs.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake /opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /opt/homebrew/share/cmake/Modules/ExternalProject.cmake /opt/homebrew/share/cmake/Modules/ExternalProject/PatchInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/RepositoryInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/UpdateInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/cfgcmd.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/gitclone.cmake.in /opt/homebrew/share/cmake/Modules/ExternalProject/gitupdate.cmake.in /opt/homebrew/share/cmake/Modules/ExternalProject/mkdirs.cmake.in /opt/homebrew/share/cmake/Modules/ExternalProject/shared_internal_commands.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin-Initialize.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake /opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.31.5/CMakeSystem.cmake CMakeLists.txt fmt-populate-prefix/tmp/fmt-populate-mkdirs.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
