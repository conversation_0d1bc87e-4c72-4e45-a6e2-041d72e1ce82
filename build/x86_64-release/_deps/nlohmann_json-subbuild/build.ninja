# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.31

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: nlohmann_json-populate
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild/

#############################################
# Utility command for nlohmann_json-populate

build nlohmann_json-populate: phony CMakeFiles/nlohmann_json-populate CMakeFiles/nlohmann_json-populate-complete nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-done nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-build nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-configure nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-download nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-install nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-mkdir nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-patch nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-test nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-update


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild && /opt/homebrew/bin/ccmake -S/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild && /opt/homebrew/bin/cmake --regenerate-during-build -S/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Phony custom command for CMakeFiles/nlohmann_json-populate

build CMakeFiles/nlohmann_json-populate | ${cmake_ninja_workdir}CMakeFiles/nlohmann_json-populate: phony CMakeFiles/nlohmann_json-populate-complete


#############################################
# Custom command for CMakeFiles/nlohmann_json-populate-complete

build CMakeFiles/nlohmann_json-populate-complete nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-done | ${cmake_ninja_workdir}CMakeFiles/nlohmann_json-populate-complete ${cmake_ninja_workdir}nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-done: CUSTOM_COMMAND nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-install nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-mkdir nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-download nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-update nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-patch nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-configure nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-build nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-install nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-test
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild && /opt/homebrew/bin/cmake -E make_directory /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild/CMakeFiles && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild/CMakeFiles/nlohmann_json-populate-complete && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild/nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-done
  DESC = Completed 'nlohmann_json-populate'
  restat = 1


#############################################
# Custom command for nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-build

build nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-build | ${cmake_ninja_workdir}nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-build: CUSTOM_COMMAND nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-configure
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-build && /opt/homebrew/bin/cmake -E echo_append && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild/nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-build
  DESC = No build step for 'nlohmann_json-populate'
  restat = 1


#############################################
# Custom command for nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-configure

build nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-configure | ${cmake_ninja_workdir}nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-configure: CUSTOM_COMMAND nlohmann_json-populate-prefix/tmp/nlohmann_json-populate-cfgcmd.txt nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-patch
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-build && /opt/homebrew/bin/cmake -E echo_append && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild/nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-configure
  DESC = No configure step for 'nlohmann_json-populate'
  restat = 1


#############################################
# Custom command for nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-download

build nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-download | ${cmake_ninja_workdir}nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-download: CUSTOM_COMMAND nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-gitinfo.txt nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-mkdir
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps && /opt/homebrew/bin/cmake -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild/nlohmann_json-populate-prefix/tmp/nlohmann_json-populate-gitclone.cmake && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild/nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-download
  DESC = Performing download step (git clone) for 'nlohmann_json-populate'
  pool = console
  restat = 1


#############################################
# Custom command for nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-install

build nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-install | ${cmake_ninja_workdir}nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-install: CUSTOM_COMMAND nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-build
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-build && /opt/homebrew/bin/cmake -E echo_append && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild/nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-install
  DESC = No install step for 'nlohmann_json-populate'
  restat = 1


#############################################
# Custom command for nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-mkdir

build nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-mkdir | ${cmake_ninja_workdir}nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-mkdir: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild && /opt/homebrew/bin/cmake -Dcfgdir= -P /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild/nlohmann_json-populate-prefix/tmp/nlohmann_json-populate-mkdirs.cmake && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild/nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-mkdir
  DESC = Creating directories for 'nlohmann_json-populate'
  restat = 1


#############################################
# Custom command for nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-patch

build nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-patch | ${cmake_ninja_workdir}nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-patch: CUSTOM_COMMAND nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-patch-info.txt nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-update
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild && /opt/homebrew/bin/cmake -E echo_append && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild/nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-patch
  DESC = No patch step for 'nlohmann_json-populate'
  pool = console
  restat = 1


#############################################
# Custom command for nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-test

build nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-test | ${cmake_ninja_workdir}nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-test: CUSTOM_COMMAND nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-install
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-build && /opt/homebrew/bin/cmake -E echo_append && /opt/homebrew/bin/cmake -E touch /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild/nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-test
  DESC = No test step for 'nlohmann_json-populate'
  restat = 1


#############################################
# Custom command for nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-update

build nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-update | ${cmake_ninja_workdir}nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-update: CUSTOM_COMMAND nlohmann_json-populate-prefix/tmp/nlohmann_json-populate-gitupdate.cmake nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-update-info.txt nlohmann_json-populate-prefix/src/nlohmann_json-populate-stamp/nlohmann_json-populate-download
  COMMAND = cd /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-src && /opt/homebrew/bin/cmake -Dcan_fetch=YES -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild/nlohmann_json-populate-prefix/tmp/nlohmann_json-populate-gitupdate.cmake
  DESC = Performing update step for 'nlohmann_json-populate'
  pool = console

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild

build codegen: phony

# =============================================================================

#############################################
# Folder: /Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild

build all: phony nlohmann_json-populate

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake /opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /opt/homebrew/share/cmake/Modules/ExternalProject.cmake /opt/homebrew/share/cmake/Modules/ExternalProject/PatchInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/RepositoryInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/UpdateInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/cfgcmd.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/gitclone.cmake.in /opt/homebrew/share/cmake/Modules/ExternalProject/gitupdate.cmake.in /opt/homebrew/share/cmake/Modules/ExternalProject/mkdirs.cmake.in /opt/homebrew/share/cmake/Modules/ExternalProject/shared_internal_commands.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin-Initialize.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake /opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.31.5/CMakeSystem.cmake CMakeLists.txt nlohmann_json-populate-prefix/tmp/nlohmann_json-populate-mkdirs.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake /opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /opt/homebrew/share/cmake/Modules/ExternalProject.cmake /opt/homebrew/share/cmake/Modules/ExternalProject/PatchInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/RepositoryInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/UpdateInfo.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/cfgcmd.txt.in /opt/homebrew/share/cmake/Modules/ExternalProject/gitclone.cmake.in /opt/homebrew/share/cmake/Modules/ExternalProject/gitupdate.cmake.in /opt/homebrew/share/cmake/Modules/ExternalProject/mkdirs.cmake.in /opt/homebrew/share/cmake/Modules/ExternalProject/shared_internal_commands.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin-Initialize.cmake /opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake /opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.31.5/CMakeSystem.cmake CMakeLists.txt nlohmann_json-populate-prefix/tmp/nlohmann_json-populate-mkdirs.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
