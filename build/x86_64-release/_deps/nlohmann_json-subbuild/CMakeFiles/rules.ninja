# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.31

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: nlohmann_json-populate
# Configurations: 
# =============================================================================
# =============================================================================

#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = /opt/homebrew/bin/cmake --regenerate-during-build -S/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild -B/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlohmann_json-subbuild
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = /opt/homebrew/bin/ninja $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = /opt/homebrew/bin/ninja -t targets
  description = All primary targets available:

