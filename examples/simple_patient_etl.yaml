# Example: Simple Patient ETL Configuration
# This configuration demonstrates extracting patient data from a CSV file
# and loading it into an OMOP CDM person table

# Source Configuration
source:
  type: csv
  encoding: UTF-8
  delimiter: ","
  quote_char: '"'
  skip_header: true

  files:
    patients:
      path: ${DATA_PATH}/patients_sample.csv
      columns:
        - patient_id
        - first_name
        - last_name
        - date_of_birth
        - gender
        - race
        - ethnicity
        - ssn
        - address
        - city
        - state
        - zip_code

# Target Configuration
target:
  type: postgresql
  connection:
    host: ${OMOP_DB_HOST:-localhost}
    port: ${OMOP_DB_PORT:-5432}
    database: ${OMOP_DB_NAME:-omop_cdm}
    username: ${OMOP_DB_USER}
    password: ${OMOP_DB_PASSWORD}
    schema: public

# Job Configuration
job_config:
  batch_size: 1000
  parallel_threads: 2
  error_tolerance: 0.01
  enable_checkpointing: true
  checkpoint_interval: 5000

# Mapping Configuration
mappings:
  person:
    source_file: patients
    target_table: person

    # Pre-processing validations
    validations:
      - field: patient_id
        rules:
          - type: not_null
          - type: unique
      - field: date_of_birth
        rules:
          - type: not_null
          - type: date_format
            format: "%Y-%m-%d"
          - type: date_range
            min: "1900-01-01"
            max: "2024-12-31"

    # Field transformations
    transformations:
      # Direct mapping
      - source_column: patient_id
        target_column: person_id
        type: direct

      # Gender concept mapping
      - source_column: gender
        target_column: gender_concept_id
        type: vocabulary_mapping
        vocabulary: Gender
        mapping:
          "M": 8507      # Male
          "F": 8532      # Female
          "O": 8521      # Other
          "U": 0         # Unknown
        default: 0

      # Date extraction
      - source_column: date_of_birth
        target_columns:
          - name: year_of_birth
            type: extract_year
          - name: month_of_birth
            type: extract_month
          - name: day_of_birth
            type: extract_day
          - name: birth_datetime
            type: datetime_format
            format: "%Y-%m-%d 00:00:00"

      # Race concept mapping
      - source_column: race
        target_column: race_concept_id
        type: vocabulary_mapping
        vocabulary: Race
        mapping:
          "White": 8527
          "Black or African American": 8516
          "Asian": 8515
          "American Indian or Alaska Native": 8657
          "Native Hawaiian or Other Pacific Islander": 8557
          "Unknown": 0
        default: 0

      # Ethnicity concept mapping
      - source_column: ethnicity
        target_column: ethnicity_concept_id
        type: vocabulary_mapping
        vocabulary: Ethnicity
        mapping:
          "Hispanic or Latino": 38003563
          "Not Hispanic or Latino": 38003564
          "Unknown": 0
        default: 0

      # Generate source value
      - source_column: patient_id
        target_column: person_source_value
        type: concatenate
        prefix: "PAT_"

      # Fixed values
      - target_column: location_id
        type: fixed_value
        value: 1

      - target_column: provider_id
        type: fixed_value
        value: null

      - target_column: care_site_id
        type: fixed_value
        value: null

      # Composite transformations
      - source_columns: [gender, race, ethnicity]
        target_column: gender_source_value
        type: custom_transform
        function: combine_demographics
        delimiter: "|"

    # Post-processing
    post_processing:
      - type: duplicate_check
        columns: [person_id]
        action: skip  # or 'update'

      - type: referential_integrity
        checks:
          - column: gender_concept_id
            reference_table: concept
            reference_column: concept_id
          - column: race_concept_id
            reference_table: concept
            reference_column: concept_id

# Logging Configuration
logging:
  level: INFO
  file:
    enabled: true
    path: ${LOG_PATH}/simple_patient_etl.log
    max_size: 10MB
    max_backups: 5

  # Log specific events
  events:
    - extraction_start
    - extraction_complete
    - transformation_errors
    - load_complete
    - validation_failures

# Notification Configuration (optional)
notifications:
  email:
    enabled: false
    smtp_server: smtp.example.com
    smtp_port: 587
    from: <EMAIL>
    to:
      - <EMAIL>
    on_events:
      - job_complete
      - job_failed

  webhook:
    enabled: false
    url: https://api.example.com/etl/webhook
    headers:
      Authorization: "Bearer ${WEBHOOK_TOKEN}"
    on_events:
      - job_complete
      - job_failed