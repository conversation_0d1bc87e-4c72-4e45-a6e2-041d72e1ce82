/**
 * @file mysql_connector_test.cpp
 * @brief Unit tests for MySQL connector functionality
 * <AUTHOR> ETL Team
 * @date 2024
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/mysql_connector.h"
#include "common/exceptions.h"
#include <cstring>
#include <vector>
#include <chrono>

using namespace omop::extract;
using namespace omop::common;
using ::testing::_;
using ::testing::Return;

// Define MySQL types and constants for testing
typedef char my_bool;
typedef unsigned long long my_ulonglong;
typedef char** MYSQL_ROW;

enum enum_field_types {
    MYSQL_TYPE_DECIMAL, MYSQL_TYPE_TINY,
    MYSQL_TYPE_SHORT, MYSQL_TYPE_LONG,
    MYSQL_TYPE_FLOAT, MYSQL_TYPE_DOUBLE,
    MYSQL_TYPE_NULL, MYSQL_TYPE_TIMESTAMP,
    MYSQL_TYPE_LONGLONG, MYSQL_TYPE_INT24,
    MYSQL_TYPE_DATE, MYSQL_TYPE_TIME,
    MYSQL_TYPE_DATETIME, MYSQL_TYPE_YEAR,
    MYSQL_TYPE_NEWDATE, MYSQL_TYPE_VARCHAR,
    MYSQL_TYPE_BIT
};

enum enum_mysql_stmt_state {
    MYSQL_STMT_INIT_DONE = 1,
    MYSQL_STMT_PREPARE_DONE,
    MYSQL_STMT_EXECUTE_DONE,
    MYSQL_STMT_FETCH_DONE
};

enum mysql_option {
    MYSQL_OPT_CONNECT_TIMEOUT,
    MYSQL_OPT_COMPRESS,
    MYSQL_OPT_NAMED_PIPE,
    MYSQL_INIT_COMMAND,
    MYSQL_READ_DEFAULT_FILE,
    MYSQL_READ_DEFAULT_GROUP,
    MYSQL_SET_CHARSET_DIR,
    MYSQL_SET_CHARSET_NAME,
    MYSQL_OPT_LOCAL_INFILE,
    MYSQL_OPT_PROTOCOL,
    MYSQL_SHARED_MEMORY_BASE_NAME,
    MYSQL_OPT_READ_TIMEOUT,
    MYSQL_OPT_WRITE_TIMEOUT,
    MYSQL_OPT_USE_RESULT,
    MYSQL_OPT_USE_REMOTE_CONNECTION,
    MYSQL_OPT_USE_EMBEDDED_CONNECTION,
    MYSQL_OPT_GUESS_CONNECTION,
    MYSQL_SET_CLIENT_IP,
    MYSQL_SECURE_AUTH,
    MYSQL_REPORT_DATA_TRUNCATION,
    MYSQL_OPT_RECONNECT,
    MYSQL_OPT_SSL_VERIFY_SERVER_CERT,
    MYSQL_PLUGIN_DIR,
    MYSQL_DEFAULT_AUTH,
    MYSQL_OPT_BIND,
    MYSQL_OPT_SSL_KEY,
    MYSQL_OPT_SSL_CERT,
    MYSQL_OPT_SSL_CA,
    MYSQL_OPT_SSL_CAPATH,
    MYSQL_OPT_SSL_CIPHER,
    MYSQL_OPT_SSL_CRL,
    MYSQL_OPT_SSL_CRLPATH,
    MYSQL_OPT_CONNECT_ATTR_RESET,
    MYSQL_OPT_CONNECT_ATTR_ADD,
    MYSQL_OPT_CONNECT_ATTR_DELETE,
    MYSQL_SERVER_PUBLIC_KEY,
    MYSQL_ENABLE_CLEARTEXT_PLUGIN,
    MYSQL_OPT_CAN_HANDLE_EXPIRED_PASSWORDS
};

#define MYSQL_NO_DATA 100

// ODBC types for compatibility
typedef void* SQLHSTMT;
typedef unsigned short SQLUSMALLINT;
typedef short SQLSMALLINT;
typedef void* SQLPOINTER;
typedef long SQLLEN;
#define SQL_SUCCESS 0

struct MYSQL_FIELD {
    char* name;
    char* org_name;
    char* table;
    char* org_table;
    char* db;
    char* catalog;
    char* def;
    unsigned long length;
    unsigned long max_length;
    unsigned int name_length;
    unsigned int org_name_length;
    unsigned int table_length;
    unsigned int org_table_length;
    unsigned int db_length;
    unsigned int catalog_length;
    unsigned int def_length;
    unsigned int flags;
    unsigned int decimals;
    unsigned int charsetnr;
    enum enum_field_types type;
    void* extension;
};

struct MYSQL_BIND {
    unsigned long* length;
    my_bool* is_null;
    void* buffer;
    my_bool* error;
    unsigned char* row_ptr;
    void (*store_param_func)(void*, MYSQL_BIND*, unsigned char*, unsigned char**);
    void (*fetch_result)(MYSQL_BIND*, MYSQL_FIELD*, unsigned char**);
    void (*skip_result)(MYSQL_BIND*, MYSQL_FIELD*, unsigned char**);
    unsigned long buffer_length;
    unsigned long offset;
    unsigned long length_value;
    unsigned int param_number;
    unsigned int pack_length;
    enum enum_field_types buffer_type;
    my_bool error_value;
    my_bool is_unsigned;
    my_bool long_data_used;
    my_bool is_null_value;
    void* extension;
};

struct MYSQL_DATA {
    struct MYSQL_ROWS* data;
    void* embedded_info;
    void* alloc;
    my_ulonglong rows;
    unsigned int fields;
};

struct MYSQL_ROWS {
    struct MYSQL_ROWS* next;
    MYSQL_ROW data;
    unsigned long length;
};

struct MEM_ROOT {
    void* free;
    void* used;
    void* pre_alloc;
    size_t min_malloc;
    size_t block_size;
    unsigned int block_num;
    unsigned int first_block_usage;
    void (*error_handler)(void);
};

// Forward declarations
struct MYSQL;
struct MYSQL_STMT;
struct MYSQL_RES;

// Mock MySQL structures and functions
struct MockMYSQL {
    unsigned int thread_id;
    char* host;
    char* user;
    char* passwd;
    char* unix_socket;
    char* server_version;
    char* host_info;
    char* info;
    char* db;
    unsigned int port;
    unsigned int client_flag;
    unsigned int server_capabilities;
    unsigned int protocol_version;
    unsigned int field_count;
    unsigned int server_status;
    unsigned int server_language;
    unsigned int warning_count;
};

struct MockMYSQL_STMT {
    MYSQL* mysql;
    unsigned long stmt_id;
    unsigned long flags;
    enum enum_mysql_stmt_state state;
    MYSQL_FIELD* fields;
    unsigned int field_count;
    unsigned int param_count;
    my_bool bind_param_done;
    my_bool bind_result_done;
    MYSQL_BIND* params;
    MYSQL_BIND* bind;
    MYSQL_DATA result;
    my_ulonglong affected_rows;
    my_ulonglong insert_id;
    int mysql_errno;
    char error[512];
    char sqlstate[6];
};

struct MockMYSQL_RES {
    my_ulonglong row_count;
    unsigned int field_count;
    unsigned int current_field;
    MYSQL_FIELD* fields;
    MYSQL_DATA* data;
    MYSQL_ROWS* data_cursor;
    MEM_ROOT field_alloc;
    MYSQL_ROW row;
    MYSQL_ROW current_row;
    unsigned long* lengths;
    MYSQL* handle;
    my_bool eof;
};

// Mock data storage
static MYSQL mock_mysql;
static MYSQL_STMT mock_stmt;
static MYSQL_RES mock_result;
static int mock_errno = 0;
static std::string mock_error = "";
static my_bool mock_connection_status = 1;
static MYSQL_FIELD* mock_fields = nullptr;
static int mock_field_count = 0;
static std::vector<std::vector<std::string>> mock_data;
static int mock_current_row = -1;

// Mock MySQL C API functions
extern "C" {
    MYSQL* mysql_init(MYSQL*) {
        memset(&mock_mysql, 0, sizeof(mock_mysql));
        return &mock_mysql;
    }
    
    void mysql_close(MYSQL*) {}
    
    MYSQL* mysql_real_connect(MYSQL* mysql, const char* host, const char* user,
                             const char* passwd, const char* db, unsigned int port,
                             const char* unix_socket, unsigned long client_flag) {
        return mock_connection_status ? mysql : nullptr;
    }
    
    const char* mysql_error(MYSQL*) {
        return mock_error.c_str();
    }
    
    unsigned int mysql_errno(MYSQL*) {
        return mock_errno;
    }
    
    int mysql_ping(MYSQL*) {
        return mock_connection_status ? 0 : 1;
    }
    
    int mysql_set_character_set(MYSQL*, const char*) {
        return 0;
    }
    
    const char* mysql_get_server_info(MYSQL*) {
        return "8.0.32";
    }
    
    int mysql_query(MYSQL*, const char*) {
        return mock_errno;
    }
    
    my_ulonglong mysql_affected_rows(MYSQL*) {
        return mock_data.size();
    }
    
    MYSQL_STMT* mysql_stmt_init(MYSQL* mysql) {
        memset(&mock_stmt, 0, sizeof(mock_stmt));
        mock_stmt.mysql = mysql;
        return &mock_stmt;
    }
    
    int mysql_stmt_prepare(MYSQL_STMT* stmt, const char* query, unsigned long length) {
        stmt->param_count = 0;
        // Count parameters (simple implementation)
        const char* p = query;
        while ((p = strstr(p, "?")) != nullptr) {
            stmt->param_count++;
            p++;
        }
        return 0;
    }
    
    int mysql_stmt_execute(MYSQL_STMT*) {
        return 0;
    }
    
    int mysql_stmt_close(MYSQL_STMT*) {
        return 0;
    }
    
    const char* mysql_stmt_error(MYSQL_STMT*) {
        return mock_error.c_str();
    }
    
    unsigned int mysql_stmt_errno(MYSQL_STMT*) {
        return mock_errno;
    }
    
    unsigned long mysql_stmt_param_count(MYSQL_STMT* stmt) {
        return stmt->param_count;
    }
    
    MYSQL_RES* mysql_stmt_result_metadata(MYSQL_STMT*) {
        if (mock_field_count > 0) {
            mock_result.field_count = mock_field_count;
            mock_result.fields = mock_fields;
            return &mock_result;
        }
        return nullptr;
    }
    
    int mysql_stmt_store_result(MYSQL_STMT*) {
        return 0;
    }
    
    my_ulonglong mysql_stmt_num_rows(MYSQL_STMT*) {
        return mock_data.size();
    }
    
    int mysql_stmt_bind_param(MYSQL_STMT*, MYSQL_BIND*) {
        return 0;
    }
    
    int mysql_stmt_bind_result(MYSQL_STMT*, MYSQL_BIND*) {
        return 0;
    }
    
    int mysql_stmt_fetch(MYSQL_STMT*) {
        mock_current_row++;
        if (mock_current_row >= static_cast<int>(mock_data.size())) {
            return MYSQL_NO_DATA;
        }
        return 0;
    }
    
    my_ulonglong mysql_stmt_affected_rows(MYSQL_STMT*) {
        return mock_data.size();
    }
    
    int mysql_stmt_reset(MYSQL_STMT*) {
        return 0;
    }
    
    void mysql_free_result(MYSQL_RES*) {}
    
    unsigned int mysql_num_fields(MYSQL_RES* res) {
        return res->field_count;
    }
    
    MYSQL_FIELD* mysql_fetch_fields(MYSQL_RES* res) {
        return res->fields;
    }
    
    MYSQL_RES* mysql_store_result(MYSQL*) {
        if (mock_field_count > 0) {
            mock_result.field_count = mock_field_count;
            mock_result.row_count = mock_data.size();
            return &mock_result;
        }
        return nullptr;
    }
    
    MYSQL_ROW mysql_fetch_row(MYSQL_RES*) {
        mock_current_row++;
        if (mock_current_row >= static_cast<int>(mock_data.size())) {
            return nullptr;
        }
        static std::vector<char*> row_ptrs;
        row_ptrs.clear();
        for (const auto& val : mock_data[mock_current_row]) {
            row_ptrs.push_back(const_cast<char*>(val.c_str()));
        }
        return row_ptrs.data();
    }
    
    int mysql_options(MYSQL*, enum mysql_option, const void*) {
        return 0;
    }
    
    int SQLGetData(SQLHSTMT, SQLUSMALLINT, SQLSMALLINT, SQLPOINTER, SQLLEN, SQLLEN*) {
        return SQL_SUCCESS;
    }
}

// Test fixture for MySQL tests
class MySQLTest : public ::testing::Test {
protected:
    void SetUp() override {
        mock_errno = 0;
        mock_error = "";
        mock_connection_status = 1;
        mock_fields = nullptr;
        mock_field_count = 0;
        mock_data.clear();
        mock_current_row = -1;
    }
    
    void TearDown() override {
        delete[] mock_fields;
        mock_fields = nullptr;
    }
    
    void setupMockFields(const std::vector<std::pair<std::string, enum_field_types>>& fields) {
        mock_field_count = fields.size();
        mock_fields = new MYSQL_FIELD[mock_field_count];
        for (size_t i = 0; i < fields.size(); ++i) {
            mock_fields[i].name = const_cast<char*>(fields[i].first.c_str());
            mock_fields[i].type = fields[i].second;
            mock_fields[i].length = 255;
            mock_fields[i].flags = 0;
            mock_fields[i].decimals = 0;
        }
    }
};

// Tests MySQL connection
TEST_F(MySQLTest, ConnectSuccess) {
    MySQLConnection conn;
    
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.port = 3306;
    params.database = "test_db";
    params.username = "test_user";
    params.password = "test_pass";
    
    ASSERT_NO_THROW(conn.connect(params));
    EXPECT_TRUE(conn.is_connected());
    EXPECT_EQ("MySQL", conn.get_database_type());
}

// Tests connection failure
TEST_F(MySQLTest, ConnectFailure) {
    mock_connection_status = 0;
    mock_errno = 2003;
    mock_error = "Can't connect to MySQL server";
    
    MySQLConnection conn;
    
    IDatabaseConnection::ConnectionParams params;
    params.host = "invalid_host";
    params.database = "test_db";
    
    EXPECT_THROW(conn.connect(params), DatabaseException);
}

// Tests query execution with results
TEST_F(MySQLTest, ExecuteQuery) {
    setupMockFields({
        {"id", MYSQL_TYPE_LONG},
        {"name", MYSQL_TYPE_VARCHAR},
        {"value", MYSQL_TYPE_DOUBLE}
    });
    
    // Can't easily mock the complex MySQL result fetching, so we'll test the statement approach
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);
    
    // The actual implementation uses prepared statements internally
    auto result = conn.execute_query("SELECT id, name, value FROM test_table");
    ASSERT_NE(nullptr, result);
}

// Tests prepared statements
TEST_F(MySQLTest, PreparedStatement) {
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);
    
    auto stmt = conn.prepare_statement("SELECT * FROM person WHERE person_id = ?");
    ASSERT_NE(nullptr, stmt);
    
    // Bind parameter
    stmt->bind(1, 12345LL);
    
    // Clear and rebind
    stmt->clear_parameters();
    stmt->bind(1, 54321LL);
}

// Tests prepared statement with multiple parameters
TEST_F(MySQLTest, PreparedStatementMultipleParams) {
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);
    
    auto stmt = conn.prepare_statement(
        "INSERT INTO person (person_id, gender_concept_id, birth_datetime) VALUES (?, ?, ?)");
    
    // Bind different types
    stmt->bind(1, 99999LL);
    stmt->bind(2, 8507);
    stmt->bind(3, std::chrono::system_clock::now());
    
    // Execute
    mock_data.push_back({"1"}); // One row affected
    size_t affected = stmt->execute_update();
    EXPECT_EQ(1, affected);
}

// Tests transaction management
TEST_F(MySQLTest, Transactions) {
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);
    
    // Begin transaction
    ASSERT_NO_THROW(conn.begin_transaction());
    
    // Cannot begin another transaction
    EXPECT_THROW(conn.begin_transaction(), DatabaseException);
    
    // Commit
    ASSERT_NO_THROW(conn.commit());
    
    // Begin another transaction
    conn.begin_transaction();
    
    // Rollback
    ASSERT_NO_THROW(conn.rollback());
}

// Tests table existence check
TEST_F(MySQLTest, TableExists) {
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);
    
    // Mock positive result
    mock_data = {{"1"}};
    mock_current_row = -1;
    EXPECT_TRUE(conn.table_exists("person"));
    
    // Mock negative result
    mock_data = {{"0"}};
    mock_current_row = -1;
    EXPECT_FALSE(conn.table_exists("nonexistent_table"));
}

// Tests version retrieval
TEST_F(MySQLTest, GetVersion) {
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);
    
    EXPECT_EQ("8.0.32", conn.get_version());
}

// Tests query timeout
TEST_F(MySQLTest, QueryTimeout) {
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);
    
    // Set timeout
    ASSERT_NO_THROW(conn.set_query_timeout(30));
}

// Tests connection with SSL options
TEST_F(MySQLTest, ConnectWithSSL) {
    MySQLConnection conn;
    
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.port = 3306;
    params.database = "test_db";
    params.username = "test_user";
    params.password = "test_pass";
    params.options["ssl_mode"] = "REQUIRED";
    params.options["ssl_ca"] = "/path/to/ca.pem";
    
    ASSERT_NO_THROW(conn.connect(params));
}

// Tests MySQL extractor
TEST_F(MySQLTest, MySQLExtractor) {
    auto conn = std::make_unique<MySQLConnection>();
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn->connect(params);
    
    MySQLExtractor extractor(std::move(conn));
    EXPECT_EQ("mysql", extractor.get_type());
}

// Tests result set with various data types
TEST_F(MySQLTest, ResultSetDataTypes) {
    // This test demonstrates the expected behavior even though
    // we can't fully mock the complex MySQL result handling
    setupMockFields({
        {"int_col", MYSQL_TYPE_LONG},
        {"bigint_col", MYSQL_TYPE_LONGLONG},
        {"float_col", MYSQL_TYPE_FLOAT},
        {"double_col", MYSQL_TYPE_DOUBLE},
        {"string_col", MYSQL_TYPE_VARCHAR},
        {"date_col", MYSQL_TYPE_DATE},
        {"datetime_col", MYSQL_TYPE_DATETIME},
        {"bit_col", MYSQL_TYPE_BIT}
    });
    
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);
    
    // The implementation would handle type conversions internally
}

// Tests error handling
TEST_F(MySQLTest, ErrorHandling) {
    MySQLConnection conn;
    
    // Test disconnected query execution
    EXPECT_THROW(conn.execute_query("SELECT 1"), DatabaseException);
    
    // Test invalid prepared statement
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);
    
    mock_errno = 1064;
    mock_error = "You have an error in your SQL syntax";
    
    EXPECT_THROW(conn.prepare_statement("INVALID SQL"), DatabaseException);
}

// Tests connection ping
TEST_F(MySQLTest, ConnectionPing) {
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);
    
    // Connection is good
    EXPECT_TRUE(conn.is_connected());
    
    // Simulate connection loss
    mock_connection_status = 0;
    EXPECT_FALSE(conn.is_connected());
}

// Tests auto-reconnect option
TEST_F(MySQLTest, AutoReconnect) {
    MySQLConnection conn;
    
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    params.options["connect_timeout"] = "5";
    
    ASSERT_NO_THROW(conn.connect(params));
}

// Tests MySQL-specific query building
TEST_F(MySQLTest, MySQLSpecificQueryBuilder) {
    auto conn = std::make_unique<MySQLConnection>();
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn->connect(params);
    
    MySQLExtractor extractor(std::move(conn));
    
    // The extractor should add MySQL-specific optimizations
    // like SQL_BUFFER_RESULT for large result sets
}

// Tests parameter binding for different types
TEST_F(MySQLTest, ParameterBindingTypes) {
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);
    
    auto stmt = conn.prepare_statement("INSERT INTO test VALUES (?, ?, ?, ?, ?, ?)");
    
    // Test various parameter types
    stmt->bind(1, true);                    // Boolean
    stmt->bind(2, 42);                      // Integer
    stmt->bind(3, 123456789LL);            // Long long
    stmt->bind(4, 3.14159);                // Double
    stmt->bind(5, std::string("test"));    // String
    stmt->bind(6, std::chrono::system_clock::now()); // Timestamp
}

// Tests NULL parameter binding
TEST_F(MySQLTest, NullParameterBinding) {
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);
    
    auto stmt = conn.prepare_statement("INSERT INTO test VALUES (?)");
    
    // Bind NULL value
    stmt->bind(1, std::any{});
}

// Tests result set memory management
TEST_F(MySQLTest, ResultSetMemoryManagement) {
    setupMockFields({{"id", MYSQL_TYPE_LONG}});
    
    MySQLConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.host = "localhost";
    params.database = "test_db";
    conn.connect(params);
    
    {
        auto result = conn.execute_query("SELECT id FROM test");
        // Result should be properly cleaned up when going out of scope
    }
    
    // Connection should still be valid
    EXPECT_TRUE(conn.is_connected());
}