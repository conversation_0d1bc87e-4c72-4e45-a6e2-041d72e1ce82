/**
 * @file compressed_csv_extractor_test.cpp
 * @brief Unit tests for CompressedCsvExtractor class
 * <AUTHOR> ETL Team
 * @date 2024
 */

#include <gtest/gtest.h>
#include "extract/csv_extractor.h"
#include "common/exceptions.h"
#include "core/interfaces.h"
#include <filesystem>
#include <fstream>
#include <zlib.h>

namespace omop::extract::test {

using namespace ::testing;

// Mock ProcessingContext for testing
class MockProcessingContext : public core::ProcessingContext {
public:
    MOCK_METHOD(void, log, (const std::string& level, const std::string& message), (override));
    MOCK_METHOD(void, increment_errors, (), (override));
    MOCK_METHOD(bool, should_continue_on_error, (), (const, override));
};

// Helper class to create test files
class CompressedCsvExtractorTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create test directory
        test_dir_ = std::filesystem::temp_directory_path() / "compressed_csv_test";
        std::filesystem::create_directories(test_dir_);
        
        // Create sample CSV content
        csv_content_ = "id,name,value\n1,test1,100\n2,test2,200\n3,test3,300\n";
    }
    
    void TearDown() override {
        // Clean up test files
        std::filesystem::remove_all(test_dir_);
    }
    
    // Create a gzip compressed file
    std::string CreateGzipFile(const std::string& content, const std::string& filename) {
        std::string filepath = (test_dir_ / filename).string();
        gzFile gz = gzopen(filepath.c_str(), "wb");
        EXPECT_NE(gz, nullptr);
        
        int written = gzwrite(gz, content.c_str(), content.length());
        EXPECT_EQ(written, content.length());
        
        gzclose(gz);
        return filepath;
    }
    
    // Create an uncompressed CSV file
    std::string CreateCsvFile(const std::string& content, const std::string& filename) {
        std::string filepath = (test_dir_ / filename).string();
        std::ofstream file(filepath);
        file << content;
        file.close();
        return filepath;
    }
    
    std::filesystem::path test_dir_;
    std::string csv_content_;
};

// Test initialization with gzip compressed file
TEST_F(CompressedCsvExtractorTest, InitializeWithGzipFile) {
    std::string gz_file = CreateGzipFile(csv_content_, "test.csv.gz");
    
    CompressedCsvExtractor extractor;
    MockProcessingContext context;
    
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;
    
    EXPECT_NO_THROW(extractor.initialize(config, context));
    EXPECT_TRUE(extractor.has_more_data());
}

// Test automatic compression format detection by extension
TEST_F(CompressedCsvExtractorTest, DetectCompressionByExtension) {
    struct TestCase {
        std::string filename;
        CompressedCsvExtractor::CompressionFormat expected;
    };
    
    std::vector<TestCase> test_cases = {
        {"test.csv.gz", CompressedCsvExtractor::CompressionFormat::Gzip},
        {"test.csv.zip", CompressedCsvExtractor::CompressionFormat::Zip},
        {"test.csv.bz2", CompressedCsvExtractor::CompressionFormat::Bzip2},
        {"test.csv.xz", CompressedCsvExtractor::CompressionFormat::Xz},
        {"test.csv", CompressedCsvExtractor::CompressionFormat::None}
    };
    
    for (const auto& tc : test_cases) {
        std::string filepath = (test_dir_ / tc.filename).string();
        std::ofstream(filepath) << "dummy";
        
        CompressedCsvExtractor extractor;
        auto format = extractor.detect_compression(filepath);
        EXPECT_EQ(format, tc.expected) << "Failed for file: " << tc.filename;
    }
}

// Test extraction from compressed file
TEST_F(CompressedCsvExtractorTest, ExtractFromCompressedFile) {
    std::string gz_file = CreateGzipFile(csv_content_, "test.csv.gz");
    
    CompressedCsvExtractor extractor;
    MockProcessingContext context;
    
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;
    
    extractor.initialize(config, context);
    
    // Extract records
    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);
    
    // Verify extracted data
    auto records = batch.get_records();
    EXPECT_EQ(records.size(), 3);
    
    // Check first record
    auto field = records[0].get_field("id");
    EXPECT_TRUE(field.has_value());
    EXPECT_EQ(std::any_cast<long long>(*field), 1);
    
    field = records[0].get_field("name");
    EXPECT_TRUE(field.has_value());
    EXPECT_EQ(std::any_cast<std::string>(*field), "test1");
}

// Test cleanup of temporary files
TEST_F(CompressedCsvExtractorTest, CleanupTemporaryFiles) {
    std::string gz_file = CreateGzipFile(csv_content_, "test.csv.gz");
    
    CompressedCsvExtractor extractor;
    MockProcessingContext context;
    
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;
    config["cleanup_temp_file"] = true;
    
    extractor.initialize(config, context);
    
    // Extract all data
    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);
    
    // Get temp file path before finalization
    auto stats = extractor.get_statistics();
    
    // Finalize should clean up temp file
    extractor.finalize(context);
    
    // Verify temp file is cleaned up
    // Note: We can't directly check the temp file as it's private,
    // but we can verify the extractor completes without errors
    EXPECT_NO_THROW(extractor.get_statistics());
}

// Test handling of non-compressed files
TEST_F(CompressedCsvExtractorTest, HandleNonCompressedFile) {
    std::string csv_file = CreateCsvFile(csv_content_, "test.csv");
    
    CompressedCsvExtractor extractor;
    MockProcessingContext context;
    
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = csv_file;
    config["has_header"] = true;
    
    // Should work like regular CSV extractor
    EXPECT_NO_THROW(extractor.initialize(config, context));
    
    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);
}

// Test explicit compression format specification
TEST_F(CompressedCsvExtractorTest, ExplicitCompressionFormat) {
    // Create file with misleading extension
    std::string gz_file = CreateGzipFile(csv_content_, "test.dat");
    
    CompressedCsvExtractor extractor;
    MockProcessingContext context;
    
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;
    config["compression_format"] = "gzip";
    
    EXPECT_NO_THROW(extractor.initialize(config, context));
    
    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);
}

// Test error handling for missing file
TEST_F(CompressedCsvExtractorTest, MissingFileError) {
    CompressedCsvExtractor extractor;
    MockProcessingContext context;
    
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = "/nonexistent/file.csv.gz";
    
    EXPECT_THROW(extractor.initialize(config, context), common::ExtractionException);
}

// Test error handling for corrupted compressed file
TEST_F(CompressedCsvExtractorTest, CorruptedCompressedFile) {
    // Create corrupted gz file
    std::string filepath = (test_dir_ / "corrupted.csv.gz").string();
    std::ofstream file(filepath, std::ios::binary);
    file << "\x1f\x8b\x08\x00\x00\x00\x00\x00\x00\x00CORRUPTED_DATA";
    file.close();
    
    CompressedCsvExtractor extractor;
    MockProcessingContext context;
    
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["has_header"] = true;
    
    EXPECT_THROW(extractor.initialize(config, context), common::ExtractionException);
}

// Test format conversion functions
TEST_F(CompressedCsvExtractorTest, FormatConversionFunctions) {
    // Test format_to_string
    EXPECT_EQ(CompressedCsvExtractor::format_to_string(
        CompressedCsvExtractor::CompressionFormat::Gzip), "Gzip");
    EXPECT_EQ(CompressedCsvExtractor::format_to_string(
        CompressedCsvExtractor::CompressionFormat::Zip), "Zip");
    EXPECT_EQ(CompressedCsvExtractor::format_to_string(
        CompressedCsvExtractor::CompressionFormat::None), "None");
    
    // Test string_to_format
    EXPECT_EQ(CompressedCsvExtractor::string_to_format("gzip"), 
        CompressedCsvExtractor::CompressionFormat::Gzip);
    EXPECT_EQ(CompressedCsvExtractor::string_to_format("GZ"), 
        CompressedCsvExtractor::CompressionFormat::Gzip);
    EXPECT_EQ(CompressedCsvExtractor::string_to_format("zip"), 
        CompressedCsvExtractor::CompressionFormat::Zip);
    EXPECT_EQ(CompressedCsvExtractor::string_to_format("unknown"), 
        CompressedCsvExtractor::CompressionFormat::None);
}

// Test get_type method
TEST_F(CompressedCsvExtractorTest, GetType) {
    CompressedCsvExtractor extractor;
    EXPECT_EQ(extractor.get_type(), "compressed_csv");
}

// Test statistics collection
TEST_F(CompressedCsvExtractorTest, Statistics) {
    std::string gz_file = CreateGzipFile(csv_content_, "test.csv.gz");
    
    CompressedCsvExtractor extractor;
    MockProcessingContext context;
    
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;
    
    extractor.initialize(config, context);
    auto batch = extractor.extract_batch(10, context);
    
    auto stats = extractor.get_statistics();
    EXPECT_TRUE(stats.contains("filepath"));
    EXPECT_TRUE(stats.contains("extracted_count"));
    EXPECT_EQ(std::any_cast<size_t>(stats["extracted_count"]), 3);
}

// Test batch extraction with limit
TEST_F(CompressedCsvExtractorTest, BatchExtractionWithLimit) {
    std::string gz_file = CreateGzipFile(csv_content_, "test.csv.gz");
    
    CompressedCsvExtractor extractor;
    MockProcessingContext context;
    
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;
    
    extractor.initialize(config, context);
    
    // Extract with batch size 2
    auto batch1 = extractor.extract_batch(2, context);
    EXPECT_EQ(batch1.size(), 2);
    
    // Extract remaining
    auto batch2 = extractor.extract_batch(2, context);
    EXPECT_EQ(batch2.size(), 1);
    
    // No more data
    auto batch3 = extractor.extract_batch(2, context);
    EXPECT_EQ(batch3.size(), 0);
    EXPECT_FALSE(extractor.has_more_data());
}

// Test disable cleanup option
TEST_F(CompressedCsvExtractorTest, DisableCleanup) {
    std::string gz_file = CreateGzipFile(csv_content_, "test.csv.gz");
    
    CompressedCsvExtractor extractor;
    MockProcessingContext context;
    
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;
    config["cleanup_temp_file"] = false;
    
    extractor.initialize(config, context);
    auto batch = extractor.extract_batch(10, context);
    EXPECT_EQ(batch.size(), 3);
    
    // Finalize should not clean up temp file
    extractor.finalize(context);
    
    // Temp file should still exist (we can't check directly, but no errors should occur)
    EXPECT_NO_THROW(extractor.get_statistics());
}

// Test large compressed file handling
TEST_F(CompressedCsvExtractorTest, LargeCompressedFile) {
    // Create large CSV content
    std::stringstream large_csv;
    large_csv << "id,name,value\n";
    for (int i = 0; i < 1000; ++i) {
        large_csv << i << ",test" << i << "," << i * 100 << "\n";
    }
    
    std::string gz_file = CreateGzipFile(large_csv.str(), "large.csv.gz");
    
    CompressedCsvExtractor extractor;
    MockProcessingContext context;
    
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = gz_file;
    config["has_header"] = true;
    
    extractor.initialize(config, context);
    
    // Extract in batches
    size_t total_extracted = 0;
    while (extractor.has_more_data()) {
        auto batch = extractor.extract_batch(100, context);
        total_extracted += batch.size();
    }
    
    EXPECT_EQ(total_extracted, 1000);
}

} // namespace omop::extract::test
