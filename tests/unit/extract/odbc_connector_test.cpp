/**
 * @file odbc_connector_test.cpp
 * @brief Unit tests for ODBC connector functionality
 * <AUTHOR> ETL Team
 * @date 2024
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/odbc_connector.h"
#include "common/exceptions.h"
#include <cstring>

using namespace omop::extract;
using namespace omop::common;

// Mock ODBC structures and data
static SQLRETURN mock_sql_return = SQL_SUCCESS;
static SQLINTEGER mock_row_count = 0;
static SQLSMALLINT mock_column_count = 0;
static std::vector<std::string> mock_column_names;
static std::vector<SQLSMALLINT> mock_column_types;
static std::vector<std::vector<std::string>> mock_data;
static int mock_current_row = -1;
static std::string mock_error_message = "";
static SQLCHAR mock_sql_state[6] = "00000";
static SQLINTEGER mock_native_error = 0;
static SQLHDBC mock_connection = reinterpret_cast<SQLHDBC>(0x1000);
static SQLHSTMT mock_statement = reinterpret_cast<SQLHSTMT>(0x2000);
static SQLHENV mock_environment = reinterpret_cast<SQLHENV>(0x3000);
static SQLINTEGER mock_connection_dead = SQL_CD_FALSE;

// Mock ODBC API functions
extern "C" {
    SQLRETURN SQLAllocHandle(SQLSMALLINT HandleType, SQLHANDLE InputHandle, SQLHANDLE* OutputHandle) {
        switch (HandleType) {
            case SQL_HANDLE_ENV:
                *OutputHandle = mock_environment;
                break;
            case SQL_HANDLE_DBC:
                *OutputHandle = mock_connection;
                break;
            case SQL_HANDLE_STMT:
                *OutputHandle = mock_statement;
                break;
        }
        return mock_sql_return;
    }
    
    SQLRETURN SQLFreeHandle(SQLSMALLINT HandleType, SQLHANDLE Handle) {
        return SQL_SUCCESS;
    }
    
    SQLRETURN SQLSetEnvAttr(SQLHENV EnvironmentHandle, SQLINTEGER Attribute,
                           SQLPOINTER Value, SQLINTEGER StringLength) {
        return SQL_SUCCESS;
    }
    
    SQLRETURN SQLDriverConnect(SQLHDBC ConnectionHandle, SQLHWND WindowHandle,
                               SQLCHAR* InConnectionString, SQLSMALLINT StringLength1,
                               SQLCHAR* OutConnectionString, SQLSMALLINT BufferLength,
                               SQLSMALLINT* StringLength2Ptr, SQLUSMALLINT DriverCompletion) {
        if (OutConnectionString && BufferLength > 0) {
            strncpy(reinterpret_cast<char*>(OutConnectionString), 
                    reinterpret_cast<char*>(InConnectionString), BufferLength - 1);
        }
        if (StringLength2Ptr) {
            *StringLength2Ptr = strlen(reinterpret_cast<char*>(InConnectionString));
        }
        return mock_sql_return;
    }
    
    SQLRETURN SQLDisconnect(SQLHDBC ConnectionHandle) {
        return SQL_SUCCESS;
    }
    
    SQLRETURN SQLGetConnectAttr(SQLHDBC ConnectionHandle, SQLINTEGER Attribute,
                               SQLPOINTER Value, SQLINTEGER BufferLength,
                               SQLINTEGER* StringLengthPtr) {
        if (Attribute == SQL_ATTR_CONNECTION_DEAD) {
            *static_cast<SQLINTEGER*>(Value) = mock_connection_dead;
        }
        return SQL_SUCCESS;
    }
    
    SQLRETURN SQLSetConnectAttr(SQLHDBC ConnectionHandle, SQLINTEGER Attribute,
                               SQLPOINTER Value, SQLINTEGER StringLength) {
        return SQL_SUCCESS;
    }
    
    SQLRETURN SQLExecDirect(SQLHSTMT StatementHandle, SQLCHAR* StatementText,
                           SQLINTEGER TextLength) {
        mock_current_row = -1;
        return mock_sql_return;
    }
    
    SQLRETURN SQLPrepare(SQLHSTMT StatementHandle, SQLCHAR* StatementText,
                        SQLINTEGER TextLength) {
        return mock_sql_return;
    }
    
    SQLRETURN SQLExecute(SQLHSTMT StatementHandle) {
        mock_current_row = -1;
        return mock_sql_return;
    }
    
    SQLRETURN SQLNumResultCols(SQLHSTMT StatementHandle, SQLSMALLINT* ColumnCountPtr) {
        *ColumnCountPtr = mock_column_count;
        return SQL_SUCCESS;
    }
    
    SQLRETURN SQLDescribeCol(SQLHSTMT StatementHandle, SQLUSMALLINT ColumnNumber,
                            SQLCHAR* ColumnName, SQLSMALLINT BufferLength,
                            SQLSMALLINT* NameLengthPtr, SQLSMALLINT* DataTypePtr,
                            SQLULEN* ColumnSizePtr, SQLSMALLINT* DecimalDigitsPtr,
                            SQLSMALLINT* NullablePtr) {
        if (ColumnNumber > 0 && ColumnNumber <= mock_column_count) {
            if (ColumnName && BufferLength > 0) {
                strncpy(reinterpret_cast<char*>(ColumnName), 
                       mock_column_names[ColumnNumber - 1].c_str(), BufferLength - 1);
            }
            if (NameLengthPtr) {
                *NameLengthPtr = mock_column_names[ColumnNumber - 1].length();
            }
            if (DataTypePtr) {
                *DataTypePtr = mock_column_types[ColumnNumber - 1];
            }
            if (ColumnSizePtr) {
                *ColumnSizePtr = 255;
            }
            if (DecimalDigitsPtr) {
                *DecimalDigitsPtr = 0;
            }
            if (NullablePtr) {
                *NullablePtr = SQL_NULLABLE;
            }
            return SQL_SUCCESS;
        }
        return SQL_ERROR;
    }
    
    SQLRETURN SQLFetch(SQLHSTMT StatementHandle) {
        mock_current_row++;
        if (mock_current_row >= static_cast<int>(mock_data.size())) {
            return SQL_NO_DATA;
        }
        return SQL_SUCCESS;
    }
    
    SQLRETURN SQLGetData(SQLHSTMT StatementHandle, SQLUSMALLINT ColumnNumber,
                        SQLSMALLINT TargetType, SQLPOINTER TargetValue,
                        SQLLEN BufferLength, SQLLEN* StrLen_or_IndPtr) {
        if (ColumnNumber > 0 && ColumnNumber <= mock_column_count &&
            mock_current_row >= 0 && mock_current_row < static_cast<int>(mock_data.size())) {
            
            const std::string& value = mock_data[mock_current_row][ColumnNumber - 1];
            
            if (value == "NULL") {
                if (StrLen_or_IndPtr) {
                    *StrLen_or_IndPtr = SQL_NULL_DATA;
                }
            } else {
                switch (TargetType) {
                    case SQL_C_SLONG:
                        *static_cast<SQLINTEGER*>(TargetValue) = std::stoi(value);
                        break;
                    case SQL_C_SBIGINT:
                        *static_cast<SQLBIGINT*>(TargetValue) = std::stoll(value);
                        break;
                    case SQL_C_DOUBLE:
                        *static_cast<SQLDOUBLE*>(TargetValue) = std::stod(value);
                        break;
                    case SQL_C_CHAR:
                        if (BufferLength > 0) {
                            strncpy(static_cast<char*>(TargetValue), value.c_str(), BufferLength - 1);
                            static_cast<char*>(TargetValue)[BufferLength - 1] = '\0';
                        }
                        break;
                    case SQL_C_TYPE_DATE:
                        // Simplified date handling
                        static_cast<SQL_DATE_STRUCT*>(TargetValue)->year = 2024;
                        static_cast<SQL_DATE_STRUCT*>(TargetValue)->month = 1;
                        static_cast<SQL_DATE_STRUCT*>(TargetValue)->day = 15;
                        break;
                    case SQL_C_TYPE_TIMESTAMP:
                        // Simplified timestamp handling
                        static_cast<SQL_TIMESTAMP_STRUCT*>(TargetValue)->year = 2024;
                        static_cast<SQL_TIMESTAMP_STRUCT*>(TargetValue)->month = 1;
                        static_cast<SQL_TIMESTAMP_STRUCT*>(TargetValue)->day = 15;
                        static_cast<SQL_TIMESTAMP_STRUCT*>(TargetValue)->hour = 10;
                        static_cast<SQL_TIMESTAMP_STRUCT*>(TargetValue)->minute = 30;
                        static_cast<SQL_TIMESTAMP_STRUCT*>(TargetValue)->second = 45;
                        static_cast<SQL_TIMESTAMP_STRUCT*>(TargetValue)->fraction = 0;
                        break;
                }
                if (StrLen_or_IndPtr) {
                    *StrLen_or_IndPtr = value.length();
                }
            }
        }
        return SQL_SUCCESS;
    }
    
    SQLRETURN SQLRowCount(SQLHSTMT StatementHandle, SQLLEN* RowCountPtr) {
        *RowCountPtr = mock_row_count;
        return SQL_SUCCESS;
    }
    
    SQLRETURN SQLGetDiagRec(SQLSMALLINT HandleType, SQLHANDLE Handle,
                           SQLSMALLINT RecNumber, SQLCHAR* SQLState,
                           SQLINTEGER* NativeErrorPtr, SQLCHAR* MessageText,
                           SQLSMALLINT BufferLength, SQLSMALLINT* TextLengthPtr) {
        if (RecNumber != 1) {
            return SQL_NO_DATA;
        }
        
        if (SQLState) {
            memcpy(SQLState, mock_sql_state, 6);
        }
        if (NativeErrorPtr) {
            *NativeErrorPtr = mock_native_error;
        }
        if (MessageText && BufferLength > 0) {
            strncpy(reinterpret_cast<char*>(MessageText), 
                   mock_error_message.c_str(), BufferLength - 1);
        }
        if (TextLengthPtr) {
            *TextLengthPtr = mock_error_message.length();
        }
        return SQL_SUCCESS;
    }
    
    SQLRETURN SQLBindParameter(SQLHSTMT StatementHandle, SQLUSMALLINT ParameterNumber,
                              SQLSMALLINT InputOutputType, SQLSMALLINT ValueType,
                              SQLSMALLINT ParameterType, SQLULEN ColumnSize,
                              SQLSMALLINT DecimalDigits, SQLPOINTER ParameterValuePtr,
                              SQLLEN BufferLength, SQLLEN* StrLen_or_IndPtr) {
        return SQL_SUCCESS;
    }
    
    SQLRETURN SQLFreeStmt(SQLHSTMT StatementHandle, SQLUSMALLINT Option) {
        return SQL_SUCCESS;
    }
    
    SQLRETURN SQLEndTran(SQLSMALLINT HandleType, SQLHANDLE Handle,
                        SQLSMALLINT CompletionType) {
        return SQL_SUCCESS;
    }
    
    SQLRETURN SQLGetInfo(SQLHDBC ConnectionHandle, SQLUSMALLINT InfoType,
                        SQLPOINTER InfoValuePtr, SQLSMALLINT BufferLength,
                        SQLSMALLINT* StringLengthPtr) {
        switch (InfoType) {
            case SQL_DRIVER_NAME:
                if (InfoValuePtr && BufferLength > 0) {
                    strncpy(static_cast<char*>(InfoValuePtr), "MockODBC", BufferLength - 1);
                }
                break;
            case SQL_DBMS_VER:
                if (InfoValuePtr && BufferLength > 0) {
                    strncpy(static_cast<char*>(InfoValuePtr), "1.0.0", BufferLength - 1);
                }
                break;
        }
        return SQL_SUCCESS;
    }
    
    SQLRETURN SQLTables(SQLHSTMT StatementHandle, SQLCHAR* CatalogName,
                       SQLSMALLINT NameLength1, SQLCHAR* SchemaName,
                       SQLSMALLINT NameLength2, SQLCHAR* TableName,
                       SQLSMALLINT NameLength3, SQLCHAR* TableType,
                       SQLSMALLINT NameLength4) {
        // Set up mock data for table existence check
        mock_column_count = 5; // TABLE_CAT, TABLE_SCHEM, TABLE_NAME, TABLE_TYPE, REMARKS
        mock_column_names = {"TABLE_CAT", "TABLE_SCHEM", "TABLE_NAME", "TABLE_TYPE", "REMARKS"};
        mock_current_row = -1;
        return SQL_SUCCESS;
    }
    
    SQLRETURN SQLDrivers(SQLHENV EnvironmentHandle, SQLUSMALLINT Direction,
                        SQLCHAR* DriverDescription, SQLSMALLINT BufferLength1,
                        SQLSMALLINT* DescriptionLengthPtr, SQLCHAR* DriverAttributes,
                        SQLSMALLINT BufferLength2, SQLSMALLINT* AttributesLengthPtr) {
        static int driver_index = 0;
        if (Direction == SQL_FETCH_FIRST) {
            driver_index = 0;
        }
        
        if (driver_index >= 2) {
            return SQL_NO_DATA;
        }
        
        if (driver_index == 0) {
            if (DriverDescription && BufferLength1 > 0) {
                strncpy(reinterpret_cast<char*>(DriverDescription), "Mock ODBC Driver", BufferLength1 - 1);
            }
            if (DriverAttributes && BufferLength2 > 0) {
                strncpy(reinterpret_cast<char*>(DriverAttributes), "Version=1.0\0", BufferLength2 - 1);
            }
        } else {
            if (DriverDescription && BufferLength1 > 0) {
                strncpy(reinterpret_cast<char*>(DriverDescription), "Test Driver", BufferLength1 - 1);
            }
        }
        
        driver_index++;
        return SQL_SUCCESS;
    }
    
    SQLRETURN SQLDataSources(SQLHENV EnvironmentHandle, SQLUSMALLINT Direction,
                            SQLCHAR* ServerName, SQLSMALLINT BufferLength1,
                            SQLSMALLINT* NameLength1Ptr, SQLCHAR* Description,
                            SQLSMALLINT BufferLength2, SQLSMALLINT* NameLength2Ptr) {
        static int dsn_index = 0;
        if (Direction == SQL_FETCH_FIRST) {
            dsn_index = 0;
        }
        
        if (dsn_index >= 1) {
            return SQL_NO_DATA;
        }
        
        if (ServerName && BufferLength1 > 0) {
            strncpy(reinterpret_cast<char*>(ServerName), "TestDSN", BufferLength1 - 1);
        }
        if (Description && BufferLength2 > 0) {
            strncpy(reinterpret_cast<char*>(Description), "Test Data Source", BufferLength2 - 1);
        }
        
        dsn_index++;
        return SQL_SUCCESS;
    }
}

// Test fixture for ODBC tests
class OdbcTest : public ::testing::Test {
protected:
    void SetUp() override {
        mock_sql_return = SQL_SUCCESS;
        mock_row_count = 0;
        mock_column_count = 0;
        mock_column_names.clear();
        mock_column_types.clear();
        mock_data.clear();
        mock_current_row = -1;
        mock_error_message = "";
        strcpy(reinterpret_cast<char*>(mock_sql_state), "00000");
        mock_native_error = 0;
        mock_connection_dead = SQL_CD_FALSE;
    }
};

// Tests ODBC connection
TEST_F(OdbcTest, ConnectSuccess) {
    OdbcDatabaseConnection conn;
    
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "SQL Server";
    params.host = "localhost";
    params.database = "test_db";
    params.username = "test_user";
    params.password = "test_pass";
    
    ASSERT_NO_THROW(conn.connect(params));
    EXPECT_TRUE(conn.is_connected());
}

// Tests DSN-based connection
TEST_F(OdbcTest, ConnectWithDSN) {
    OdbcDatabaseConnection conn;
    
    IDatabaseConnection::ConnectionParams params;
    params.options["DSN"] = "MyDataSource";
    params.username = "test_user";
    params.password = "test_pass";
    
    ASSERT_NO_THROW(conn.connect(params));
    EXPECT_TRUE(conn.is_connected());
}

// Tests connection failure
TEST_F(OdbcTest, ConnectFailure) {
    mock_sql_return = SQL_ERROR;
    mock_native_error = 1;
    mock_error_message = "Unable to connect to data source";
    strcpy(reinterpret_cast<char*>(mock_sql_state), "08001");
    
    OdbcDatabaseConnection conn;
    
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Invalid Driver";
    params.host = "invalid_host";
    
    EXPECT_THROW(conn.connect(params), DatabaseException);
}

// Tests query execution
TEST_F(OdbcTest, ExecuteQuery) {
    mock_column_count = 3;
    mock_column_names = {"id", "name", "value"};
    mock_column_types = {SQL_INTEGER, SQL_VARCHAR, SQL_DOUBLE};
    mock_data = {
        {"1", "John", "100.5"},
        {"2", "Jane", "200.75"}
    };
    
    OdbcDatabaseConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    params.database = "test_db";
    conn.connect(params);
    
    auto result = conn.execute_query("SELECT id, name, value FROM test_table");
    ASSERT_NE(nullptr, result);
    
    // Check metadata
    EXPECT_EQ(3, result->column_count());
    EXPECT_EQ("id", result->column_name(0));
    EXPECT_EQ("name", result->column_name(1));
    EXPECT_EQ("value", result->column_name(2));
    
    // Check first row
    ASSERT_TRUE(result->next());
    EXPECT_EQ(1, std::any_cast<int>(result->get_value(0)));
    EXPECT_EQ("John", std::any_cast<std::string>(result->get_value(1)));
    EXPECT_DOUBLE_EQ(100.5, std::any_cast<double>(result->get_value(2)));
    
    // Check second row
    ASSERT_TRUE(result->next());
    EXPECT_EQ(2, std::any_cast<int>(result->get_value(0)));
    
    // No more rows
    EXPECT_FALSE(result->next());
}

// Tests NULL value handling
TEST_F(OdbcTest, NullValueHandling) {
    mock_column_count = 3;
    mock_column_names = {"id", "name", "optional"};
    mock_column_types = {SQL_INTEGER, SQL_VARCHAR, SQL_INTEGER};
    mock_data = {{"1", "Test", "NULL"}};
    
    OdbcDatabaseConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    conn.connect(params);
    
    auto result = conn.execute_query("SELECT * FROM test_table");
    ASSERT_TRUE(result->next());
    
    EXPECT_FALSE(result->is_null(0));
    EXPECT_FALSE(result->is_null(1));
    EXPECT_TRUE(result->is_null(2));
    
    EXPECT_FALSE(result->is_null("id"));
    EXPECT_TRUE(result->is_null("optional"));
}

// Tests prepared statements
TEST_F(OdbcTest, PreparedStatement) {
    OdbcDatabaseConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    conn.connect(params);
    
    auto stmt = conn.prepare_statement("SELECT * FROM person WHERE person_id = ?");
    ASSERT_NE(nullptr, stmt);
    
    // Bind parameter
    stmt->bind(1, 12345LL);
    
    // Set up result
    mock_column_count = 2;
    mock_column_names = {"person_id", "birth_date"};
    mock_column_types = {SQL_BIGINT, SQL_TYPE_DATE};
    mock_data = {{"12345", "2000-01-15"}};
    
    auto result = stmt->execute_query();
    ASSERT_NE(nullptr, result);
}

// Tests execute update
TEST_F(OdbcTest, ExecuteUpdate) {
    mock_row_count = 5;
    
    OdbcDatabaseConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    conn.connect(params);
    
    size_t affected = conn.execute_update("UPDATE test_table SET value = 100");
    EXPECT_EQ(5, affected);
}

// Tests transactions
TEST_F(OdbcTest, Transactions) {
    OdbcDatabaseConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    conn.connect(params);
    
    // Begin transaction
    ASSERT_NO_THROW(conn.begin_transaction());
    
    // Commit
    ASSERT_NO_THROW(conn.commit());
    
    // Begin another transaction
    conn.begin_transaction();
    
    // Rollback
    ASSERT_NO_THROW(conn.rollback());
}

// Tests table existence
TEST_F(OdbcTest, TableExists) {
    OdbcDatabaseConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    conn.connect(params);
    
    // Mock table exists
    mock_data = {{"", "", "person", "TABLE", ""}};
    EXPECT_TRUE(conn.table_exists("person"));
    
    // Mock table doesn't exist
    mock_data.clear();
    EXPECT_FALSE(conn.table_exists("nonexistent"));
}

// Tests version retrieval
TEST_F(OdbcTest, GetVersion) {
    OdbcDatabaseConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    conn.connect(params);
    
    EXPECT_EQ("1.0.0", conn.get_version());
}

// Tests database type detection
TEST_F(OdbcTest, GetDatabaseType) {
    OdbcDatabaseConnection conn;
    
    // Test various driver names
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "MySQL ODBC 8.0 Driver";
    conn.connect(params);
    EXPECT_EQ("MySQL", conn.get_database_type());
    
    conn.disconnect();
    params.options["Driver"] = "PostgreSQL Unicode";
    conn.connect(params);
    EXPECT_EQ("PostgreSQL", conn.get_database_type());
    
    conn.disconnect();
    params.options["Driver"] = "ODBC Driver 17 for SQL Server";
    conn.connect(params);
    EXPECT_EQ("SQL Server", conn.get_database_type());
}

// Tests error handling
TEST_F(OdbcTest, ErrorHandling) {
    OdbcDatabaseConnection conn;
    
    // Test query on disconnected connection
    EXPECT_THROW(conn.execute_query("SELECT 1"), DatabaseException);
    
    // Test connection dead detection
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    conn.connect(params);
    
    mock_connection_dead = SQL_CD_TRUE;
    EXPECT_FALSE(conn.is_connected());
}

// Tests ODBC driver manager
TEST_F(OdbcTest, DriverManager) {
    // Test getting available drivers
    auto drivers = OdbcDriverManager::get_available_drivers();
    EXPECT_GE(drivers.size(), 1);
    EXPECT_EQ("Mock ODBC Driver", drivers[0].name);
    
    // Test getting data sources
    auto sources = OdbcDriverManager::get_data_sources();
    EXPECT_GE(sources.size(), 1);
    EXPECT_EQ("TestDSN", sources[0].name);
    
    // Test connection testing
    auto [success, message] = OdbcDriverManager::test_connection("Driver={Test Driver}");
    EXPECT_TRUE(success);
}

// Tests ODBC extractor
TEST_F(OdbcTest, OdbcExtractor) {
    auto conn = std::make_unique<OdbcDatabaseConnection>();
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    conn->connect(params);
    
    OdbcExtractor extractor(std::move(conn));
    EXPECT_EQ("odbc", extractor.get_type());
}

// Tests result set column types
TEST_F(OdbcTest, ResultSetColumnTypes) {
    mock_column_count = 7;
    mock_column_names = {"bit_col", "int_col", "bigint_col", "float_col", 
                        "date_col", "time_col", "timestamp_col"};
    mock_column_types = {SQL_BIT, SQL_INTEGER, SQL_BIGINT, SQL_DOUBLE,
                        SQL_TYPE_DATE, SQL_TYPE_TIME, SQL_TYPE_TIMESTAMP};
    mock_data = {{"1", "42", "123456789", "3.14159", "2024-01-15", "10:30:45", "2024-01-15 10:30:45"}};
    
    OdbcDatabaseConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    conn.connect(params);
    
    auto result = conn.execute_query("SELECT * FROM test");
    ASSERT_TRUE(result->next());
    
    // Check type names
    EXPECT_EQ("BIT", result->column_type(0));
    EXPECT_EQ("INTEGER", result->column_type(1));
    EXPECT_EQ("BIGINT", result->column_type(2));
    EXPECT_EQ("DOUBLE", result->column_type(3));
    EXPECT_EQ("DATE", result->column_type(4));
    EXPECT_EQ("TIME", result->column_type(5));
    EXPECT_EQ("TIMESTAMP", result->column_type(6));
}

// Tests parameter binding for different types
TEST_F(OdbcTest, ParameterBindingTypes) {
    OdbcDatabaseConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    conn.connect(params);
    
    auto stmt = conn.prepare_statement("INSERT INTO test VALUES (?, ?, ?, ?, ?, ?)");
    
    // Test various parameter types
    stmt->bind(1, true);                    // Boolean
    stmt->bind(2, 42);                      // Integer
    stmt->bind(3, 123456789LL);            // Long long
    stmt->bind(4, 3.14159);                // Double
    stmt->bind(5, std::string("test"));    // String
    stmt->bind(6, std::chrono::system_clock::now()); // Timestamp
    
    mock_row_count = 1;
    size_t affected = stmt->execute_update();
    EXPECT_EQ(1, affected);
}

// Tests query timeout
TEST_F(OdbcTest, QueryTimeout) {
    OdbcDatabaseConnection conn;
    IDatabaseConnection::ConnectionParams params;
    params.options["Driver"] = "Test Driver";
    conn.connect(params);
    
    ASSERT_NO_THROW(conn.set_query_timeout(30));
}