/**
 * @file extractor_base_test.cpp
 * @brief Unit tests for extractor base class functionality
 * <AUTHOR> ETL Team
 * @date 2024
 */

#include <gtest/gtest.h>
#include "extract/extractor_base.h"
#include "common/exceptions.h"
#include "common/configuration.h"
#include "common/logging.h"
#include <thread>
#include <chrono>

using namespace omop::extract;
using namespace omop::core;
using namespace omop::common;
using ::testing::_;
using ::testing::Return;
using ::testing::Invoke;

// Mock extractor implementation for testing
class MockExtractor : public ExtractorBase {
public:
    MockExtractor(const std::string& name,
                  std::shared_ptr<Configuration> config,
                  std::shared_ptr<Logger> logger)
        : ExtractorBase(name, config, logger) {}

    MOCK_METHOD(SourceSchema, getSchema, (), (const, override));
    MOCK_METHOD(ValidationResult, validateSource, (), (override));
    MOCK_METHOD(bool, connect, (), (override));
    MOCK_METHOD(void, disconnect, (), (override));
    MOCK_METHOD(std::vector<Record>, extractBatchImpl, (size_t batch_size), (override));
    MOCK_METHOD(Record, convertToRecord, (const std::any& source_data), (override));
};

// Test fixture for extractor base tests
class ExtractorBaseTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_ = std::make_shared<Configuration>();
        logger_ = Logger::get("test-extractor");
        extractor_ = std::make_unique<MockExtractor>("test_extractor", config_, logger_);
        extractor_ptr_ = extractor_.get();
    }

    std::shared_ptr<Configuration> config_;
    std::shared_ptr<Logger> logger_;
    std::unique_ptr<MockExtractor> extractor_;
    MockExtractor* extractor_ptr_;
};

// Tests extractor initialization
TEST_F(ExtractorBaseTest, Initialize) {
    EXPECT_CALL(*extractor_ptr_, connect())
        .WillOnce(Return(true));
    
    ValidationResult valid_result{true, ""};
    EXPECT_CALL(*extractor_ptr_, validateSource())
        .WillOnce(Return(valid_result));
    
    EXPECT_TRUE(extractor_ptr_->initialize());
    
    // Second initialization should warn but succeed
    EXPECT_TRUE(extractor_ptr_->initialize());
}

// Tests initialization failure on connection
TEST_F(ExtractorBaseTest, InitializeConnectionFailure) {
    EXPECT_CALL(*extractor_ptr_, connect())
        .WillOnce(Return(false));
    
    EXPECT_FALSE(extractor_ptr_->initialize());
}

// Tests initialization failure on validation
TEST_F(ExtractorBaseTest, InitializeValidationFailure) {
    EXPECT_CALL(*extractor_ptr_, connect())
        .WillOnce(Return(true));
    
    ValidationResult invalid_result{false, "Schema mismatch"};
    EXPECT_CALL(*extractor_ptr_, validateSource())
        .WillOnce(Return(invalid_result));
    
    EXPECT_FALSE(extractor_ptr_->initialize());
}

// Tests basic extraction
TEST_F(ExtractorBaseTest, Extract) {
    // Initialize extractor
    EXPECT_CALL(*extractor_ptr_, connect()).WillOnce(Return(true));
    ValidationResult valid_result{true, ""};
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize();
    
    // Set up mock batch extraction
    std::vector<Record> mock_records;
    for (int i = 1; i <= 5; ++i) {
        Record r;
        r.setField("id", i);
        r.setField("value", i * 10);
        mock_records.push_back(r);
    }
    
    EXPECT_CALL(*extractor_ptr_, extractBatchImpl(10))
        .WillOnce(Return(mock_records))
        .WillOnce(Return(std::vector<Record>{})); // Empty batch to end
    
    ExtractRequest request;
    request.batch_size = 10;
    
    auto result = extractor_ptr_->extract(request);
    
    EXPECT_TRUE(result.success);
    EXPECT_EQ(5, result.records.size());
    EXPECT_EQ(5, result.records_extracted);
    EXPECT_EQ(0, result.records_failed);
}

// Tests extraction with max records limit
TEST_F(ExtractorBaseTest, ExtractWithMaxRecords) {
    // Initialize
    EXPECT_CALL(*extractor_ptr_, connect()).WillOnce(Return(true));
    ValidationResult valid_result{true, ""};
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize();
    
    // Set up to return more records than max
    std::vector<Record> mock_batch;
    for (int i = 1; i <= 10; ++i) {
        Record r;
        r.setField("id", i);
        mock_batch.push_back(r);
    }
    
    EXPECT_CALL(*extractor_ptr_, extractBatchImpl(_))
        .WillRepeatedly(Return(mock_batch));
    
    ExtractRequest request;
    request.batch_size = 10;
    request.max_records = 15; // Limit to 15 records
    
    auto result = extractor_ptr_->extract(request);
    
    EXPECT_TRUE(result.success);
    EXPECT_EQ(15, result.records.size());
}

// Tests extraction with skip records
TEST_F(ExtractorBaseTest, ExtractWithSkipRecords) {
    // Initialize
    EXPECT_CALL(*extractor_ptr_, connect()).WillOnce(Return(true));
    ValidationResult valid_result{true, ""};
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize();
    
    ExtractRequest request;
    request.batch_size = 10;
    request.skip_records = 100; // Skip first 100 records
    
    auto result = extractor_ptr_->extract(request);
    
    EXPECT_TRUE(result.success);
    // Verify skip was applied (position should be updated)
    auto stats = extractor_ptr_->getStatistics();
    EXPECT_EQ(0, stats.successful_records); // No records extracted in this test
}

// Tests extraction with column selection
TEST_F(ExtractorBaseTest, ExtractWithColumnSelection) {
    // Initialize
    EXPECT_CALL(*extractor_ptr_, connect()).WillOnce(Return(true));
    ValidationResult valid_result{true, ""};
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize();
    
    // Create records with multiple fields
    std::vector<Record> mock_records;
    Record r;
    r.setField("id", 1);
    r.setField("name", std::string("test"));
    r.setField("value", 100);
    r.setField("extra", std::string("should be filtered"));
    mock_records.push_back(r);
    
    EXPECT_CALL(*extractor_ptr_, extractBatchImpl(_))
        .WillOnce(Return(mock_records))
        .WillOnce(Return(std::vector<Record>{}));
    
    ExtractRequest request;
    request.batch_size = 10;
    request.columns = {"id", "name", "value"}; // Select specific columns
    
    auto result = extractor_ptr_->extract(request);
    
    EXPECT_TRUE(result.success);
    EXPECT_EQ(1, result.records.size());
    
    // Check that only selected columns are present
    const auto& extracted_record = result.records[0];
    EXPECT_TRUE(extracted_record.hasField("id"));
    EXPECT_TRUE(extracted_record.hasField("name"));
    EXPECT_TRUE(extracted_record.hasField("value"));
    EXPECT_FALSE(extracted_record.hasField("extra"));
}

// Tests batch extraction
TEST_F(ExtractorBaseTest, ExtractBatch) {
    // Initialize
    EXPECT_CALL(*extractor_ptr_, connect()).WillOnce(Return(true));
    ValidationResult valid_result{true, ""};
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize();
    
    // Mock raw data
    std::vector<std::any> raw_data = {
        std::make_any<int>(1),
        std::make_any<int>(2),
        std::make_any<int>(3)
    };
    
    // Set up convertToRecord expectations
    for (size_t i = 0; i < raw_data.size(); ++i) {
        Record r;
        r.setField("id", std::any_cast<int>(raw_data[i]));
        EXPECT_CALL(*extractor_ptr_, convertToRecord(raw_data[i]))
            .WillOnce(Return(r));
    }
    
    EXPECT_CALL(*extractor_ptr_, extractBatchImpl(10))
        .WillOnce(Return(raw_data));
    
    auto batch = extractor_ptr_->extractBatch(10);
    EXPECT_EQ(3, batch.size());
}

// Tests error handling during extraction
TEST_F(ExtractorBaseTest, ExtractWithErrors) {
    // Initialize
    EXPECT_CALL(*extractor_ptr_, connect()).WillOnce(Return(true));
    ValidationResult valid_result{true, ""};
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize();
    
    // Set up to throw exception during batch extraction
    EXPECT_CALL(*extractor_ptr_, extractBatchImpl(_))
        .WillOnce(Throw(std::runtime_error("Extraction error")));
    
    ExtractRequest request;
    request.batch_size = 10;
    request.continue_on_error = false;
    
    auto result = extractor_ptr_->extract(request);
    
    EXPECT_FALSE(result.success);
    EXPECT_FALSE(result.error_message.empty());
}

// Tests continue on error
TEST_F(ExtractorBaseTest, ContinueOnError) {
    // Initialize
    EXPECT_CALL(*extractor_ptr_, connect()).WillOnce(Return(true));
    ValidationResult valid_result{true, ""};
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize();
    
    // Set up mixed success/failure in convertToRecord
    std::vector<std::any> raw_data;
    for (int i = 0; i < 5; ++i) {
        raw_data.push_back(std::make_any<int>(i));
    }
    
    EXPECT_CALL(*extractor_ptr_, extractBatchImpl(_))
        .WillOnce(Return(raw_data))
        .WillOnce(Return(std::vector<std::any>{}));
    
    // Make some conversions fail
    for (size_t i = 0; i < raw_data.size(); ++i) {
        if (i % 2 == 0) {
            Record r;
            r.setField("id", i);
            EXPECT_CALL(*extractor_ptr_, convertToRecord(raw_data[i]))
                .WillOnce(Return(r));
        } else {
            EXPECT_CALL(*extractor_ptr_, convertToRecord(raw_data[i]))
                .WillOnce(Throw(std::runtime_error("Conversion error")));
        }
    }
    
    ExtractRequest request;
    request.batch_size = 10;
    request.continue_on_error = true;
    
    auto result = extractor_ptr_->extract(request);
    
    EXPECT_TRUE(result.success);
    EXPECT_EQ(3, result.records_extracted); // Only even indices succeeded
    EXPECT_EQ(2, result.records_failed);    // Odd indices failed
}

// Tests progress callback
TEST_F(ExtractorBaseTest, ProgressCallback) {
    // Initialize
    EXPECT_CALL(*extractor_ptr_, connect()).WillOnce(Return(true));
    ValidationResult valid_result{true, ""};
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize();
    
    // Track progress updates
    std::vector<std::pair<size_t, size_t>> progress_updates;
    extractor_ptr_->setProgressCallback(
        [&progress_updates](size_t current, size_t total) {
            progress_updates.push_back({current, total});
        });
    
    // Set up batch extraction
    std::vector<Record> batch1, batch2;
    for (int i = 0; i < 10; ++i) {
        Record r;
        r.setField("id", i);
        if (i < 5) batch1.push_back(r);
        else batch2.push_back(r);
    }
    
    EXPECT_CALL(*extractor_ptr_, extractBatchImpl(_))
        .WillOnce(Return(batch1))
        .WillOnce(Return(batch2))
        .WillOnce(Return(std::vector<Record>{}));
    
    ExtractRequest request;
    request.batch_size = 5;
    request.max_records = 10;
    
    auto result = extractor_ptr_->extract(request);
    
    // Should have received progress updates
    EXPECT_FALSE(progress_updates.empty());
    EXPECT_EQ(10, progress_updates.back().first);
}

// Tests reset functionality
TEST_F(ExtractorBaseTest, Reset) {
    // Initialize
    EXPECT_CALL(*extractor_ptr_, connect())
        .WillOnce(Return(true))
        .WillOnce(Return(true)); // For reconnection
    ValidationResult valid_result{true, ""};
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize();
    
    // Extract some data to update statistics
    std::vector<Record> mock_records;
    mock_records.push_back(Record());
    EXPECT_CALL(*extractor_ptr_, extractBatchImpl(_))
        .WillOnce(Return(mock_records))
        .WillOnce(Return(std::vector<Record>{}));
    
    extractor_ptr_->extractBatch(10);
    
    // Disconnect should be called during reset
    EXPECT_CALL(*extractor_ptr_, disconnect()).Times(1);
    
    extractor_ptr_->reset();
    
    // Statistics should be reset
    auto stats = extractor_ptr_->getStatistics();
    EXPECT_EQ(0, stats.total_records);
    EXPECT_EQ(0, stats.successful_records);
}

// Tests close functionality
TEST_F(ExtractorBaseTest, Close) {
    // Initialize
    EXPECT_CALL(*extractor_ptr_, connect()).WillOnce(Return(true));
    ValidationResult valid_result{true, ""};
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize();
    
    EXPECT_CALL(*extractor_ptr_, disconnect()).Times(1);
    
    extractor_ptr_->close();
    
    // Should not be initialized after close
    EXPECT_FALSE(extractor_ptr_->hasMore());
}

// Tests schema retrieval
TEST_F(ExtractorBaseTest, GetSchema) {
    SourceSchema schema;
    schema.source_name = "test_source";
    schema.source_type = "test";
    
    SourceSchema::Column col1{"id", "integer", false, {}, {}, "Primary key"};
    SourceSchema::Column col2{"name", "string", true, 255, {}, "Name field"};
    schema.columns = {col1, col2};
    schema.primary_keys = {"id"};
    
    EXPECT_CALL(*extractor_ptr_, getSchema())
        .WillOnce(Return(schema));
    
    auto retrieved_schema = extractor_ptr_->getSchema();
    EXPECT_EQ("test_source", retrieved_schema.source_name);
    EXPECT_EQ(2, retrieved_schema.columns.size());
    EXPECT_EQ("id", retrieved_schema.columns[0].name);
    EXPECT_FALSE(retrieved_schema.columns[0].nullable);
}

// Tests statistics collection
TEST_F(ExtractorBaseTest, Statistics) {
    // Initialize
    EXPECT_CALL(*extractor_ptr_, connect()).WillOnce(Return(true));
    ValidationResult valid_result{true, ""};
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize();
    
    // Extract some records
    std::vector<Record> mock_records;
    for (int i = 0; i < 5; ++i) {
        mock_records.push_back(Record());
    }
    
    EXPECT_CALL(*extractor_ptr_, extractBatchImpl(_))
        .WillOnce(Return(mock_records))
        .WillOnce(Return(std::vector<Record>{}));
    
    ExtractRequest request;
    request.batch_size = 10;
    auto result = extractor_ptr_->extract(request);
    
    auto stats = extractor_ptr_->getStatistics();
    EXPECT_EQ(5, stats.total_records);
    EXPECT_EQ(5, stats.successful_records);
    EXPECT_EQ(0, stats.failed_records);
    EXPECT_GT(stats.extraction_time_seconds, 0.0);
}

// Tests hasMore functionality
TEST_F(ExtractorBaseTest, HasMore) {
    // Before initialization
    EXPECT_FALSE(extractor_ptr_->hasMore());
    
    // After initialization
    EXPECT_CALL(*extractor_ptr_, connect()).WillOnce(Return(true));
    ValidationResult valid_result{true, ""};
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize();
    
    EXPECT_TRUE(extractor_ptr_->hasMore());
}

// Tests ExtractorFactory
TEST(ExtractorFactoryTest, RegisterAndCreate) {
    // Clear any existing registrations
    ExtractorFactory::getRegisteredTypes(); // Initialize if needed
    
    // Register a test extractor type
    ExtractorFactory::registerExtractor("test_type",
        [](const std::string& name,
           std::shared_ptr<Configuration> config,
           std::shared_ptr<Logger> logger) {
            return std::make_unique<MockExtractor>(name, config, logger);
        });
    
    // Create extractor
    auto config = std::make_shared<Configuration>();
    auto logger = Logger::get("test");
    auto extractor = ExtractorFactory::createExtractor("test_type", "test_instance", config, logger);
    
    ASSERT_NE(nullptr, extractor);
    EXPECT_EQ("test_instance", extractor->getName());
    
    // Test invalid type
    EXPECT_THROW(ExtractorFactory::createExtractor("invalid_type", "test", config, logger),
                 ConfigurationException);
}

// Tests getting registered types
TEST(ExtractorFactoryTest, GetRegisteredTypes) {
    // Register multiple types
    ExtractorFactory::registerExtractor("type1",
        [](const std::string& name,
           std::shared_ptr<Configuration> config,
           std::shared_ptr<Logger> logger) {
            return std::make_unique<MockExtractor>(name, config, logger);
        });
    
    ExtractorFactory::registerExtractor("type2",
        [](const std::string& name,
           std::shared_ptr<Configuration> config,
           std::shared_ptr<Logger> logger) {
            return std::make_unique<MockExtractor>(name, config, logger);
        });
    
    auto types = ExtractorFactory::getRegisteredTypes();
    EXPECT_GE(types.size(), 2);
    
    // Check that our types are registered
    bool found_type1 = std::find(types.begin(), types.end(), "type1") != types.end();
    bool found_type2 = std::find(types.begin(), types.end(), "type2") != types.end();
    
    EXPECT_TRUE(found_type1);
    EXPECT_TRUE(found_type2);
}