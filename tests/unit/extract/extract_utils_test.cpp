/**
 * @file extract_utils_test.cpp
 * @brief Unit tests for extract utilities and factory functionality
 * <AUTHOR> ETL Team
 * @date 2024
 */

#include <gtest/gtest.h>
#include "extract/extract.h"
#include "extract/extractor_factory.h"
#include "common/exceptions.h"
#include <filesystem>
#include <fstream>
#include <thread>
#include <future>

using namespace omop::extract;
using namespace omop::core;
using namespace omop::common;
using ::testing::_;
using ::testing::Return;
using ::testing::AtLeast;

// Mock extractor for testing
class MockIExtractor : public IExtractor {
public:
    MOCK_METHOD(void, initialize, 
                (const std::unordered_map<std::string, std::any>&, ProcessingContext&), 
                (override));
    MOCK_METHOD(RecordBatch, extract_batch, (size_t, ProcessingContext&), (override));
    MOCK_METHOD(bool, has_more_data, (), (const, override));
    MOCK_METHOD(std::string, get_type, (), (const, override));
    MOCK_METHOD(void, finalize, (ProcessingContext&), (override));
    MOCK_METHOD(std::unordered_map<std::string, std::any>, get_statistics, (), (const, override));
};

// Test fixture for extract utilities
class ExtractUtilsTest : public ::testing::Test {
protected:
    void SetUp() override {
        test_dir_ = std::filesystem::temp_directory_path() / "omop_extract_test";
        std::filesystem::create_directories(test_dir_);
    }

    void TearDown() override {
        std::filesystem::remove_all(test_dir_);
    }

    std::string createTestFile(const std::string& name, const std::string& content) {
        std::filesystem::path filepath = test_dir_ / name;
        std::ofstream file(filepath);
        file << content;
        file.close();
        return filepath.string();
    }

    std::filesystem::path test_dir_;
};

// Tests automatic extractor creation
TEST_F(ExtractUtilsTest, CreateExtractorAuto) {
    // Initialize extractors if not already done
    initialize_extractors();
    
    // Test CSV file detection
    std::string csv_file = createTestFile("data.csv", "id,name\n1,test\n");
    auto csv_extractor = create_extractor_auto(csv_file);
    ASSERT_NE(nullptr, csv_extractor);
    EXPECT_EQ("csv", csv_extractor->get_type());
    
    // Test JSON file detection
    std::string json_file = createTestFile("data.json", "[{\"id\":1}]");
    auto json_extractor = create_extractor_auto(json_file);
    ASSERT_NE(nullptr, json_extractor);
    EXPECT_EQ("json", json_extractor->get_type());
    
    // Test JSONL file detection
    std::string jsonl_file = createTestFile("data.jsonl", "{\"id\":1}\n{\"id\":2}\n");
    auto jsonl_extractor = create_extractor_auto(jsonl_file);
    ASSERT_NE(nullptr, jsonl_extractor);
    EXPECT_EQ("jsonl", jsonl_extractor->get_type());
    
    // Test directory detection
    auto dir_extractor = create_extractor_auto(test_dir_.string());
    ASSERT_NE(nullptr, dir_extractor);
    EXPECT_EQ("csv_directory", dir_extractor->get_type());
}

// Tests source type detection
TEST_F(ExtractUtilsTest, DetectSourceType) {
    // File extensions
    EXPECT_EQ("csv", detect_source_type("data.csv"));
    EXPECT_EQ("json", detect_source_type("data.json"));
    EXPECT_EQ("jsonl", detect_source_type("data.jsonl"));
    EXPECT_EQ("jsonl", detect_source_type("data.ndjson"));
    EXPECT_EQ("compressed_csv", detect_source_type("data.csv.gz"));
    EXPECT_EQ("compressed_csv", detect_source_type("data.csv.zip"));
    EXPECT_EQ("compressed_csv", detect_source_type("data.gz"));
    
    // Database URLs
    EXPECT_EQ("postgresql", detect_source_type("postgresql://localhost/db"));
    EXPECT_EQ("postgres", detect_source_type("postgres://localhost/db"));
    EXPECT_EQ("mysql", detect_source_type("mysql://localhost/db"));
    EXPECT_EQ("mariadb", detect_source_type("mariadb://localhost/db"));
    
    // Directory
    std::filesystem::create_directories(test_dir_ / "csv_dir");
    createTestFile("csv_dir/file1.csv", "id\n1\n");
    EXPECT_EQ("csv_directory", detect_source_type((test_dir_ / "csv_dir").string()));
    
    // Unknown
    EXPECT_EQ("", detect_source_type("unknown.xyz"));
}

// Tests configuration validation
TEST_F(ExtractUtilsTest, ValidateExtractorConfig) {
    // Valid CSV configuration
    std::unordered_map<std::string, std::any> csv_config;
    csv_config["filepath"] = createTestFile("valid.csv", "id\n1\n");
    auto [valid, error] = validate_extractor_config("csv", csv_config);
    EXPECT_TRUE(valid);
    EXPECT_TRUE(error.empty());
    
    // Missing required parameter
    std::unordered_map<std::string, std::any> invalid_config;
    auto [invalid, error_msg] = validate_extractor_config("csv", invalid_config);
    EXPECT_FALSE(invalid);
    EXPECT_FALSE(error_msg.empty());
    
    // Non-existent file
    csv_config["filepath"] = "/nonexistent/file.csv";
    auto [not_found, not_found_error] = validate_extractor_config("csv", csv_config);
    EXPECT_FALSE(not_found);
    EXPECT_NE(not_found_error.find("not found"), std::string::npos);
}

// Tests BatchExtractor
TEST_F(ExtractUtilsTest, BatchExtractor) {
    auto mock_extractor = std::make_unique<MockIExtractor>();
    auto mock_ptr = mock_extractor.get();
    
    // Set up expectations
    EXPECT_CALL(*mock_ptr, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(true))
        .WillOnce(Return(false));
    
    RecordBatch batch1, batch2;
    for (int i = 0; i < 5; ++i) {
        Record r;
        r.set_field("id", i);
        batch1.add_record(r);
    }
    for (int i = 5; i < 8; ++i) {
        Record r;
        r.set_field("id", i);
        batch2.add_record(r);
    }
    
    ProcessingContext context;
    EXPECT_CALL(*mock_ptr, extract_batch(_, _))
        .WillOnce(Return(batch1))
        .WillOnce(Return(batch2));
    
    BatchExtractor::Config config;
    config.batch_size = 5;
    
    BatchExtractor batch_extractor(std::move(mock_extractor), config);
    
    auto records = batch_extractor.extract_all();
    EXPECT_EQ(8, records.size());
}

// Tests BatchExtractor with max records
TEST_F(ExtractUtilsTest, BatchExtractorMaxRecords) {
    auto mock_extractor = std::make_unique<MockIExtractor>();
    auto mock_ptr = mock_extractor.get();
    
    EXPECT_CALL(*mock_ptr, has_more_data())
        .WillRepeatedly(Return(true));
    
    RecordBatch large_batch;
    for (int i = 0; i < 100; ++i) {
        Record r;
        r.set_field("id", i);
        large_batch.add_record(r);
    }
    
    ProcessingContext context;
    EXPECT_CALL(*mock_ptr, extract_batch(_, _))
        .WillRepeatedly(Return(large_batch));
    
    BatchExtractor::Config config;
    config.batch_size = 100;
    config.max_records = 150; // Limit total records
    
    BatchExtractor batch_extractor(std::move(mock_extractor), config);
    
    auto records = batch_extractor.extract_all();
    EXPECT_EQ(150, records.size());
}

// Tests BatchExtractor with callback
TEST_F(ExtractUtilsTest, BatchExtractorWithCallback) {
    auto mock_extractor = std::make_unique<MockIExtractor>();
    auto mock_ptr = mock_extractor.get();
    
    EXPECT_CALL(*mock_ptr, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(false));
    
    RecordBatch batch;
    for (int i = 0; i < 5; ++i) {
        Record r;
        r.set_field("id", i);
        batch.add_record(r);
    }
    
    ProcessingContext context;
    EXPECT_CALL(*mock_ptr, extract_batch(_, _))
        .WillOnce(Return(batch));
    
    BatchExtractor batch_extractor(std::move(mock_extractor));
    
    size_t callback_count = 0;
    size_t total_records = 0;
    
    size_t extracted = batch_extractor.extract_with_callback(
        [&](const RecordBatch& b) {
            callback_count++;
            total_records += b.size();
        });
    
    EXPECT_EQ(1, callback_count);
    EXPECT_EQ(5, total_records);
    EXPECT_EQ(5, extracted);
}

// Tests BatchExtractor error handling
TEST_F(ExtractUtilsTest, BatchExtractorErrorHandling) {
    auto mock_extractor = std::make_unique<MockIExtractor>();
    auto mock_ptr = mock_extractor.get();
    
    EXPECT_CALL(*mock_ptr, has_more_data())
        .WillOnce(Return(true));
    
    ProcessingContext context;
    EXPECT_CALL(*mock_ptr, extract_batch(_, _))
        .WillOnce(Throw(std::runtime_error("Extraction error")));
    
    BatchExtractor::Config config;
    config.continue_on_error = false;
    config.error_callback = [](const std::string& error) {
        EXPECT_NE(error.find("Extraction error"), std::string::npos);
    };
    
    BatchExtractor batch_extractor(std::move(mock_extractor), config);
    
    EXPECT_THROW(batch_extractor.extract_all(), std::runtime_error);
}

// Tests ParallelExtractor
TEST_F(ExtractUtilsTest, ParallelExtractor) {
    ParallelExtractor::Config config;
    config.num_threads = 2;
    
    ParallelExtractor parallel_extractor(config);
    
    // Add multiple extractors
    for (int i = 0; i < 3; ++i) {
        auto mock = std::make_unique<MockIExtractor>();
        
        EXPECT_CALL(*mock, has_more_data())
            .WillOnce(Return(true))
            .WillOnce(Return(false));
        
        RecordBatch batch;
        Record r;
        r.set_field("extractor_id", i);
        r.set_field("value", i * 100);
        batch.add_record(r);
        
        ProcessingContext context;
        EXPECT_CALL(*mock, extract_batch(_, _))
            .WillOnce(Return(batch));
        
        parallel_extractor.add_extractor(std::move(mock), 
                                        std::format("extractor_{}", i));
    }
    
    auto records = parallel_extractor.extract_all();
    EXPECT_EQ(3, records.size());
}

// Tests ParallelExtractor streaming
TEST_F(ExtractUtilsTest, ParallelExtractorStreaming) {
    ParallelExtractor parallel_extractor;
    
    auto mock = std::make_unique<MockIExtractor>();
    
    EXPECT_CALL(*mock, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(false));
    
    RecordBatch batch;
    Record r;
    r.set_field("id", 1);
    batch.add_record(r);
    
    ProcessingContext context;
    EXPECT_CALL(*mock, extract_batch(_, _))
        .WillOnce(Return(batch));
    
    parallel_extractor.add_extractor(std::move(mock), "test");
    
    std::atomic<int> batch_count{0};
    parallel_extractor.extract_streaming(
        [&batch_count](const RecordBatch& b, const std::string& name) {
            batch_count++;
            EXPECT_EQ("test", name);
            EXPECT_EQ(1, b.size());
        });
    
    EXPECT_EQ(1, batch_count);
}

// Tests ExtractorFactoryRegistry
TEST_F(ExtractUtilsTest, ExtractorFactoryRegistry) {
    // Clear registry
    ExtractorFactoryRegistry::clear();
    
    // Register a test type
    ExtractorFactoryRegistry::register_type("test_type",
        []() { return std::make_unique<MockIExtractor>(); });
    
    // Check registration
    EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered("test_type"));
    EXPECT_FALSE(ExtractorFactoryRegistry::is_type_registered("unknown_type"));
    
    // Create instance
    auto extractor = ExtractorFactoryRegistry::create("test_type");
    ASSERT_NE(nullptr, extractor);
    
    // Get registered types
    auto types = ExtractorFactoryRegistry::get_registered_types();
    EXPECT_EQ(1, types.size());
    EXPECT_EQ("test_type", types[0]);
    
    // Test unknown type
    EXPECT_THROW(ExtractorFactoryRegistry::create("unknown_type"), 
                 ConfigurationException);
}

// Tests extractor initialization
TEST_F(ExtractUtilsTest, InitializeExtractors) {
    // Clear and re-initialize
    ExtractorFactoryRegistry::clear();
    initialize_extractors();
    
    // Check that built-in types are registered
    auto types = ExtractorFactoryRegistry::get_registered_types();
    EXPECT_FALSE(types.empty());
    
    // Check specific types
    EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered("csv"));
    EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered("json"));
    EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered("postgresql"));
    EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered("mysql"));
}

// Tests ExtractorConfigBuilder
TEST_F(ExtractUtilsTest, ExtractorConfigBuilder) {
    ExtractorConfigBuilder builder("csv");
    
    builder.with_file("/path/to/file.csv")
           .set("delimiter", ',')
           .set("has_header", true)
           .with_columns({"id", "name", "value"})
           .with_filter("id > 100");
    
    auto config = builder.get_config();
    
    EXPECT_EQ("/path/to/file.csv", std::any_cast<std::string>(config.at("filepath")));
    EXPECT_EQ(',', std::any_cast<char>(config.at("delimiter")));
    EXPECT_TRUE(std::any_cast<bool>(config.at("has_header")));
    
    auto columns = std::any_cast<std::vector<std::string>>(config.at("columns"));
    EXPECT_EQ(3, columns.size());
    EXPECT_EQ("id", columns[0]);
}

// Tests database config builder
TEST_F(ExtractUtilsTest, DatabaseConfigBuilder) {
    ExtractorConfigBuilder builder("postgresql");
    
    builder.with_database("localhost", 5432, "test_db", "user", "pass")
           .with_table("person", "cdm")
           .with_columns({"person_id", "birth_datetime"})
           .with_filter("person_id > 1000");
    
    auto config = builder.get_config();
    
    EXPECT_EQ("localhost", std::any_cast<std::string>(config.at("host")));
    EXPECT_EQ(5432, std::any_cast<int>(config.at("port")));
    EXPECT_EQ("test_db", std::any_cast<std::string>(config.at("database")));
    EXPECT_EQ("person", std::any_cast<std::string>(config.at("table")));
    EXPECT_EQ("cdm", std::any_cast<std::string>(config.at("schema")));
}

// Tests utility extract functions
TEST_F(ExtractUtilsTest, UtilityExtractCsv) {
    std::string csv_content = "id,name,value\n1,John,100\n2,Jane,200\n";
    std::string csv_file = createTestFile("util_test.csv", csv_content);
    
    CsvOptions options;
    options.delimiter = ',';
    options.has_header = true;
    
    auto records = utils::extract_csv(csv_file, options);
    EXPECT_EQ(2, records.size());
    
    EXPECT_EQ("1", std::any_cast<std::string>(*records[0].get_field("id")));
    EXPECT_EQ("John", std::any_cast<std::string>(*records[0].get_field("name")));
}

// Tests utility extract JSON
TEST_F(ExtractUtilsTest, UtilityExtractJson) {
    std::string json_content = R"([
        {"id": 1, "name": "John"},
        {"id": 2, "name": "Jane"}
    ])";
    std::string json_file = createTestFile("util_test.json", json_content);
    
    JsonOptions options;
    options.flatten_nested = true;
    
    auto records = utils::extract_json(json_file, options);
    EXPECT_EQ(2, records.size());
    
    EXPECT_EQ(1LL, std::any_cast<int64_t>(*records[0].get_field("id")));
    EXPECT_EQ("Jane", std::any_cast<std::string>(*records[1].get_field("name")));
}

// Tests database URL parsing
TEST_F(ExtractUtilsTest, CreateConnectionFromUrl) {
    // This would normally connect to a real database
    // For testing, we just verify the URL parsing logic
    
    // Valid PostgreSQL URL
    std::string pg_url = "postgresql://user:pass@localhost:5432/testdb";
    EXPECT_NO_THROW({
        // Would create real connection in production
        // auto conn = utils::create_connection_from_url(pg_url);
    });
    
    // Invalid URL format
    std::string invalid_url = "not a valid url";
    EXPECT_THROW(utils::create_connection_from_url(invalid_url), 
                 ConfigurationException);
}

// Tests getting extractor info
TEST_F(ExtractUtilsTest, GetExtractorInfo) {
    auto info = get_extractor_info();
    EXPECT_FALSE(info.empty());
    
    // Find CSV info
    auto csv_info = std::find_if(info.begin(), info.end(),
        [](const ExtractorTypeInfo& i) { return i.type == "csv"; });
    
    ASSERT_NE(info.end(), csv_info);
    EXPECT_FALSE(csv_info->description.empty());
    EXPECT_FALSE(csv_info->required_params.empty());
    EXPECT_TRUE(std::find(csv_info->required_params.begin(), 
                         csv_info->required_params.end(), 
                         "filepath") != csv_info->required_params.end());
}

// Tests progress tracking
TEST_F(ExtractUtilsTest, ProgressTracking) {
    auto mock_extractor = std::make_unique<MockIExtractor>();
    auto mock_ptr = mock_extractor.get();
    
    EXPECT_CALL(*mock_ptr, has_more_data())
        .WillOnce(Return(true))
        .WillOnce(Return(true))
        .WillOnce(Return(false));
    
    RecordBatch batch;
    for (int i = 0; i < 10; ++i) {
        batch.add_record(Record());
    }
    
    ProcessingContext context;
    EXPECT_CALL(*mock_ptr, extract_batch(_, _))
        .WillOnce(Return(batch))
        .WillOnce(Return(batch));
    
    std::vector<std::pair<size_t, size_t>> progress_updates;
    
    BatchExtractor::Config config;
    config.batch_size = 10;
    config.max_records = 20;
    config.progress_callback = [&progress_updates](size_t current, size_t total) {
        progress_updates.push_back({current, total});
    };
    
    BatchExtractor batch_extractor(std::move(mock_extractor), config);
    batch_extractor.extract_all();
    
    EXPECT_FALSE(progress_updates.empty());
    EXPECT_EQ(20, progress_updates.back().first);
    EXPECT_EQ(20, progress_updates.back().second);
}

// Tests thread safety of factory
TEST_F(ExtractUtilsTest, FactoryThreadSafety) {
    const int num_threads = 10;
    const int ops_per_thread = 100;
    
    std::vector<std::future<void>> futures;
    
    for (int i = 0; i < num_threads; ++i) {
        futures.push_back(std::async(std::launch::async, [i, ops_per_thread]() {
            for (int j = 0; j < ops_per_thread; ++j) {
                // Register type
                std::string type_name = std::format("thread_{}_{}", i, j);
                ExtractorFactoryRegistry::register_type(type_name,
                    []() { return std::make_unique<MockIExtractor>(); });
                
                // Check registration
                EXPECT_TRUE(ExtractorFactoryRegistry::is_type_registered(type_name));
                
                // Create instance
                auto extractor = ExtractorFactoryRegistry::create(type_name);
                EXPECT_NE(nullptr, extractor);
            }
        }));
    }
    
    // Wait for all threads
    for (auto& future : futures) {
        future.wait();
    }
    
    // Verify all types were registered
    auto types = ExtractorFactoryRegistry::get_registered_types();
    EXPECT_GE(types.size(), num_threads * ops_per_thread);
}