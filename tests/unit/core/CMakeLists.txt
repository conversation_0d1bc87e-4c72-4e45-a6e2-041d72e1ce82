# Unit tests for core module

# Test source files in the core directory
set(CORE_TEST_SOURCES
    interfaces_test.cpp
    job_manager_test.cpp
    job_scheduler_test.cpp
    pipeline_test.cpp
    record_test.cpp
)

# Create individual test executables for each test file
foreach(test_source ${CORE_TEST_SOURCES})
    # Extract test name from filename (remove .cpp extension)
    get_filename_component(test_name ${test_source} NAME_WE)

    # Create the test executable using the parent function
    create_unit_test_executable(${test_name} ${test_source})

    # Link core library (other dependencies inherited from parent)
    target_link_libraries(${test_name}
        omop_core
        omop_common
    )

    # Add component-specific include directories
    target_include_directories(${test_name} PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/src/lib/core
        ${CMAKE_SOURCE_DIR}/src/lib/common
        ${CMAKE_CURRENT_SOURCE_DIR}
    )

    # Set test-specific properties
    set_target_properties(${test_name} PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests/unit/core
        CXX_STANDARD 20
        CXX_STANDARD_REQUIRED ON
    )

    # Add coverage flags if enabled
    if(ENABLE_COVERAGE)
        target_compile_options(${test_name} PRIVATE --coverage)
        target_link_options(${test_name} PRIVATE --coverage)
    endif()
endforeach()

# Create a combined test executable for all core tests
create_unit_test_executable(test_core_all "${CORE_TEST_SOURCES}")

# Link core library for combined test (other dependencies inherited from parent)
target_link_libraries(test_core_all
    omop_core
    omop_common
)

# Add component-specific include directories for combined test
target_include_directories(test_core_all PRIVATE
    ${CMAKE_SOURCE_DIR}/src/lib
    ${CMAKE_SOURCE_DIR}/src/lib/core
    ${CMAKE_SOURCE_DIR}/src/lib/common
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Set properties for combined test
set_target_properties(test_core_all PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/tests/unit/core
    CXX_STANDARD 20
    CXX_STANDARD_REQUIRED ON
)

# Add coverage flags if enabled for combined test
if(ENABLE_COVERAGE)
    target_compile_options(test_core_all PRIVATE --coverage)
    target_link_options(test_core_all PRIVATE --coverage)
endif()

# Create convenience target to run all core tests
add_custom_target(run_core_tests
    COMMAND ${CMAKE_CTEST_COMMAND} -L "unit" --output-on-failure
    DEPENDS test_core_all
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
    COMMENT "Running all core unit tests"
)

# Copy test data files if any exist
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/test_data)
    file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/test_data
        DESTINATION ${CMAKE_CURRENT_BINARY_DIR}
    )
endif()

# Test configuration summary
message(STATUS "Core Unit Tests Configuration:")
message(STATUS "  Test files: ${CORE_TEST_SOURCES}")
message(STATUS "  Output directory: ${CMAKE_BINARY_DIR}/tests/unit/core")
message(STATUS "  C++ Standard: 20")
