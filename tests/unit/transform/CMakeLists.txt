# tests/unit/transform/CMakeLists.txt

# Transform unit tests
set(TRANSFORM_TEST_SOURCES
    transform_utils_test.cpp
    transformation_registry_test.cpp
    field_transformation_test.cpp
    string_transformations_test.cpp
    numeric_transformations_test.cpp
    date_transformations_test.cpp
    conditional_transformations_test.cpp
    vocabulary_service_test.cpp
    vocabulary_transformations_test.cpp
    custom_transformations_test.cpp
    transformation_engine_test.cpp
    validation_engine_test.cpp
    field_transformation_helpers_test.cpp
    transform_integration_test.cpp
)

# Create test executable for each test file
foreach(test_source ${TRANSFORM_TEST_SOURCES})
    get_filename_component(test_name ${test_source} NAME_WE)
    
    add_executable(${test_name} ${test_source})
    
    target_link_libraries(${test_name}
        PRIVATE
            omop_transform
            omop_core
            omop_common
            omop_extract
            GTest::gtest
            GTest::gtest_main
    )
    
    # Add include directories
    target_include_directories(${test_name}
        PRIVATE
            ${CMAKE_SOURCE_DIR}/src/lib
    )
    
    # Set C++20 standard
    target_compile_features(${test_name} PRIVATE cxx_std_20)
    
    # Add test to CTest
    add_test(NAME transform_${test_name} COMMAND ${test_name})
    
    # Set test properties
    set_tests_properties(transform_${test_name} PROPERTIES
        TIMEOUT 60
        LABELS "transform;unit"
    )
endforeach()

# Create a single executable with all transform tests
add_executable(transform_all_tests ${TRANSFORM_TEST_SOURCES})

target_link_libraries(transform_all_tests
    PRIVATE
        omop_transform
        omop_core
        omop_common
        omop_extract
        GTest::gtest
        GTest::gtest_main
)

target_include_directories(transform_all_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
)

target_compile_features(transform_all_tests PRIVATE cxx_std_20)

# Add comprehensive test
add_test(NAME transform_all_tests COMMAND transform_all_tests)
set_tests_properties(transform_all_tests PROPERTIES
    TIMEOUT 300
    LABELS "transform;unit;all"
)

# Add custom target to run only transform tests
add_custom_target(test_transform
    COMMAND ${CMAKE_CTEST_COMMAND} -L transform --output-on-failure
    DEPENDS transform_all_tests
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
    COMMENT "Running transform module tests"
)

# Code coverage target for transform module
if(ENABLE_COVERAGE)
    add_custom_target(coverage_transform
        COMMAND ${CMAKE_COMMAND} -E make_directory ${CMAKE_BINARY_DIR}/coverage/transform
        COMMAND ${GCOV_EXECUTABLE} -b -c -o ${CMAKE_CURRENT_BINARY_DIR} ${TRANSFORM_TEST_SOURCES}
        COMMAND ${LCOV_EXECUTABLE} --capture --directory ${CMAKE_CURRENT_BINARY_DIR} 
                --output-file ${CMAKE_BINARY_DIR}/coverage/transform/coverage.info
        COMMAND ${LCOV_EXECUTABLE} --remove ${CMAKE_BINARY_DIR}/coverage/transform/coverage.info
                '/usr/*' '*/test/*' '*/external/*'
                --output-file ${CMAKE_BINARY_DIR}/coverage/transform/coverage_filtered.info
        COMMAND ${GENHTML_EXECUTABLE} ${CMAKE_BINARY_DIR}/coverage/transform/coverage_filtered.info
                --output-directory ${CMAKE_BINARY_DIR}/coverage/transform/html
        DEPENDS test_transform
        WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
        COMMENT "Generating transform module coverage report"
    )
endif()

# Memory leak detection for transform tests
if(ENABLE_SANITIZERS)
    target_compile_options(transform_all_tests PRIVATE
        -fsanitize=address,undefined
        -fno-omit-frame-pointer
    )
    
    target_link_options(transform_all_tests PRIVATE
        -fsanitize=address,undefined
    )
endif()
