# tests/unit/load/CMakeLists.txt

# Load unit tests
set(LOAD_TEST_SOURCES
    loader_base_test.cpp
    batch_loader_test.cpp
    database_loader_test.cpp
    additional_loaders_test.cpp
    database_integration_test.cpp
)

# Create test executable for each test file
foreach(test_source ${LOAD_TEST_SOURCES})
    get_filename_component(test_name ${test_source} NAME_WE)

    add_executable(${test_name} ${test_source})

    target_link_libraries(${test_name}
        PRIVATE
            omop_load
            omop_core
            omop_common
            omop_extract
            GTest::gtest
            GTest::gtest_main
            GTest::gmock
            GTest::gmock_main
    )

    # Add include directories
    target_include_directories(${test_name}
        PRIVATE
            ${CMAKE_SOURCE_DIR}/src/lib
            ${CMAKE_SOURCE_DIR}/src/lib/load
            ${CMAKE_SOURCE_DIR}/src/lib/core
            ${CMAKE_SOURCE_DIR}/src/lib/common
    )

    # Set C++20 standard
    target_compile_features(${test_name} PRIVATE cxx_std_20)

    # Add test to CTest
    add_test(NAME load_${test_name} COMMAND ${test_name})

    # Set test properties
    set_tests_properties(load_${test_name} PROPERTIES
        TIMEOUT 60
        LABELS "load;unit"
    )
endforeach()

# Create a single executable with all load tests
add_executable(load_all_tests ${LOAD_TEST_SOURCES})

target_link_libraries(load_all_tests
    PRIVATE
        omop_load
        omop_core
        omop_common
        omop_extract
        GTest::gtest
        GTest::gtest_main
        GTest::gmock
        GTest::gmock_main
)

target_include_directories(load_all_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/src/lib/load
        ${CMAKE_SOURCE_DIR}/src/lib/core
        ${CMAKE_SOURCE_DIR}/src/lib/common
)

target_compile_features(load_all_tests PRIVATE cxx_std_20)

# Add comprehensive test
add_test(NAME load_all_tests COMMAND load_all_tests)
set_tests_properties(load_all_tests PROPERTIES
    TIMEOUT 300
    LABELS "load;unit;all"
)

# Add custom target to run only load tests
add_custom_target(test_load
    COMMAND ${CMAKE_CTEST_COMMAND} -L load --output-on-failure
    DEPENDS load_all_tests
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
    COMMENT "Running load module tests"
)

# Code coverage target for load module
if(ENABLE_COVERAGE)
    add_custom_target(coverage_load
        COMMAND ${CMAKE_COMMAND} -E make_directory ${CMAKE_BINARY_DIR}/coverage/load
        COMMAND ${GCOV_EXECUTABLE} -b -c -o ${CMAKE_CURRENT_BINARY_DIR} ${LOAD_TEST_SOURCES}
        COMMAND ${LCOV_EXECUTABLE} --capture --directory ${CMAKE_CURRENT_BINARY_DIR}
                --output-file ${CMAKE_BINARY_DIR}/coverage/load/coverage.info
        COMMAND ${LCOV_EXECUTABLE} --remove ${CMAKE_BINARY_DIR}/coverage/load/coverage.info
                '/usr/*' '*/test/*' '*/external/*'
                --output-file ${CMAKE_BINARY_DIR}/coverage/load/coverage_filtered.info
        COMMAND ${GENHTML_EXECUTABLE} ${CMAKE_BINARY_DIR}/coverage/load/coverage_filtered.info
                --output-directory ${CMAKE_BINARY_DIR}/coverage/load/html
        DEPENDS test_load
        WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
        COMMENT "Generating load module coverage report"
    )
endif()

# Memory leak detection for load tests
if(ENABLE_SANITIZERS)
    target_compile_options(load_all_tests PRIVATE
        -fsanitize=address,undefined
        -fno-omit-frame-pointer
    )

    target_link_options(load_all_tests PRIVATE
        -fsanitize=address,undefined
    )
endif()

