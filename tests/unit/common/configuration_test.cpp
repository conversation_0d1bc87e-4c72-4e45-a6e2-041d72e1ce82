#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "common/configuration.h"
#include <yaml-cpp/yaml.h>
#include <fstream>
#include <filesystem>
#include <format>

using namespace omop::common;
using namespace testing;

namespace omop::common::test {

// Test fixture for TransformationRule tests
class TransformationRuleTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create basic YAML nodes for testing
        basic_rule_yaml_ = YAML::Load(R"(
            source_column: "patient_id"
            target_column: "person_id"
            type: "direct"
        )");

        multi_column_rule_yaml_ = YAML::Load(R"(
            source_columns: ["first_name", "last_name"]
            target_column: "full_name"
            type: "string_concatenation"
            separator: " "
        )");

        vocab_mapping_yaml_ = YAML::Load(R"(
            source_column: "gender_code"
            target_column: "gender_concept_id"
            type: "vocabulary_mapping"
            vocabulary: "gender_vocab"
            default_value: 0
        )");
    }

    YAML::Node basic_rule_yaml_;
    YAML::Node multi_column_rule_yaml_;
    YAML::Node vocab_mapping_yaml_;
};

// Tests for TransformationRule constructor and basic functionality
TEST_F(TransformationRuleTest, ConstructorWithBasicRule) {
    TransformationRule rule(basic_rule_yaml_);
    
    EXPECT_EQ(rule.source_column(), "patient_id");
    EXPECT_EQ(rule.target_column(), "person_id");
    EXPECT_EQ(rule.type(), TransformationRule::Type::Direct);
    EXPECT_FALSE(rule.is_multi_column());
    EXPECT_TRUE(rule.source_columns().empty());
}

TEST_F(TransformationRuleTest, ConstructorWithMultiColumnRule) {
    TransformationRule rule(multi_column_rule_yaml_);
    
    EXPECT_TRUE(rule.is_multi_column());
    EXPECT_EQ(rule.source_columns().size(), 2);
    EXPECT_EQ(rule.source_columns()[0], "first_name");
    EXPECT_EQ(rule.source_columns()[1], "last_name");
    EXPECT_EQ(rule.target_column(), "full_name");
    EXPECT_EQ(rule.type(), TransformationRule::Type::StringConcatenation);
}

TEST_F(TransformationRuleTest, ConstructorWithVocabularyMapping) {
    TransformationRule rule(vocab_mapping_yaml_);
    
    EXPECT_EQ(rule.source_column(), "gender_code");
    EXPECT_EQ(rule.target_column(), "gender_concept_id");
    EXPECT_EQ(rule.type(), TransformationRule::Type::VocabularyMapping);
    
    // Check parameters
    const auto& params = rule.parameters();
    EXPECT_TRUE(params["vocabulary"]);
    EXPECT_EQ(params["vocabulary"].as<std::string>(), "gender_vocab");
    EXPECT_TRUE(params["default_value"]);
    EXPECT_EQ(params["default_value"].as<int>(), 0);
}

TEST_F(TransformationRuleTest, ConstructorThrowsOnMissingSourceColumn) {
    YAML::Node invalid_yaml = YAML::Load(R"(
        target_column: "person_id"
        type: "direct"
    )");
    
    EXPECT_THROW(TransformationRule rule(invalid_yaml), ConfigurationException);
}

TEST_F(TransformationRuleTest, ConstructorThrowsOnMissingTargetColumn) {
    YAML::Node invalid_yaml = YAML::Load(R"(
        source_column: "patient_id"
        type: "direct"
    )");
    
    EXPECT_THROW(TransformationRule rule(invalid_yaml), ConfigurationException);
}

TEST_F(TransformationRuleTest, ConstructorThrowsOnInvalidType) {
    YAML::Node invalid_yaml = YAML::Load(R"(
        source_column: "patient_id"
        target_column: "person_id"
        type: "invalid_type"
    )");
    
    EXPECT_THROW(TransformationRule rule(invalid_yaml), ConfigurationException);
}

TEST_F(TransformationRuleTest, AllTransformationTypes) {
    std::vector<std::pair<std::string, TransformationRule::Type>> type_mappings = {
        {"direct", TransformationRule::Type::Direct},
        {"date_transform", TransformationRule::Type::DateTransform},
        {"vocabulary_mapping", TransformationRule::Type::VocabularyMapping},
        {"date_calculation", TransformationRule::Type::DateCalculation},
        {"numeric_transform", TransformationRule::Type::NumericTransform},
        {"string_concatenation", TransformationRule::Type::StringConcatenation},
        {"conditional", TransformationRule::Type::Conditional},
        {"custom", TransformationRule::Type::Custom}
    };
    
    for (const auto& [type_str, expected_type] : type_mappings) {
        YAML::Node yaml = YAML::Load(std::format(R"(
            source_column: "test_col"
            target_column: "target_col"
            type: "{}"
        )", type_str));
        
        TransformationRule rule(yaml);
        EXPECT_EQ(rule.type(), expected_type) << "Failed for type: " << type_str;
    }
}

// Test fixture for TableMapping tests
class TableMappingTest : public ::testing::Test {
protected:
    void SetUp() override {
        basic_mapping_yaml_ = YAML::Load(R"(
            source_table: "patients"
            target_table: "person"
            transformations:
              - source_column: "patient_id"
                target_column: "person_id"
                type: "direct"
              - source_column: "birth_date"
                target_column: "birth_datetime"
                type: "date_transform"
                format: "YYYY-MM-DD"
        )");
    }

    YAML::Node basic_mapping_yaml_;
};

TEST_F(TableMappingTest, ConstructorWithBasicMapping) {
    TableMapping mapping(basic_mapping_yaml_);
    
    EXPECT_EQ(mapping.source_table(), "patients");
    EXPECT_EQ(mapping.target_table(), "person");
    EXPECT_EQ(mapping.transformations().size(), 2);
    EXPECT_FALSE(mapping.pre_process_sql().has_value());
    EXPECT_FALSE(mapping.post_process_sql().has_value());
}

TEST_F(TableMappingTest, ConstructorThrowsOnMissingSourceTable) {
    YAML::Node invalid_yaml = YAML::Load(R"(
        target_table: "person"
        transformations: []
    )");
    
    EXPECT_THROW(TableMapping mapping(invalid_yaml), ConfigurationException);
}

TEST_F(TableMappingTest, ConstructorThrowsOnMissingTargetTable) {
    YAML::Node invalid_yaml = YAML::Load(R"(
        source_table: "patients"
        transformations: []
    )");
    
    EXPECT_THROW(TableMapping mapping(invalid_yaml), ConfigurationException);
}

// Test fixture for DatabaseConfig tests
class DatabaseConfigTest : public ::testing::Test {
protected:
    void SetUp() override {
        postgresql_config_yaml_ = YAML::Load(R"(
            type: "postgresql"
            host: "localhost"
            port: 5432
            database: "omop_cdm"
            username: "postgres"
            password: "password123"
            parameters:
              sslmode: "require"
              connect_timeout: "30"
        )");

        connection_string_config_yaml_ = YAML::Load(R"(
            type: "mysql"
            connection_string: "mysql://user:pass@localhost:3306/omop_db"
        )");
    }

    YAML::Node postgresql_config_yaml_;
    YAML::Node connection_string_config_yaml_;
};

TEST_F(DatabaseConfigTest, ConstructorWithIndividualParameters) {
    DatabaseConfig config(postgresql_config_yaml_);
    
    EXPECT_EQ(config.type(), DatabaseConfig::Type::PostgreSQL);
    EXPECT_EQ(config.host(), "localhost");
    EXPECT_EQ(config.port(), 5432);
    EXPECT_EQ(config.database(), "omop_cdm");
    EXPECT_EQ(config.username(), "postgres");
    EXPECT_EQ(config.password(), "password123");
    
    const auto& params = config.parameters();
    EXPECT_EQ(params.at("sslmode"), "require");
    EXPECT_EQ(params.at("connect_timeout"), "30");
}

TEST_F(DatabaseConfigTest, ConstructorWithConnectionString) {
    DatabaseConfig config(connection_string_config_yaml_);
    
    EXPECT_EQ(config.type(), DatabaseConfig::Type::MySQL);
    EXPECT_EQ(config.connection_string(), "mysql://user:pass@localhost:3306/omop_db");
}

TEST_F(DatabaseConfigTest, DefaultPortsForDifferentDatabaseTypes) {
    std::vector<std::pair<std::string, std::pair<DatabaseConfig::Type, int>>> db_types = {
        {"postgresql", {DatabaseConfig::Type::PostgreSQL, 5432}},
        {"mysql", {DatabaseConfig::Type::MySQL, 3306}},
        {"mssql", {DatabaseConfig::Type::MSSQL, 1433}},
        {"oracle", {DatabaseConfig::Type::Oracle, 1521}}
    };
    
    for (const auto& [type_str, type_info] : db_types) {
        YAML::Node yaml = YAML::Load(std::format(R"(
            type: "{}"
            host: "localhost"
            database: "test_db"
            username: "user"
        )", type_str));
        
        DatabaseConfig config(yaml);
        EXPECT_EQ(config.type(), type_info.first) << "Failed for type: " << type_str;
        EXPECT_EQ(config.port(), type_info.second) << "Failed for type: " << type_str;
    }
}

TEST_F(DatabaseConfigTest, ConstructorThrowsOnInvalidType) {
    YAML::Node invalid_yaml = YAML::Load(R"(
        type: "invalid_db_type"
        host: "localhost"
        database: "test_db"
        username: "user"
    )");
    
    EXPECT_THROW(DatabaseConfig config(invalid_yaml), ConfigurationException);
}

// Test fixture for ConfigurationManager tests
class ConfigurationManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create temporary directory for file tests
        temp_dir_ = std::filesystem::temp_directory_path() / "omop_config_test";
        std::filesystem::create_directories(temp_dir_);
    }

    void TearDown() override {
        // Clean up temporary files
        std::filesystem::remove_all(temp_dir_);
    }

    std::filesystem::path temp_dir_;

    std::string createSimpleConfig() {
        return "source_database:\n"
               "  type: postgresql\n"
               "  host: localhost\n"
               "  database: source_db\n"
               "  username: user\n"
               "target_database:\n"
               "  type: postgresql\n"
               "  host: localhost\n"
               "  database: omop_cdm\n"
               "  username: omop_user\n"
               "table_mappings:\n"
               "  patients:\n"
               "    source_table: patients\n"
               "    target_table: person\n"
               "    transformations:\n"
               "      - source_column: patient_id\n"
               "        target_column: person_id\n"
               "        type: direct\n";
    }

    void writeConfigFile(const std::string& content, const std::string& filename = "config.yaml") {
        auto filepath = temp_dir_ / filename;
        std::ofstream file(filepath);
        file << content;
        file.close();
    }
};

TEST_F(ConfigurationManagerTest, LoadConfigFromString) {
    ConfigurationManager manager;
    std::string config_content = createSimpleConfig();

    EXPECT_FALSE(manager.is_loaded());
    EXPECT_NO_THROW(manager.load_config_from_string(config_content));
    EXPECT_TRUE(manager.is_loaded());
}

TEST_F(ConfigurationManagerTest, LoadConfigFromFile) {
    ConfigurationManager manager;
    std::string config_content = createSimpleConfig();
    writeConfigFile(config_content);

    auto config_file = temp_dir_ / "config.yaml";
    EXPECT_NO_THROW(manager.load_config(config_file.string()));
    EXPECT_TRUE(manager.is_loaded());
}

TEST_F(ConfigurationManagerTest, LoadConfigThrowsOnInvalidFile) {
    ConfigurationManager manager;
    EXPECT_THROW(manager.load_config("/nonexistent/file.yaml"), ConfigurationException);
}

TEST_F(ConfigurationManagerTest, LoadConfigThrowsOnInvalidYaml) {
    ConfigurationManager manager;
    std::string invalid_yaml = "invalid: yaml: content: [unclosed";
    EXPECT_THROW(manager.load_config_from_string(invalid_yaml), ConfigurationException);
}

TEST_F(ConfigurationManagerTest, DatabaseConfigurationsParsedCorrectly) {
    ConfigurationManager manager;
    std::string config_content = createSimpleConfig();
    manager.load_config_from_string(config_content);

    const auto& source_db = manager.get_source_db();
    EXPECT_EQ(source_db.type(), DatabaseConfig::Type::PostgreSQL);
    EXPECT_EQ(source_db.host(), "localhost");
    EXPECT_EQ(source_db.database(), "source_db");
    EXPECT_EQ(source_db.username(), "user");

    const auto& target_db = manager.get_target_db();
    EXPECT_EQ(target_db.type(), DatabaseConfig::Type::PostgreSQL);
    EXPECT_EQ(target_db.database(), "omop_cdm");
    EXPECT_EQ(target_db.username(), "omop_user");
}

TEST_F(ConfigurationManagerTest, TableMappingsParsedCorrectly) {
    ConfigurationManager manager;
    std::string config_content = createSimpleConfig();
    manager.load_config_from_string(config_content);

    auto patients_mapping = manager.get_table_mapping("patients");
    ASSERT_TRUE(patients_mapping.has_value());

    EXPECT_EQ(patients_mapping->source_table(), "patients");
    EXPECT_EQ(patients_mapping->target_table(), "person");
    EXPECT_EQ(patients_mapping->transformations().size(), 1);

    const auto& transformations = patients_mapping->transformations();
    EXPECT_EQ(transformations[0].source_column(), "patient_id");
    EXPECT_EQ(transformations[0].target_column(), "person_id");
    EXPECT_EQ(transformations[0].type(), TransformationRule::Type::Direct);
}

TEST_F(ConfigurationManagerTest, GetTableMappingReturnsNulloptForNonexistent) {
    ConfigurationManager manager;
    std::string config_content = createSimpleConfig();
    manager.load_config_from_string(config_content);

    auto mapping = manager.get_table_mapping("nonexistent_table");
    EXPECT_FALSE(mapping.has_value());
}

TEST_F(ConfigurationManagerTest, GetAllMappingsReturnsAllTables) {
    ConfigurationManager manager;
    std::string config_content = createSimpleConfig();
    manager.load_config_from_string(config_content);

    const auto& all_mappings = manager.get_all_mappings();
    EXPECT_EQ(all_mappings.size(), 1);
    EXPECT_TRUE(all_mappings.find("patients") != all_mappings.end());
}

// Test Config singleton functionality
TEST_F(ConfigurationManagerTest, ConfigSingleton) {
    auto& instance1 = Config::instance();
    auto& instance2 = Config::instance();

    // Should be the same instance
    EXPECT_EQ(&instance1, &instance2);

    // Test that it works as expected
    std::string config_content = createSimpleConfig();
    instance1.load_config_from_string(config_content);

    EXPECT_TRUE(instance2.is_loaded());

    auto mapping = instance2.get_table_mapping("patients");
    EXPECT_TRUE(mapping.has_value());
}

} // namespace omop::common::test
