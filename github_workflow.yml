name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ created ]

env:
  CMAKE_VERSION: 3.23.0
  BUILD_TYPE: Release

jobs:
  build-and-test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-22.04, ubuntu-20.04]
        compiler: [gcc-11, gcc-12, clang-13, clang-14]
        include:
          - os: ubuntu-22.04
            compiler: gcc-11
            cc: gcc-11
            cxx: g++-11
          - os: ubuntu-22.04
            compiler: gcc-12
            cc: gcc-12
            cxx: g++-12
          - os: ubuntu-22.04
            compiler: clang-13
            cc: clang-13
            cxx: clang++-13
          - os: ubuntu-22.04
            compiler: clang-14
            cc: clang-14
            cxx: clang++-14

    steps:
    - uses: actions/checkout@v3
      with:
        submodules: recursive

    - name: Install Dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          libpq-dev \
          libyaml-cpp-dev \
          libfmt-dev \
          libspdlog-dev \
          unixodbc-dev \
          postgresql-client \
          lcov

    - name: Install nlohmann-json
      run: |
        wget https://github.com/nlohmann/json/releases/download/v3.11.2/json.hpp
        sudo mv json.hpp /usr/local/include/

    - name: Configure CMake
      env:
        CC: ${{ matrix.cc }}
        CXX: ${{ matrix.cxx }}
      run: |
        cmake -B build \
          -DCMAKE_BUILD_TYPE=$BUILD_TYPE \
          -DBUILD_TESTS=ON \
          -DENABLE_COVERAGE=ON

    - name: Build
      run: cmake --build build --config $BUILD_TYPE --parallel $(nproc)

    - name: Run Tests
      run: |
        cd build
        ctest -C $BUILD_TYPE --verbose --output-on-failure

    - name: Generate Coverage Report
      if: matrix.compiler == 'gcc-11'
      run: |
        cd build
        lcov --capture --directory . --output-file coverage.info
        lcov --remove coverage.info '/usr/*' --output-file coverage.info
        lcov --list coverage.info

    - name: Upload Coverage to Codecov
      if: matrix.compiler == 'gcc-11'
      uses: codecov/codecov-action@v3
      with:
        file: ./build/coverage.info
        flags: unittests
        name: codecov-umbrella

  static-analysis:
    runs-on: ubuntu-22.04
    steps:
    - uses: actions/checkout@v3

    - name: Install Analysis Tools
      run: |
        sudo apt-get update
        sudo apt-get install -y cppcheck clang-tidy-14 clang-format-14

    - name: Run clang-format
      run: |
        find src -name "*.cpp" -o -name "*.h" | xargs clang-format-14 --dry-run --Werror

    - name: Run cppcheck
      run: |
        cppcheck --enable=all --error-exitcode=1 \
          --suppress=missingIncludeSystem \
          --inline-suppr \
          -I src/lib \
          src/

    - name: Run clang-tidy
      run: |
        cmake -B build -DCMAKE_EXPORT_COMPILE_COMMANDS=ON
        find src -name "*.cpp" | xargs clang-tidy-14 \
          -p build \
          --checks='-*,bugprone-*,clang-analyzer-*,modernize-*,performance-*,readability-*' \
          --warnings-as-errors='*'

  integration-tests:
    runs-on: ubuntu-22.04
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_pass
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - uses: actions/checkout@v3

    - name: Install Dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          libpq-dev \
          libyaml-cpp-dev \
          libfmt-dev \
          libspdlog-dev \
          postgresql-client

    - name: Setup Test Database
      run: |
        PGPASSWORD=test_pass psql -h localhost -U test_user -d test_db < tests/integration/setup.sql

    - name: Build
      run: |
        cmake -B build -DBUILD_TESTS=ON -DBUILD_INTEGRATION_TESTS=ON
        cmake --build build --parallel $(nproc)

    - name: Run Integration Tests
      env:
        TEST_DB_HOST: localhost
        TEST_DB_PORT: 5432
        TEST_DB_USER: test_user
        TEST_DB_PASS: test_pass
        TEST_DB_NAME: test_db
      run: |
        cd build
        ctest -R integration --verbose --output-on-failure

  docker-build:
    runs-on: ubuntu-22.04
    steps:
    - uses: actions/checkout@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Login to DockerHub
      if: github.event_name != 'pull_request'
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Build and Push Docker Image
      uses: docker/build-push-action@v4
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: ${{ github.event_name != 'pull_request' }}
        tags: |
          ${{ secrets.DOCKER_USERNAME }}/omop-etl:latest
          ${{ secrets.DOCKER_USERNAME }}/omop-etl:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  documentation:
    runs-on: ubuntu-22.04
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
    - uses: actions/checkout@v3

    - name: Install Doxygen
      run: |
        sudo apt-get update
        sudo apt-get install -y doxygen graphviz

    - name: Generate Documentation
      run: |
        cmake -B build -DBUILD_DOCS=ON
        cmake --build build --target docs

    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./build/docs/html

  release:
    needs: [build-and-test, static-analysis, integration-tests, docker-build]
    runs-on: ubuntu-22.04
    if: github.event_name == 'release'
    steps:
    - uses: actions/checkout@v3

    - name: Install Dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          libpq-dev \
          libyaml-cpp-dev \
          libfmt-dev \
          libspdlog-dev

    - name: Build Release
      run: |
        cmake -B build \
          -DCMAKE_BUILD_TYPE=Release \
          -DBUILD_TESTS=OFF
        cmake --build build --parallel $(nproc)

    - name: Create Package
      run: |
        cd build
        cpack -G DEB
        cpack -G RPM
        cpack -G TGZ

    - name: Upload Release Assets
      uses: softprops/action-gh-release@v1
      with:
        files: |
          build/*.deb
          build/*.rpm
          build/*.tar.gz

  security-scan:
    runs-on: ubuntu-22.04
    steps:
    - uses: actions/checkout@v3

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Run Snyk to check for vulnerabilities
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high