# Load library CMakeLists.txt
set(LOAD_SOURCES
    batch_loader.cpp
    database_loader.cpp
    loader_base.cpp
)

set(LOAD_HEADERS
    batch_loader.h
    database_loader.h
    loader_base.h
)

include_directories(${CMAKE_CURRENT_SOURCE_DIR}
                    ${CMAKE_SOURCE_DIR}/src/lib/common
                    ${CMAKE_SOURCE_DIR}/src/lib/core
)

# Create load library
add_library(omop_load STATIC ${LOAD_SOURCES})

# Set include directories
target_include_directories(omop_load
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/..>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_SOURCE_DIR}/src/lib/common
        ${CMAKE_SOURCE_DIR}/src/lib/core
)

# Link dependencies
target_link_libraries(omop_load
    PUBLIC
        omop_core
        omop_common
        omop_extract
        omop_cdm
    PRIVATE
        Threads::Threads
)


# Set compile features and flags
target_compile_features(omop_load PUBLIC cxx_std_20)

# Platform-specific compile options
if(MSVC)
    target_compile_options(omop_load PRIVATE /W4 /WX)
else()
    target_compile_options(omop_load PRIVATE -Wall -Wextra -Wpedantic)
endif()

# Enable position independent code
set_target_properties(omop_load PROPERTIES POSITION_INDEPENDENT_CODE ON)

# Export compile commands for IDE support
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# Install rules
install(TARGETS omop_load
    EXPORT omop-etl-targets
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES ${LOAD_HEADERS}
    DESTINATION include/omop/load
)