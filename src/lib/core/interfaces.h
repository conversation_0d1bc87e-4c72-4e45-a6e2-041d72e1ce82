#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <any>
#include <functional>
#include <variant>
#include <optional>
#include <chrono>
#include <concepts>
#include <iostream>
#include <format>
#include <mutex>
#include <spdlog/spdlog.h>

#include "common/exceptions.h"
#include "common/logging.h"
#include "record.h"

namespace omop::core {

/**
 * @brief Forward declarations
 */
class ProcessingContext;
class ValidationResult;

/**
 * @brief Concept for record-like types
 */
template<typename T>
concept RecordLike = requires(T t) {
    { t.getField(std::string{}) } -> std::convertible_to<std::optional<std::any>>;
    { t.setField(std::string{}, std::any{}) } -> std::same_as<void>;
    { t.getFieldNames() } -> std::convertible_to<std::vector<std::string>>;
};

/**
 * @brief Processing context for ETL operations
 *
 * This class provides contextual information and services to ETL components
 * during processing, including configuration access, logging, and metrics.
 */
class ProcessingContext {
public:
    /**
     * @brief Processing stage enumeration
     */
    enum class Stage {
        Extract,
        Transform,
        Load
    };

    /**
     * @brief Default constructor
     */
    ProcessingContext() : start_time_(std::chrono::steady_clock::now()) {}

    /**
     * @brief Get current processing stage
     * @return Stage Current stage
     */
    [[nodiscard]] Stage current_stage() const noexcept { return current_stage_; }

    /**
     * @brief Set current processing stage
     * @param stage Processing stage
     */
    void set_stage(Stage stage) { current_stage_ = stage; }

    /**
     * @brief Get job ID
     * @return const std::string& Job identifier
     */
    [[nodiscard]] const std::string& job_id() const noexcept { return job_id_; }

    /**
     * @brief Set job ID
     * @param id Job identifier
     */
    void set_job_id(std::string id) { job_id_ = std::move(id); }

    /**
     * @brief Increment processed record count
     * @param count Number of records to add
     */
    void increment_processed(size_t count = 1) { processed_count_ += count; }

    /**
     * @brief Increment error count
     * @param count Number of errors to add
     */
    void increment_errors(size_t count = 1) { error_count_ += count; }

    /**
     * @brief Get processed record count
     * @return size_t Number of processed records
     */
    [[nodiscard]] size_t processed_count() const noexcept { return processed_count_; }

    /**
     * @brief Get error count
     * @return size_t Number of errors
     */
    [[nodiscard]] size_t error_count() const noexcept { return error_count_; }

    /**
     * @brief Get elapsed time
     * @return std::chrono::duration<double> Elapsed time in seconds
     */
    [[nodiscard]] std::chrono::duration<double> elapsed_time() const {
        return std::chrono::steady_clock::now() - start_time_;
    }

    /**
     * @brief Store context data
     * @param key Data key
     * @param value Data value
     */
    void set_data(const std::string& key, std::any value);

    /**
     * @brief Retrieve context data
     * @param key Data key
     * @return std::optional<std::any> Data value if exists
     */
    [[nodiscard]] std::optional<std::any> get_data(const std::string& key) const;

    /**
     * @brief Log message
     * @param level Log level
     * @param message Log message
     */
    void log(const std::string& level, const std::string& message);

private:
    Stage current_stage_{Stage::Extract};
    std::string job_id_;
    size_t processed_count_{0};
    size_t error_count_{0};
    std::chrono::steady_clock::time_point start_time_;
    mutable std::mutex context_mutex_;
    std::unordered_map<std::string, std::any> context_data_;
};

/**
 * @brief Base interface for data extraction
 *
 * This interface defines the contract for all data extractors in the ETL pipeline.
 * Extractors are responsible for reading data from various sources and converting
 * them into Record objects.
 */
class IExtractor {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~IExtractor() = default;

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    virtual void initialize(const std::unordered_map<std::string, std::any>& config,
                           ProcessingContext& context) = 0;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return RecordBatch Extracted records (empty if no more data)
     */
    virtual RecordBatch extract_batch(size_t batch_size, ProcessingContext& context) = 0;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    virtual bool has_more_data() const = 0;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    virtual std::string get_type() const = 0;

    /**
     * @brief Finalize extraction and clean up resources
     * @param context Processing context
     */
    virtual void finalize(ProcessingContext& context) = 0;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    virtual std::unordered_map<std::string, std::any> get_statistics() const = 0;
};

/**
 * @brief Base interface for data transformation
 *
 * This interface defines the contract for all data transformers in the ETL pipeline.
 * Transformers are responsible for converting, validating, and enriching records
 * according to business rules and mapping configurations.
 */
class ITransformer {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~ITransformer() = default;

    /**
     * @brief Initialize the transformer
     * @param config Configuration parameters
     * @param context Processing context
     */
    virtual void initialize(const std::unordered_map<std::string, std::any>& config,
                           ProcessingContext& context) = 0;

    /**
     * @brief Transform a single record
     * @param record Record to transform
     * @param context Processing context
     * @return std::optional<Record> Transformed record or empty if filtered out
     */
    virtual std::optional<Record> transform(const Record& record,
                                          ProcessingContext& context) = 0;

    /**
     * @brief Transform a batch of records
     * @param batch Batch to transform
     * @param context Processing context
     * @return RecordBatch Transformed batch
     */
    virtual RecordBatch transform_batch(const RecordBatch& batch,
                                       ProcessingContext& context) = 0;

    /**
     * @brief Get transformer type name
     * @return std::string Transformer type identifier
     */
    virtual std::string get_type() const = 0;

    /**
     * @brief Validate record according to transformation rules
     * @param record Record to validate
     * @return ValidationResult Validation result
     */
    virtual ValidationResult validate(const Record& record) const = 0;

    /**
     * @brief Get transformation statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    virtual std::unordered_map<std::string, std::any> get_statistics() const = 0;
};

/**
 * @brief Base interface for data loading
 *
 * This interface defines the contract for all data loaders in the ETL pipeline.
 * Loaders are responsible for writing transformed records to target destinations.
 */
class ILoader {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~ILoader() = default;

    /**
     * @brief Initialize the loader
     * @param config Configuration parameters
     * @param context Processing context
     */
    virtual void initialize(const std::unordered_map<std::string, std::any>& config,
                           ProcessingContext& context) = 0;

    /**
     * @brief Load a single record
     * @param record Record to load
     * @param context Processing context
     * @return bool True if successfully loaded
     */
    virtual bool load(const Record& record, ProcessingContext& context) = 0;

    /**
     * @brief Load a batch of records
     * @param batch Batch to load
     * @param context Processing context
     * @return size_t Number of successfully loaded records
     */
    virtual size_t load_batch(const RecordBatch& batch, ProcessingContext& context) = 0;

    /**
     * @brief Commit pending changes
     * @param context Processing context
     */
    virtual void commit(ProcessingContext& context) = 0;

    /**
     * @brief Rollback pending changes
     * @param context Processing context
     */
    virtual void rollback(ProcessingContext& context) = 0;

    /**
     * @brief Get loader type name
     * @return std::string Loader type identifier
     */
    virtual std::string get_type() const = 0;

    /**
     * @brief Finalize loading and clean up resources
     * @param context Processing context
     */
    virtual void finalize(ProcessingContext& context) = 0;

    /**
     * @brief Get loading statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    virtual std::unordered_map<std::string, std::any> get_statistics() const = 0;
};

/**
 * @brief Validation result for record validation
 */
class ValidationResult {
public:
    struct ValidationError {
        std::string field_name;
        std::string error_message;
        std::string rule_name;
    };

    /**
     * @brief Default constructor (valid result)
     */
    ValidationResult() = default;

    /**
     * @brief Add validation error
     * @param error Validation error
     */
    void add_error(ValidationError error) {
        errors_.push_back(std::move(error));
        is_valid_ = false;
    }

    /**
     * @brief Add validation error with field, message and rule
     * @param field_name Field name
     * @param error_message Error message
     * @param rule_name Rule name
     */
    void add_error(const std::string& field_name, 
                  const std::string& error_message,
                  const std::string& rule_name) {
        errors_.push_back({field_name, error_message, rule_name});
        is_valid_ = false;
    }

    /**
     * @brief Check if validation passed
     * @return bool True if valid
     */
    [[nodiscard]] bool is_valid() const noexcept { return is_valid_; }

    /**
     * @brief Get validation errors
     * @return const std::vector<ValidationError>& Vector of errors
     */
    [[nodiscard]] const std::vector<ValidationError>& errors() const noexcept {
        return errors_;
    }

    /**
     * @brief Get error count
     * @return size_t Number of errors
     */
    [[nodiscard]] size_t error_count() const noexcept { return errors_.size(); }
    
    /**
     * @brief Get error messages as a formatted string
     * @return std::string Formatted error messages
     */
    [[nodiscard]] std::string error_messages() const {
        if (errors_.empty()) {
            return "";
        }
        
        std::string result;
        for (const auto& error : errors_) {
            result += std::format("Field '{}': {} (rule: {})\n", 
                                 error.field_name, 
                                 error.error_message,
                                 error.rule_name);
        }
        return result;
    }
    
    /**
     * @brief Merge another validation result
     * @param other Other validation result
     */
    void merge(const ValidationResult& other) {
        if (!other.is_valid()) {
            is_valid_ = false;
            errors_.insert(errors_.end(), other.errors().begin(), other.errors().end());
        }
    }

private:
    bool is_valid_{true};
    std::vector<ValidationError> errors_;
};

/**
 * @brief Factory for creating ETL components
 */
template<typename T>
class ComponentFactory {
public:
    using Creator = std::function<std::unique_ptr<T>()>;

    /**
     * @brief Register component creator
     * @param type Component type name
     * @param creator Creator function
     */
    void register_creator(const std::string& type, Creator creator) {
        creators_[type] = std::move(creator);
    }

    /**
     * @brief Create component by type
     * @param type Component type name
     * @return std::unique_ptr<T> Created component
     */
    [[nodiscard]] std::unique_ptr<T> create(const std::string& type) const {
        auto it = creators_.find(type);
        if (it != creators_.end()) {
            return it->second();
        }
        throw common::ConfigurationException(
            std::format("Unknown component type: '{}'", type));
    }

    /**
     * @brief Get registered types
     * @return std::vector<std::string> Vector of type names
     */
    [[nodiscard]] std::vector<std::string> get_registered_types() const {
        std::vector<std::string> types;
        types.reserve(creators_.size());
        for (const auto& [type, _] : creators_) {
            types.push_back(type);
        }
        return types;
    }
    
    /**
     * @brief Check if a type is registered
     * @param type Component type name
     * @return bool True if type is registered
     */
    [[nodiscard]] bool is_registered(const std::string& type) const {
        return creators_.find(type) != creators_.end();
    }
    
    /**
     * @brief Get number of registered types
     * @return size_t Number of registered types
     */
    [[nodiscard]] size_t registered_count() const noexcept {
        return creators_.size();
    }

private:
    std::unordered_map<std::string, Creator> creators_;
};

} // namespace omop::core