/**
 * @file record.cpp
 * @brief Implementation of data record representation for OMOP ETL pipeline
 * <AUTHOR> ETL Team
 * @date 2024
 */

#include "record.h"
#include <nlohmann/json.hpp>
#include <sstream>
#include <iomanip>
#include <stdexcept>

namespace omop::core {

// Record implementation
Record::Record(const std::unordered_map<std::string, std::any>& data)
    : fields_(data) {
    metadata_.extraction_time = std::chrono::system_clock::now();
}

Record::Record(const std::unordered_map<std::string, std::any>& data,
               const RecordMetadata& metadata)
    : fields_(data), metadata_(metadata) {}

void Record::setField(const std::string& field_name, const std::any& value) {
    fields_[field_name] = value;
}

const std::any& Record::getField(const std::string& field_name) const {
    auto it = fields_.find(field_name);
    if (it == fields_.end()) {
        throw std::out_of_range("Field not found: " + field_name);
    }
    return it->second;
}

std::optional<std::any> Record::getFieldOptional(const std::string& field_name) const {
    auto it = fields_.find(field_name);
    if (it != fields_.end()) {
        return it->second;
    }
    return std::nullopt;
}

bool Record::hasField(const std::string& field_name) const {
    return fields_.find(field_name) != fields_.end();
}

bool Record::isFieldNull(const std::string& field_name) const {
    auto it = fields_.find(field_name);
    if (it == fields_.end()) {
        return true;
    }
    return !it->second.has_value();
}

bool Record::removeField(const std::string& field_name) {
    auto removed = fields_.erase(field_name);
    field_metadata_.erase(field_name);
    return removed > 0;
}

void Record::clear() {
    fields_.clear();
    field_metadata_.clear();
}

std::vector<std::string> Record::getFieldNames() const {
    std::vector<std::string> names;
    names.reserve(fields_.size());
    for (const auto& [name, _] : fields_) {
        names.push_back(name);
    }
    return names;
}

void Record::setFieldMetadata(const std::string& field_name, const FieldMetadata& metadata) {
    field_metadata_[field_name] = metadata;
}

std::optional<Record::FieldMetadata> Record::getFieldMetadata(const std::string& field_name) const {
    auto it = field_metadata_.find(field_name);
    if (it != field_metadata_.end()) {
        return it->second;
    }
    return std::nullopt;
}

void Record::merge(const Record& other, bool overwrite) {
    for (const auto& [name, value] : other.fields_) {
        if (overwrite || !hasField(name)) {
            fields_[name] = value;
        }
    }

    // Merge field metadata
    for (const auto& [name, metadata] : other.field_metadata_) {
        if (overwrite || field_metadata_.find(name) == field_metadata_.end()) {
            field_metadata_[name] = metadata;
        }
    }

    // Merge custom metadata
    for (const auto& [key, value] : other.metadata_.custom) {
        if (overwrite || metadata_.custom.find(key) == metadata_.custom.end()) {
            metadata_.custom[key] = value;
        }
    }
}

Record Record::selectFields(const std::vector<std::string>& field_names) const {
    Record result;
    result.metadata_ = metadata_;

    for (const auto& name : field_names) {
        if (hasField(name)) {
            result.fields_[name] = fields_.at(name);
            auto metadata = getFieldMetadata(name);
            if (metadata) {
                result.field_metadata_[name] = *metadata;
            }
        }
    }

    return result;
}

bool Record::renameField(const std::string& old_name, const std::string& new_name) {
    auto it = fields_.find(old_name);
    if (it == fields_.end() || fields_.find(new_name) != fields_.end()) {
        return false;
    }

    // Move field value
    fields_[new_name] = std::move(it->second);
    fields_.erase(it);

    // Move field metadata if exists
    auto meta_it = field_metadata_.find(old_name);
    if (meta_it != field_metadata_.end()) {
        field_metadata_[new_name] = std::move(meta_it->second);
        field_metadata_.erase(meta_it);
    }

    return true;
}

std::string Record::toJson(bool pretty) const {
    nlohmann::json j;

    // Convert fields
    nlohmann::json fields;
    for (const auto& [name, value] : fields_) {
        if (!value.has_value()) {
            fields[name] = nullptr;
            continue;
        }

        // Convert std::any to JSON value
        if (value.type() == typeid(bool)) {
            fields[name] = std::any_cast<bool>(value);
        } else if (value.type() == typeid(int)) {
            fields[name] = std::any_cast<int>(value);
        } else if (value.type() == typeid(int32_t)) {
            fields[name] = std::any_cast<int32_t>(value);
        } else if (value.type() == typeid(int64_t)) {
            fields[name] = std::any_cast<int64_t>(value);
        } else if (value.type() == typeid(double)) {
            fields[name] = std::any_cast<double>(value);
        } else if (value.type() == typeid(float)) {
            fields[name] = std::any_cast<float>(value);
        } else if (value.type() == typeid(std::string)) {
            fields[name] = std::any_cast<std::string>(value);
        } else if (value.type() == typeid(const char*)) {
            fields[name] = std::any_cast<const char*>(value);
        } else if (value.type() == typeid(std::chrono::system_clock::time_point)) {
            auto tp = std::any_cast<std::chrono::system_clock::time_point>(value);
            auto time_t = std::chrono::system_clock::to_time_t(tp);
            fields[name] = std::to_string(time_t);
        } else {
            fields[name] = "unsupported_type";
        }
    }
    j["fields"] = fields;

    // Add metadata
    j["metadata"]["source_table"] = metadata_.source_table;
    j["metadata"]["target_table"] = metadata_.target_table;
    j["metadata"]["source_row_number"] = metadata_.source_row_number;
    j["metadata"]["record_id"] = metadata_.record_id;
    j["metadata"]["extraction_time"] = std::chrono::system_clock::to_time_t(metadata_.extraction_time);
    j["metadata"]["custom"] = metadata_.custom;

    return pretty ? j.dump(2) : j.dump();
}

Record Record::fromJson(const std::string& json) {
    auto j = nlohmann::json::parse(json);
    Record record;

    // Parse fields
    if (j.contains("fields")) {
        for (auto& [key, value] : j["fields"].items()) {
            if (value.is_null()) {
                continue;
            } else if (value.is_boolean()) {
                record.fields_[key] = value.get<bool>();
            } else if (value.is_number_integer()) {
                record.fields_[key] = value.get<int64_t>();
            } else if (value.is_number_float()) {
                record.fields_[key] = value.get<double>();
            } else if (value.is_string()) {
                record.fields_[key] = value.get<std::string>();
            }
        }
    }

    // Parse metadata
    if (j.contains("metadata")) {
        auto& meta = j["metadata"];
        if (meta.contains("source_table")) {
            record.metadata_.source_table = meta["source_table"];
        }
        if (meta.contains("target_table")) {
            record.metadata_.target_table = meta["target_table"];
        }
        if (meta.contains("source_row_number")) {
            record.metadata_.source_row_number = meta["source_row_number"];
        }
        if (meta.contains("record_id")) {
            record.metadata_.record_id = meta["record_id"];
        }
        if (meta.contains("extraction_time")) {
            auto time_t = meta["extraction_time"].get<std::time_t>();
            record.metadata_.extraction_time = std::chrono::system_clock::from_time_t(time_t);
        }
        if (meta.contains("custom")) {
            for (auto& [key, value] : meta["custom"].items()) {
                record.metadata_.custom[key] = value.get<std::string>();
            }
        }
    }

    return record;
}

std::string Record::toString() const {
    std::stringstream ss;
    ss << "Record(";
    if (!metadata_.record_id.empty()) {
        ss << "id=" << metadata_.record_id << ", ";
    }
    ss << "fields=" << fields_.size();
    if (!metadata_.source_table.empty()) {
        ss << ", source=" << metadata_.source_table;
    }
    if (!metadata_.target_table.empty()) {
        ss << ", target=" << metadata_.target_table;
    }
    ss << ")";
    return ss.str();
}

bool Record::operator==(const Record& other) const {
    if (fields_.size() != other.fields_.size()) {
        return false;
    }

    for (const auto& [name, value] : fields_) {
        auto it = other.fields_.find(name);
        if (it == other.fields_.end()) {
            return false;
        }

        // Compare std::any values
        if (value.type() != it->second.type()) {
            return false;
        }

        // Type-specific comparison
        try {
            if (value.type() == typeid(bool)) {
                if (std::any_cast<bool>(value) != std::any_cast<bool>(it->second)) {
                    return false;
                }
            } else if (value.type() == typeid(int32_t)) {
                if (std::any_cast<int32_t>(value) != std::any_cast<int32_t>(it->second)) {
                    return false;
                }
            } else if (value.type() == typeid(int64_t)) {
                if (std::any_cast<int64_t>(value) != std::any_cast<int64_t>(it->second)) {
                    return false;
                }
            } else if (value.type() == typeid(double)) {
                if (std::any_cast<double>(value) != std::any_cast<double>(it->second)) {
                    return false;
                }
            } else if (value.type() == typeid(std::string)) {
                if (std::any_cast<std::string>(value) != std::any_cast<std::string>(it->second)) {
                    return false;
                }
            }
        } catch (...) {
            return false;
        }
    }

    return true;
}

// RecordBatch implementation
RecordBatch::RecordBatch(size_t capacity) {
    records_.reserve(capacity);
}

void RecordBatch::addRecord(const Record& record) {
    records_.push_back(record);
}

void RecordBatch::addRecord(Record&& record) {
    records_.push_back(std::move(record));
}

const Record& RecordBatch::getRecord(size_t index) const {
    if (index >= records_.size()) {
        throw std::out_of_range("Index out of range");
    }
    return records_[index];
}

Record& RecordBatch::getRecordMutable(size_t index) {
    if (index >= records_.size()) {
        throw std::out_of_range("Index out of range");
    }
    return records_[index];
}

} // namespace omop::core