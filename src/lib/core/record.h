/**
 * @file record.h
 * @brief Data record representation for OMOP ETL pipeline
 * <AUTHOR> ETL Team
 * @date 2024
 *
 * This file contains the Record class which represents a single data record
 * flowing through the ETL pipeline.
 */

#pragma once

#include <string>
#include <unordered_map>
#include <any>
#include <vector>
#include <chrono>
#include <optional>
#include <memory>
#include <variant>

namespace omop::core {

/**
 * @brief Represents a single data record in the ETL pipeline
 *
 * This class encapsulates a data record as it flows through the pipeline,
 * providing a unified interface for accessing and manipulating field values
 * regardless of the source or target data format.
 */
class Record {
public:
    /**
     * @brief Field metadata
     */
    struct FieldMetadata {
        std::string name;           ///< Field name
        std::string data_type;      ///< Data type
        bool is_nullable{true};     ///< Whether field can be null
        std::string source_column;  ///< Original source column name
        std::string description;    ///< Field description
    };

    /**
     * @brief Record metadata
     */
    struct RecordMetadata {
        std::string source_table;   ///< Source table/file name
        std::string target_table;   ///< Target table name
        size_t source_row_number{0}; ///< Row number in source
        std::chrono::system_clock::time_point extraction_time; ///< When record was extracted
        std::string record_id;      ///< Unique record identifier
        std::unordered_map<std::string, std::string> custom; ///< Custom metadata
    };

    /**
     * @brief Default constructor
     */
    Record() = default;

    /**
     * @brief Constructor with initial data
     * @param data Initial field data
     */
    explicit Record(const std::unordered_map<std::string, std::any>& data);

    /**
     * @brief Constructor with metadata
     * @param data Initial field data
     * @param metadata Record metadata
     */
    Record(const std::unordered_map<std::string, std::any>& data,
           const RecordMetadata& metadata);

    /**
     * @brief Copy constructor
     */
    Record(const Record& other) = default;

    /**
     * @brief Move constructor
     */
    Record(Record&& other) noexcept = default;

    /**
     * @brief Copy assignment operator
     */
    Record& operator=(const Record& other) = default;

    /**
     * @brief Move assignment operator
     */
    Record& operator=(Record&& other) noexcept = default;

    /**
     * @brief Destructor
     */
    virtual ~Record() = default;

    /**
     * @brief Set a field value
     * @param field_name Field name
     * @param value Field value
     */
    void setField(const std::string& field_name, const std::any& value);

    /**
     * @brief Get a field value
     * @param field_name Field name
     * @return Field value
     * @throws std::out_of_range if field not found
     */
    const std::any& getField(const std::string& field_name) const;

    /**
     * @brief Get a field value with type conversion
     * @tparam T Target type
     * @param field_name Field name
     * @return Field value as type T
     * @throws std::bad_any_cast if type conversion fails
     */
    template<typename T>
    T getFieldAs(const std::string& field_name) const {
        const auto& value = getField(field_name);
        return std::any_cast<T>(value);
    }

    /**
     * @brief Get optional field value
     * @param field_name Field name
     * @return Optional containing field value if exists
     */
    std::optional<std::any> getFieldOptional(const std::string& field_name) const;

    /**
     * @brief Check if field exists
     * @param field_name Field name
     * @return true if field exists
     */
    bool hasField(const std::string& field_name) const;

    /**
     * @brief Check if field is null
     * @param field_name Field name
     * @return true if field is null or doesn't exist
     */
    bool isFieldNull(const std::string& field_name) const;

    /**
     * @brief Remove a field
     * @param field_name Field name
     * @return true if field was removed
     */
    bool removeField(const std::string& field_name);

    /**
     * @brief Clear all fields
     */
    void clear();

    /**
     * @brief Get all field names
     * @return Vector of field names
     */
    std::vector<std::string> getFieldNames() const;

    /**
     * @brief Get all fields
     * @return Map of field names to values
     */
    const std::unordered_map<std::string, std::any>& getFields() const { return fields_; }

    /**
     * @brief Get mutable reference to fields
     * @return Mutable map of field names to values
     */
    std::unordered_map<std::string, std::any>& getFieldsMutable() { return fields_; }

    /**
     * @brief Get number of fields
     * @return Field count
     */
    size_t getFieldCount() const { return fields_.size(); }

    /**
     * @brief Check if record is empty
     * @return true if no fields
     */
    bool isEmpty() const { return fields_.empty(); }

    /**
     * @brief Get record metadata
     * @return Record metadata
     */
    const RecordMetadata& getMetadata() const { return metadata_; }

    /**
     * @brief Get mutable record metadata
     * @return Mutable record metadata
     */
    RecordMetadata& getMetadataMutable() { return metadata_; }

    /**
     * @brief Set record metadata
     * @param metadata New metadata
     */
    void setMetadata(const RecordMetadata& metadata) { metadata_ = metadata; }

    /**
     * @brief Set field metadata
     * @param field_name Field name
     * @param metadata Field metadata
     */
    void setFieldMetadata(const std::string& field_name, const FieldMetadata& metadata);

    /**
     * @brief Get field metadata
     * @param field_name Field name
     * @return Optional field metadata
     */
    std::optional<FieldMetadata> getFieldMetadata(const std::string& field_name) const;

    /**
     * @brief Merge another record into this one
     * @param other Record to merge
     * @param overwrite Whether to overwrite existing fields
     */
    void merge(const Record& other, bool overwrite = true);

    /**
     * @brief Create a copy with selected fields
     * @param field_names Fields to include
     * @return New record with selected fields
     */
    Record selectFields(const std::vector<std::string>& field_names) const;

    /**
     * @brief Rename a field
     * @param old_name Current field name
     * @param new_name New field name
     * @return true if field was renamed
     */
    bool renameField(const std::string& old_name, const std::string& new_name);

    /**
     * @brief Convert record to JSON string
     * @param pretty Whether to format with indentation
     * @return JSON representation
     */
    std::string toJson(bool pretty = false) const;

    /**
     * @brief Create record from JSON string
     * @param json JSON string
     * @return Record object
     */
    static Record fromJson(const std::string& json);

    /**
     * @brief Convert record to string representation
     * @return String representation
     */
    std::string toString() const;

    /**
     * @brief Equality operator
     * @param other Other record
     * @return true if records are equal
     */
    bool operator==(const Record& other) const;

    /**
     * @brief Inequality operator
     * @param other Other record
     * @return true if records are not equal
     */
    bool operator!=(const Record& other) const { return !(*this == other); }

private:
    std::unordered_map<std::string, std::any> fields_;           ///< Field data
    std::unordered_map<std::string, FieldMetadata> field_metadata_; ///< Field metadata
    RecordMetadata metadata_;                                     ///< Record metadata
};

/**
 * @brief Batch of records for processing
 */
class RecordBatch {
public:
    /**
     * @brief Default constructor
     */
    RecordBatch() = default;

    /**
     * @brief Constructor with initial capacity
     * @param capacity Initial capacity
     */
    explicit RecordBatch(size_t capacity);

    /**
     * @brief Add a record to the batch
     * @param record Record to add
     */
    void addRecord(const Record& record);

    /**
     * @brief Add a record to the batch (move)
     * @param record Record to add
     */
    void addRecord(Record&& record);

    /**
     * @brief Get record at index
     * @param index Record index
     * @return Reference to record
     */
    const Record& getRecord(size_t index) const;

    /**
     * @brief Get mutable record at index
     * @param index Record index
     * @return Mutable reference to record
     */
    Record& getRecordMutable(size_t index);

    /**
     * @brief Get all records
     * @return Vector of records
     */
    const std::vector<Record>& getRecords() const { return records_; }

    /**
     * @brief Get mutable records
     * @return Mutable vector of records
     */
    std::vector<Record>& getRecordsMutable() { return records_; }

    /**
     * @brief Get batch size
     * @return Number of records in batch
     */
    size_t size() const { return records_.size(); }

    /**
     * @brief Check if batch is empty
     * @return true if no records
     */
    bool isEmpty() const { return records_.empty(); }

    /**
     * @brief Check if batch is empty (STL-style)
     * @return true if no records
     */
    bool empty() const { return records_.empty(); }

    /**
     * @brief Clear all records
     */
    void clear() { records_.clear(); }

    /**
     * @brief Reserve capacity
     * @param capacity Capacity to reserve
     */
    void reserve(size_t capacity) { records_.reserve(capacity); }

    /**
     * @brief Iterator support
     */
    auto begin() { return records_.begin(); }
    auto end() { return records_.end(); }
    auto begin() const { return records_.begin(); }
    auto end() const { return records_.end(); }

private:
    std::vector<Record> records_; ///< Batch of records
};

} // namespace core::omop