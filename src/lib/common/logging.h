/**
 * @file logging.h
 * @brief Logging utilities for OMOP ETL pipeline
 * <AUTHOR> ETL Team
 * @date 2024
 */

#pragma once

#include <string>
#include <memory>
#include <chrono>
#include <unordered_map>
#include <vector>
#include <functional>
#include <mutex>
#include <optional>
#include <any>
#include <spdlog/spdlog.h>
#include <spdlog/sinks/sink.h>
#include <spdlog/fmt/ostr.h>

// Forward declarations for external dependencies
namespace omop::extract {
    class IDatabaseConnection;
}

namespace omop::common {

/**
 * @brief Log level enumeration
 */
enum class LogLevel {
    Trace,
    Debug,
    Info,
    Warning,
    Error,
    Critical
};

/**
 * @brief Structured log entry
 *
 * Represents a single log entry with structured data for better analysis.
 */
struct LogEntry {
    std::chrono::system_clock::time_point timestamp;
    LogLevel level;
    std::string logger_name;
    std::string message;
    std::string thread_id;
    std::string job_id;
    std::string component;
    std::string operation;
    std::unordered_map<std::string, std::any> context;
    std::optional<std::string> error_code;
    std::optional<std::string> stack_trace;
};

/**
 * @brief Log formatter interface
 */
class ILogFormatter {
public:
    virtual ~ILogFormatter() = default;

    /**
     * @brief Format log entry
     * @param entry Log entry
     * @return std::string Formatted log message
     */
    virtual std::string format(const LogEntry& entry) = 0;
};

/**
 * @brief JSON log formatter
 *
 * Formats log entries as JSON for structured logging.
 */
class JsonLogFormatter : public ILogFormatter {
public:
    /**
     * @brief Format log entry as JSON
     * @param entry Log entry
     * @return std::string JSON formatted log
     */
    std::string format(const LogEntry& entry) override;

    /**
     * @brief Set whether to pretty print JSON
     * @param pretty Whether to pretty print
     */
    void set_pretty_print(bool pretty) { pretty_print_ = pretty; }

private:
    bool pretty_print_{false};
};

/**
 * @brief Plain text log formatter
 *
 * Formats log entries as human-readable text.
 */
class TextLogFormatter : public ILogFormatter {
public:
    /**
     * @brief Constructor
     * @param pattern Log pattern (default: "[%Y-%m-%d %H:%M:%S.%e] [%l] [%n] %v")
     */
    explicit TextLogFormatter(const std::string& pattern =
        "[%Y-%m-%d %H:%M:%S.%e] [%l] [%n] %v");

    /**
     * @brief Format log entry as text
     * @param entry Log entry
     * @return std::string Text formatted log
     */
    std::string format(const LogEntry& entry) override;

private:
    std::string pattern_;
};

/**
 * @brief Log sink interface
 */
class ILogSink {
public:
    virtual ~ILogSink() = default;

    /**
     * @brief Write log entry
     * @param entry Log entry
     */
    virtual void write(const LogEntry& entry) = 0;

    /**
     * @brief Flush buffered logs
     */
    virtual void flush() = 0;

    /**
     * @brief Set formatter
     * @param formatter Log formatter
     */
    void set_formatter(std::unique_ptr<ILogFormatter> formatter) {
        formatter_ = std::move(formatter);
    }

protected:
    std::unique_ptr<ILogFormatter> formatter_;
};

/**
 * @brief Database log sink
 *
 * Writes log entries to a database table for centralized logging.
 */
class DatabaseLogSink : public ILogSink {
public:
    /**
     * @brief Constructor
     * @param connection Database connection
     * @param table_name Log table name
     */
    DatabaseLogSink(std::shared_ptr<omop::extract::IDatabaseConnection> connection,
                    const std::string& table_name = "etl_logs");

    void write(const LogEntry& entry) override;
    void flush() override;

    /**
     * @brief Create log table if not exists
     */
    void create_table_if_not_exists();

private:
    std::shared_ptr<omop::extract::IDatabaseConnection> connection_;
    std::string table_name_;
    std::vector<LogEntry> buffer_;
    std::mutex buffer_mutex_;
    size_t buffer_size_{100};

    void flush_buffer();
};

/**
 * @brief ETL-specific logger
 *
 * Provides logging functionality tailored for ETL operations with
 * support for job tracking, component identification, and metrics.
 */
class Logger {
    friend class LoggingConfig;
public:
    /**
     * @brief Get logger instance
     * @param name Logger name
     * @return std::shared_ptr<Logger> Logger instance
     */
    static std::shared_ptr<Logger> get(const std::string& name);

    /**
     * @brief Constructor
     * @param name Logger name
     */
    explicit Logger(const std::string& name);

    /**
     * @brief Set log level
     * @param level Log level
     */
    void set_level(LogLevel level);

    /**
     * @brief Add log sink
     * @param sink Log sink
     */
    void add_sink(std::shared_ptr<ILogSink> sink);

    /**
     * @brief Clear all sinks
     */
    void clear_sinks() { sinks_.clear(); }

    /**
     * @brief Set job context
     * @param job_id Job identifier
     */
    void set_job_id(const std::string& job_id) { job_id_ = job_id; }

    /**
     * @brief Set component context
     * @param component Component name
     */
    void set_component(const std::string& component) { component_ = component; }

    // Logging methods
    template<typename... Args>
    void trace(const std::string& msg, Args&&... args) {
        log(LogLevel::Trace, msg, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void debug(const std::string& msg, Args&&... args) {
        log(LogLevel::Debug, msg, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void info(const std::string& msg, Args&&... args) {
        log(LogLevel::Info, msg, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void warn(const std::string& msg, Args&&... args) {
        log(LogLevel::Warning, msg, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void error(const std::string& msg, Args&&... args) {
        log(LogLevel::Error, msg, std::forward<Args>(args)...);
    }

    template<typename... Args>
    void critical(const std::string& msg, Args&&... args) {
        log(LogLevel::Critical, msg, std::forward<Args>(args)...);
    }

    /**
     * @brief Log with structured context
     * @param level Log level
     * @param msg Message
     * @param context Additional context
     */
    void log_structured(LogLevel level, const std::string& msg,
                       const std::unordered_map<std::string, std::any>& context);

    /**
     * @brief Log ETL operation
     * @param operation Operation name
     * @param status Operation status
     * @param details Operation details
     */
    void log_operation(const std::string& operation,
                      const std::string& status,
                      const std::unordered_map<std::string, std::any>& details = {});

    /**
     * @brief Log ETL metrics
     * @param metrics Metrics map
     */
    void log_metrics(const std::unordered_map<std::string, double>& metrics);

    /**
     * @brief Log exception
     * @param e Exception
     * @param context Additional context
     */
    void log_exception(const std::exception& e,
                      const std::unordered_map<std::string, std::any>& context = {});

private:
    template<typename... Args>
    void log(LogLevel level, const std::string& format, Args&&... args) {
        if (level < min_level_) return;

        auto formatted = fmt::format(fmt::runtime(format), std::forward<Args>(args)...);
        LogEntry entry{
            .timestamp = std::chrono::system_clock::now(),
            .level = level,
            .logger_name = name_,
            .message = formatted,
            .thread_id = get_thread_id(),
            .job_id = job_id_,
            .component = component_
        };

        write_entry(entry);
    }

    void write_entry(const LogEntry& entry);
    std::string get_thread_id() const;

    std::string name_;
    std::string job_id_;
    std::string component_;
    LogLevel min_level_{LogLevel::Info};
    std::vector<std::shared_ptr<ILogSink>> sinks_;
    std::shared_ptr<spdlog::logger> spdlog_logger_;

    static std::unordered_map<std::string, std::shared_ptr<Logger>> loggers_;
    static std::mutex loggers_mutex_;
};

/**
 * @brief Performance logger
 *
 * Specialized logger for tracking performance metrics and timings.
 */
class PerformanceLogger {
public:
    /**
     * @brief Constructor
     * @param logger Base logger
     */
    explicit PerformanceLogger(std::shared_ptr<Logger> logger)
        : logger_(logger) {}

    /**
     * @brief Start timing an operation
     * @param operation_id Operation identifier
     */
    void start_timing(const std::string& operation_id);

    /**
     * @brief End timing an operation
     * @param operation_id Operation identifier
     * @param record_count Records processed (optional)
     */
    void end_timing(const std::string& operation_id,
                   std::optional<size_t> record_count = std::nullopt);

    /**
     * @brief Log throughput
     * @param operation Operation name
     * @param records_per_second Records per second
     */
    void log_throughput(const std::string& operation, double records_per_second);

    /**
     * @brief Log resource usage
     * @param cpu_percent CPU usage percentage
     * @param memory_mb Memory usage in MB
     * @param disk_io_mb Disk I/O in MB
     */
    void log_resource_usage(double cpu_percent, double memory_mb, double disk_io_mb);

    /**
     * @brief RAII timer for automatic timing
     */
    class ScopedTimer {
    public:
        ScopedTimer(PerformanceLogger& logger, const std::string& operation_id)
            : logger_(logger), operation_id_(operation_id) {
            logger_.start_timing(operation_id_);
        }

        ~ScopedTimer() {
            if (record_count_.has_value()) {
                logger_.end_timing(operation_id_, record_count_);
            } else {
                logger_.end_timing(operation_id_);
            }
        }

        void set_record_count(size_t count) { record_count_ = count; }

    private:
        PerformanceLogger& logger_;
        std::string operation_id_;
        std::optional<size_t> record_count_;
    };

    /**
     * @brief Create scoped timer
     * @param operation_id Operation identifier
     * @return ScopedTimer Timer object
     */
    [[nodiscard]] ScopedTimer scoped_timer(const std::string& operation_id) {
        return ScopedTimer(*this, operation_id);
    }

private:
    std::shared_ptr<Logger> logger_;
    std::unordered_map<std::string, std::chrono::steady_clock::time_point> timings_;
    std::mutex timings_mutex_;
};

/**
 * @brief Audit logger
 *
 * Specialized logger for audit trail and compliance logging.
 */
class AuditLogger {
public:
    /**
     * @brief Constructor
     * @param logger Base logger
     */
    explicit AuditLogger(std::shared_ptr<Logger> logger)
        : logger_(logger) {}

    /**
     * @brief Log data access
     * @param table_name Table accessed
     * @param operation Operation type (read/write)
     * @param record_count Number of records
     * @param user User identifier
     */
    void log_data_access(const std::string& table_name,
                        const std::string& operation,
                        size_t record_count,
                        const std::string& user);

    /**
     * @brief Log configuration change
     * @param config_item Configuration item
     * @param old_value Old value
     * @param new_value New value
     * @param user User making the change
     */
    void log_config_change(const std::string& config_item,
                          const std::string& old_value,
                          const std::string& new_value,
                          const std::string& user);

    /**
     * @brief Log security event
     * @param event_type Event type
     * @param details Event details
     */
    void log_security_event(const std::string& event_type,
                           const std::unordered_map<std::string, std::string>& details);

private:
    std::shared_ptr<Logger> logger_;
};

/**
 * @brief Global logging configuration
 */
class LoggingConfig {
public:
    /**
     * @brief Initialize logging system
     * @param config_file Configuration file path
     */
    static void initialize(const std::string& config_file);

    /**
     * @brief Initialize with default configuration
     */
    static void initialize_default();

    /**
     * @brief Set global log level
     * @param level Log level
     */
    static void set_global_level(LogLevel level);

    /**
     * @brief Add global sink
     * @param sink Log sink
     */
    static void add_global_sink(std::shared_ptr<ILogSink> sink);

    /**
     * @brief Flush all loggers
     */
    static void flush_all();

    /**
     * @brief Shutdown logging system
     */
    static void shutdown();
};

// Convenience macros
#define LOG_TRACE(logger, ...) (logger)->trace(__VA_ARGS__)
#define LOG_DEBUG(logger, ...) (logger)->debug(__VA_ARGS__)
#define LOG_INFO(logger, ...) (logger)->info(__VA_ARGS__)
#define LOG_WARN(logger, ...) (logger)->warn(__VA_ARGS__)
#define LOG_ERROR(logger, ...) (logger)->error(__VA_ARGS__)
#define LOG_CRITICAL(logger, ...) (logger)->critical(__VA_ARGS__)

} // namespace omop::common