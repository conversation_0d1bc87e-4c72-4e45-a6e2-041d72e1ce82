#include "configuration.h"

#include <fstream>
#include <sstream>
#include <algorithm>
#include <ranges>

namespace omop::common {

// TransformationRule implementation
TransformationRule::TransformationRule(const YAML::Node& node) {
    if (node["source_column"]) {
        source_column_ = node["source_column"].as<std::string>();
    } else if (node["source_columns"]) {
        source_columns_ = node["source_columns"].as<std::vector<std::string>>();
    } else {
        throw ConfigurationException("Transformation rule must have either 'source_column' or 'source_columns'");
    }

    if (!node["target_column"]) {
        throw ConfigurationException("Transformation rule must have 'target_column'");
    }
    target_column_ = node["target_column"].as<std::string>();

    if (node["type"]) {
        static const std::unordered_map<std::string, Type> type_map = {
            {"direct", Type::Direct},
            {"date_transform", Type::DateTransform},
            {"vocabulary_mapping", Type::VocabularyMapping},
            {"date_calculation", Type::DateCalculation},
            {"numeric_transform", Type::NumericTransform},
            {"string_concatenation", Type::StringConcatenation},
            {"conditional", Type::Conditional},
            {"custom", Type::Custom}
        };

        std::string type_str = node["type"].as<std::string>();
        auto it = type_map.find(type_str);
        if (it != type_map.end()) {
            type_ = it->second;
        } else {
            throw ConfigurationException(
                std::format("Unknown transformation type: '{}'", type_str), "type");
        }
    }

    // Store all other parameters
    for (const auto& item : node) {
        const std::string& key = item.first.as<std::string>();
        if (key != "source_column" && key != "source_columns" &&
            key != "target_column" && key != "type") {
            parameters_[key] = item.second;
        }
    }
}

// TableMapping implementation
TableMapping::TableMapping(const YAML::Node& node) {
    if (!node["source_table"]) {
        throw ConfigurationException("Table mapping must have 'source_table'");
    }
    source_table_ = node["source_table"].as<std::string>();

    if (!node["target_table"]) {
        throw ConfigurationException("Table mapping must have 'target_table'");
    }
    target_table_ = node["target_table"].as<std::string>();

    if (node["transformations"]) {
        for (const auto& trans_node : node["transformations"]) {
            transformations_.emplace_back(trans_node);
        }
    }

    if (node["pre_process_sql"]) {
        pre_process_sql_ = node["pre_process_sql"].as<std::string>();
    }

    if (node["post_process_sql"]) {
        post_process_sql_ = node["post_process_sql"].as<std::string>();
    }

    if (node["filters"]) {
        filters_ = node["filters"];
    }

    if (node["validations"]) {
        validations_ = node["validations"];
    }
}

// DatabaseConfig implementation
DatabaseConfig::DatabaseConfig(const YAML::Node& node) {
    if (node["type"]) {
        static const std::unordered_map<std::string, Type> type_map = {
            {"postgresql", Type::PostgreSQL},
            {"mysql", Type::MySQL},
            {"mssql", Type::MSSQL},
            {"oracle", Type::Oracle}
        };

        std::string type_str = node["type"].as<std::string>();
        std::transform(type_str.begin(), type_str.end(), type_str.begin(), ::tolower);

        auto it = type_map.find(type_str);
        if (it != type_map.end()) {
            type_ = it->second;
        } else {
            throw ConfigurationException(
                std::format("Unknown database type: '{}'", type_str), "type");
        }
    }

    if (node["connection_string"]) {
        connection_string_ = node["connection_string"].as<std::string>();
    } else {
        // Build connection from individual parameters
        if (!node["host"]) {
            throw ConfigurationException("Database configuration must have 'host' or 'connection_string'");
        }
        host_ = node["host"].as<std::string>();

        if (node["port"]) {
            port_ = node["port"].as<int>();
        } else {
            // Set default ports based on database type
            switch (type_) {
                case Type::PostgreSQL: port_ = 5432; break;
                case Type::MySQL: port_ = 3306; break;
                case Type::MSSQL: port_ = 1433; break;
                case Type::Oracle: port_ = 1521; break;
            }
        }

        if (!node["database"]) {
            throw ConfigurationException("Database configuration must have 'database'");
        }
        database_ = node["database"].as<std::string>();

        if (!node["username"]) {
            throw ConfigurationException("Database configuration must have 'username'");
        }
        username_ = node["username"].as<std::string>();

        if (node["password"]) {
            password_ = node["password"].as<std::string>();
        }
    }

    // Parse additional parameters
    if (node["parameters"]) {
        const auto& params_node = node["parameters"];
        for (const auto& param : params_node) {
            parameters_[param.first.as<std::string>()] = param.second.as<std::string>();
        }
    }
}

// ConfigurationManager implementation
void ConfigurationManager::load_config(const std::string& filepath) {
    std::ifstream file(filepath);
    if (!file.is_open()) {
        throw ConfigurationException(
            std::format("Failed to open configuration file: '{}'", filepath));
    }

    std::stringstream buffer;
    buffer << file.rdbuf();
    load_config_from_string(buffer.str());
}

void ConfigurationManager::load_config_from_string(const std::string& yaml_content) {
    try {
        root_config_ = YAML::Load(yaml_content);
    } catch (const YAML::Exception& e) {
        throw ConfigurationException(
            std::format("Failed to parse YAML configuration: {}", e.what()));
    }

    // Parse source database configuration
    if (!root_config_["source_database"]) {
        throw ConfigurationException("Configuration must have 'source_database' section");
    }
    source_db_ = parse_database_config(root_config_["source_database"]);

    // Parse target database configuration
    if (!root_config_["target_database"]) {
        throw ConfigurationException("Configuration must have 'target_database' section");
    }
    target_db_ = parse_database_config(root_config_["target_database"]);

    // Parse table mappings
    if (root_config_["table_mappings"]) {
        parse_table_mappings(root_config_["table_mappings"]);
    }

    // Parse vocabulary mappings
    if (root_config_["vocabulary_mappings"]) {
        vocabulary_mappings_ = root_config_["vocabulary_mappings"];
    }

    // Parse ETL settings
    if (root_config_["etl_settings"]) {
        etl_settings_ = root_config_["etl_settings"];
    } else {
        // Set default ETL settings
        etl_settings_["batch_size"] = 1000;
        etl_settings_["parallel_workers"] = 4;
        etl_settings_["validation_mode"] = "strict";
        etl_settings_["error_threshold"] = 0.01;
    }

    config_loaded_ = true;
    validate_config();
}

std::optional<TableMapping> ConfigurationManager::get_table_mapping(
    const std::string& table_name) const {
    auto it = table_mappings_.find(table_name);
    if (it != table_mappings_.end()) {
        return it->second;
    }
    return std::nullopt;
}

std::optional<YAML::Node> ConfigurationManager::get_value(const std::string& key) const {
    if (!config_loaded_) {
        return std::nullopt;
    }

    // Split key by dots to navigate nested structure
    std::vector<std::string> parts;
    std::stringstream ss(key);
    std::string part;
    while (std::getline(ss, part, '.')) {
        parts.push_back(part);
    }

    YAML::Node current = root_config_;
    for (const auto& p : parts) {
        if (!current[p]) {
            return std::nullopt;
        }
        current = current[p];
    }

    return current;
}

void ConfigurationManager::validate_config() const {
    if (!config_loaded_) {
        throw ConfigurationException("Configuration not loaded");
    }

    // Validate that we have at least one table mapping
    if (table_mappings_.empty()) {
        throw ConfigurationException("No table mappings defined in configuration");
    }

    // Validate each table mapping
    for (const auto& [name, mapping] : table_mappings_) {
        if (mapping.transformations().empty()) {
            throw ConfigurationException(
                std::format("Table mapping '{}' has no transformations defined", name));
        }

        // Validate that all transformation rules are properly configured
        for (const auto& rule : mapping.transformations()) {
            if (rule.target_column().empty()) {
                throw ConfigurationException(
                    std::format("Empty target column in table mapping '{}'", name));
            }

            if (!rule.is_multi_column() && rule.source_column().empty()) {
                throw ConfigurationException(
                    std::format("Empty source column in table mapping '{}'", name));
            }

            // Validate vocabulary mappings reference existing vocabularies
            if (rule.type() == TransformationRule::Type::VocabularyMapping) {
                auto vocab_param = rule.parameters()["vocabulary"];
                if (!vocab_param || !vocab_param.IsDefined()) {
                    throw ConfigurationException(
                        std::format("Vocabulary mapping in table '{}' missing vocabulary parameter", name));
                }

                std::string vocab_name = vocab_param.as<std::string>();
                if (!vocabulary_mappings_[vocab_name]) {
                    throw ConfigurationException(
                        std::format("Referenced vocabulary '{}' not found in vocabulary_mappings", vocab_name),
                        vocab_name);
                }
            }
        }
    }

    // Validate ETL settings
    int batch_size = get_value_or<int>("etl_settings.batch_size", 1000);
    if (batch_size <= 0) {
        throw ConfigurationException("Invalid batch_size: must be greater than 0", "batch_size");
    }

    int workers = get_value_or<int>("etl_settings.parallel_workers", 1);
    if (workers <= 0) {
        throw ConfigurationException("Invalid parallel_workers: must be greater than 0", "parallel_workers");
    }

    double error_threshold = get_value_or<double>("etl_settings.error_threshold", 0.01);
    if (error_threshold < 0.0 || error_threshold > 1.0) {
        throw ConfigurationException("Invalid error_threshold: must be between 0.0 and 1.0", "error_threshold");
    }
}

void ConfigurationManager::parse_table_mappings(const YAML::Node& mappings_node) {
    for (const auto& item : mappings_node) {
        std::string table_name = item.first.as<std::string>();
        try {
            table_mappings_[table_name] = TableMapping(item.second);
        } catch (const ConfigurationException& e) {
            throw ConfigurationException(
                std::format("Error parsing table mapping '{}': {}", table_name, e.message()));
        }
    }
}

DatabaseConfig ConfigurationManager::parse_database_config(const YAML::Node& db_node) {
    try {
        return DatabaseConfig(db_node);
    } catch (const ConfigurationException& e) {
        throw ConfigurationException(
            std::format("Error parsing database configuration: {}", e.message()));
    }
}

} // namespace omop::common