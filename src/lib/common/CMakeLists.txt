# Common library CMakeLists.txt
set(COMMON_SOURCES
    configuration.cpp
    exceptions.cpp
    logging.cpp
    utilities.cpp
    validation.cpp
)

set(COMMON_HEADERS
    configuration.h
    exceptions.h
    logging.h
    utilities.h
    validation.h
)

include_directories(${CMAKE_SOURCE_DIR}/src/lib/common
                    ${CMAKE_SOURCE_DIR}/src/lib/core
)

# Create common library
add_library(omop_common STATIC ${COMMON_SOURCES})

# Set include directories
target_include_directories(omop_common
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/..>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${UUID_INCLUDE_DIRS}
)

# Link dependencies
target_link_libraries(omop_common
    PUBLIC
        yaml-cpp
    PRIVATE
        nlohmann_json::nlohmann_json
        fmt::fmt
        spdlog::spdlog
        OpenSSL::SSL
        OpenSSL::Crypto
        Threads::Threads
        ${UUID_LIBRARIES}
)

# Set compile features and flags
target_compile_features(omop_common PUBLIC cxx_std_20)

# Install rules
install(TARGETS omop_common
    # EXPORT omop-targets
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES ${COMMON_HEADERS}
    DESTINATION include/omop/common
)