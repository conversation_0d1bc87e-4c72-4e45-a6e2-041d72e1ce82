/**
 * @file utilities.cpp
 * @brief Implementation of utility functions for OMOP ETL pipeline
 * <AUTHOR> ETL Team
 * @date 2024
 */

#include "utilities.h"
#include "core/interfaces.h"

#include <fstream>
#include <random>
#include <cctype>
#include <ctime>
#include <iomanip>
#include <openssl/md5.h>
#include <openssl/sha.h>
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/bio.h>
#include <openssl/buffer.h>
#include <uuid/uuid.h>
#include <nlohmann/json.hpp>
#include <cstdlib>
#include <unistd.h>
#include <sys/types.h>
#include <pwd.h>
#include <thread>
#include <array>
#include <memory>

#ifdef _WIN32
#include <windows.h>
#include <psapi.h>
#elif defined(__linux__)
#include <sys/resource.h>
#include <sys/sysinfo.h>
#elif defined(__APPLE__)
#include <sys/resource.h>
#include <sys/types.h>
#include <sys/sysctl.h>
#include <mach/mach.h>
#endif

namespace omop::utils {

std::string any_to_string(const std::any& value) {
    if (!value.has_value()) {
        return "";
    }

    const std::type_info& type = value.type();

    try {
        if (type == typeid(std::string)) {
            return std::any_cast<std::string>(value);
        } else if (type == typeid(int)) {
            return std::to_string(std::any_cast<int>(value));
        } else if (type == typeid(long)) {
            return std::to_string(std::any_cast<long>(value));
        } else if (type == typeid(long long)) {
            return std::to_string(std::any_cast<long long>(value));
        } else if (type == typeid(unsigned int)) {
            return std::to_string(std::any_cast<unsigned int>(value));
        } else if (type == typeid(unsigned long)) {
            return std::to_string(std::any_cast<unsigned long>(value));
        } else if (type == typeid(unsigned long long)) {
            return std::to_string(std::any_cast<unsigned long long>(value));
        } else if (type == typeid(float)) {
            return std::to_string(std::any_cast<float>(value));
        } else if (type == typeid(double)) {
            return std::to_string(std::any_cast<double>(value));
        } else if (type == typeid(bool)) {
            return std::any_cast<bool>(value) ? "true" : "false";
        } else if (type == typeid(char)) {
            return std::string(1, std::any_cast<char>(value));
        } else {
            // For unknown types, return type name
            return std::string("<") + type.name() + ">";
        }
    } catch (const std::bad_any_cast& e) {
        return std::string("<bad_cast: ") + type.name() + ">";
    }
}

// StringUtils implementation
std::string StringUtils::trim(const std::string& str) {
    size_t first = str.find_first_not_of(" \t\n\r\f\v");
    if (first == std::string::npos) {
        return "";
    }
    size_t last = str.find_last_not_of(" \t\n\r\f\v");
    return str.substr(first, (last - first + 1));
}

std::string StringUtils::to_lower(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(),
                   [](unsigned char c) { return std::tolower(c); });
    return result;
}

std::string StringUtils::to_upper(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(),
                   [](unsigned char c) { return std::toupper(c); });
    return result;
}

std::vector<std::string> StringUtils::split(const std::string& str, char delimiter) {
    std::vector<std::string> tokens;
    std::stringstream ss(str);
    std::string token;

    while (std::getline(ss, token, delimiter)) {
        if (!token.empty()) {
            tokens.push_back(token);
        }
    }

    return tokens;
}

std::vector<std::string> StringUtils::split(const std::string& str, const std::string& delimiter) {
    std::vector<std::string> tokens;
    size_t start = 0;
    size_t end = str.find(delimiter);

    while (end != std::string::npos) {
        tokens.push_back(str.substr(start, end - start));
        start = end + delimiter.length();
        end = str.find(delimiter, start);
    }

    tokens.push_back(str.substr(start));
    return tokens;
}

std::string StringUtils::join(const std::vector<std::string>& strings, const std::string& delimiter) {
    if (strings.empty()) {
        return "";
    }

    std::stringstream ss;
    for (size_t i = 0; i < strings.size(); ++i) {
        if (i > 0) {
            ss << delimiter;
        }
        ss << strings[i];
    }

    return ss.str();
}

std::string StringUtils::replace_all(const std::string& str,
                                    const std::string& from,
                                    const std::string& to) {
    if (from.empty()) {
        return str;
    }

    std::string result = str;
    size_t start_pos = 0;

    while ((start_pos = result.find(from, start_pos)) != std::string::npos) {
        result.replace(start_pos, from.length(), to);
        start_pos += to.length();
    }

    return result;
}

bool StringUtils::starts_with(const std::string& str, const std::string& prefix) {
    return str.size() >= prefix.size() &&
           str.compare(0, prefix.size(), prefix) == 0;
}

bool StringUtils::ends_with(const std::string& str, const std::string& suffix) {
    return str.size() >= suffix.size() &&
           str.compare(str.size() - suffix.size(), suffix.size(), suffix) == 0;
}

bool StringUtils::contains(const std::string& str, const std::string& substr) {
    return str.find(substr) != std::string::npos;
}

std::string StringUtils::to_snake_case(const std::string& str) {
    std::string result;
    for (size_t i = 0; i < str.length(); ++i) {
        if (i > 0 && std::isupper(str[i]) && std::islower(str[i-1])) {
            result += '_';
        }
        result += std::tolower(str[i]);
    }
    return result;
}

std::string StringUtils::to_camel_case(const std::string& str) {
    std::string result;
    bool capitalize_next = false;

    for (char c : str) {
        if (c == '_' || c == ' ' || c == '-') {
            capitalize_next = true;
        } else {
            if (capitalize_next) {
                result += std::toupper(c);
                capitalize_next = false;
            } else {
                result += std::tolower(c);
            }
        }
    }

    return result;
}

std::string StringUtils::escape_sql(const std::string& str) {
    std::string result;
    result.reserve(str.length() * 2);

    for (char c : str) {
        switch (c) {
            case '\'': result += "''"; break;
            case '"': result += "\\\""; break;
            case '\\': result += "\\\\"; break;
            case '\n': result += "\\n"; break;
            case '\r': result += "\\r"; break;
            case '\t': result += "\\t"; break;
            case '\0': result += "\\0"; break;
            default: result += c; break;
        }
    }

    return result;
}

std::string StringUtils::random_string(size_t length) {
    static const char alphanum[] =
        "0123456789"
        "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        "abcdefghijklmnopqrstuvwxyz";

    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, sizeof(alphanum) - 2);

    std::string result;
    result.reserve(length);

    for (size_t i = 0; i < length; ++i) {
        result += alphanum[dis(gen)];
    }

    return result;
}

// DateTimeUtils implementation
std::optional<DateTimeUtils::time_point> DateTimeUtils::parse_date(
    const std::string& date_str, const std::string& format) {

    std::tm tm = {};
    std::istringstream ss(date_str);
    ss >> std::get_time(&tm, format.c_str());

    if (ss.fail()) {
        return std::nullopt;
    }

    auto tp = std::chrono::system_clock::from_time_t(std::mktime(&tm));
    return tp;
}

std::string DateTimeUtils::format_date(const time_point& tp, const std::string& format) {
    auto time_t = std::chrono::system_clock::to_time_t(tp);
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), format.c_str());
    return ss.str();
}

std::string DateTimeUtils::current_timestamp(const std::string& format) {
    return format_date(std::chrono::system_clock::now(), format);
}

DateTimeUtils::time_point DateTimeUtils::convert_timezone(
    const time_point& tp, const std::string& from_tz, const std::string& to_tz) {
    // Simplified implementation - in production, use a proper timezone library
    // This is a placeholder that doesn't actually convert timezones
    return tp;
}

DateTimeUtils::time_point DateTimeUtils::add_duration(
    const time_point& tp, int days, int hours, int minutes) {

    auto duration = std::chrono::days(days) +
                   std::chrono::hours(hours) +
                   std::chrono::minutes(minutes);
    return tp + duration;
}

int DateTimeUtils::calculate_age(const time_point& birth_date, const time_point& as_of_date) {
    auto birth_time_t = std::chrono::system_clock::to_time_t(birth_date);
    auto as_of_time_t = std::chrono::system_clock::to_time_t(as_of_date);

    std::tm birth_tm = *std::localtime(&birth_time_t);
    std::tm as_of_tm = *std::localtime(&as_of_time_t);

    int age = as_of_tm.tm_year - birth_tm.tm_year;

    if (as_of_tm.tm_mon < birth_tm.tm_mon ||
        (as_of_tm.tm_mon == birth_tm.tm_mon && as_of_tm.tm_mday < birth_tm.tm_mday)) {
        age--;
    }

    return age;
}

bool DateTimeUtils::is_valid_date(int year, int month, int day) {
    if (month < 1 || month > 12) return false;
    if (day < 1) return false;

    static const int days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

    int max_day = days_in_month[month - 1];

    // Check for leap year
    if (month == 2 && ((year % 4 == 0 && year % 100 != 0) || year % 400 == 0)) {
        max_day = 29;
    }

    return day <= max_day;
}

int DateTimeUtils::get_iso_week(const time_point& tp) {
    auto time_t = std::chrono::system_clock::to_time_t(tp);
    std::tm tm = *std::localtime(&time_t);

    // Simplified ISO week calculation
    // In production, use a proper date library
    std::tm jan1 = tm;
    jan1.tm_mon = 0;
    jan1.tm_mday = 1;
    std::mktime(&jan1);

    int days_since_jan1 = tm.tm_yday;
    int jan1_weekday = jan1.tm_wday;

    // Adjust for ISO week (Monday = 1, Sunday = 7)
    if (jan1_weekday == 0) jan1_weekday = 7;

    int iso_week = (days_since_jan1 + jan1_weekday - 1) / 7 + 1;

    return iso_week;
}

// FileUtils implementation
std::optional<std::string> FileUtils::read_file(const std::string& filepath) {
    std::ifstream file(filepath, std::ios::binary);
    if (!file.is_open()) {
        return std::nullopt;
    }

    std::string content((std::istreambuf_iterator<char>(file)),
                       std::istreambuf_iterator<char>());
    return content;
}

bool FileUtils::write_file(const std::string& filepath,
                          const std::string& content,
                          bool append) {
    std::ios_base::openmode mode = std::ios::out;
    if (append) {
        mode |= std::ios::app;
    }

    std::ofstream file(filepath, mode);
    if (!file.is_open()) {
        return false;
    }

    file << content;
    return file.good();
}

bool FileUtils::file_exists(const std::string& filepath) {
    return std::filesystem::exists(filepath) &&
           std::filesystem::is_regular_file(filepath);
}

std::optional<size_t> FileUtils::file_size(const std::string& filepath) {
    try {
        return std::filesystem::file_size(filepath);
    } catch (...) {
        return std::nullopt;
    }
}

std::string FileUtils::get_extension(const std::string& filepath) {
    return std::filesystem::path(filepath).extension().string();
}

std::string FileUtils::get_basename(const std::string& filepath) {
    return std::filesystem::path(filepath).stem().string();
}

std::string FileUtils::get_directory(const std::string& filepath) {
    return std::filesystem::path(filepath).parent_path().string();
}

bool FileUtils::create_directory(const std::string& path) {
    try {
        return std::filesystem::create_directories(path);
    } catch (...) {
        return false;
    }
}

std::vector<std::string> FileUtils::list_files(const std::string& directory,
                                              const std::string& pattern,
                                              bool recursive) {
    std::vector<std::string> files;
    std::regex pattern_regex(pattern);

    try {
        if (recursive) {
            for (const auto& entry : std::filesystem::recursive_directory_iterator(directory)) {
                if (entry.is_regular_file() &&
                    std::regex_match(entry.path().filename().string(), pattern_regex)) {
                    files.push_back(entry.path().string());
                }
            }
        } else {
            for (const auto& entry : std::filesystem::directory_iterator(directory)) {
                if (entry.is_regular_file() &&
                    std::regex_match(entry.path().filename().string(), pattern_regex)) {
                    files.push_back(entry.path().string());
                }
            }
        }
    } catch (...) {
        // Return empty vector on error
    }

    return files;
}

bool FileUtils::copy_file(const std::string& source, const std::string& destination) {
    try {
        std::filesystem::copy_file(source, destination,
                                  std::filesystem::copy_options::overwrite_existing);
        return true;
    } catch (...) {
        return false;
    }
}

bool FileUtils::move_file(const std::string& source, const std::string& destination) {
    try {
        std::filesystem::rename(source, destination);
        return true;
    } catch (...) {
        return false;
    }
}

bool FileUtils::delete_file(const std::string& filepath) {
    try {
        return std::filesystem::remove(filepath);
    } catch (...) {
        return false;
    }
}

std::string FileUtils::get_temp_directory() {
    return std::filesystem::temp_directory_path().string();
}

std::string FileUtils::create_temp_file(const std::string& prefix, const std::string& extension) {
    std::string temp_dir = get_temp_directory();
    std::string filename = prefix + "_" + StringUtils::random_string(8) + extension;
    return std::filesystem::path(temp_dir) / filename;
}

// SystemUtils implementation
std::optional<std::string> SystemUtils::get_env(const std::string& name) {
    const char* value = std::getenv(name.c_str());
    if (value) {
        return std::string(value);
    }
    return std::nullopt;
}

bool SystemUtils::set_env(const std::string& name, const std::string& value) {
#ifdef _WIN32
    return _putenv_s(name.c_str(), value.c_str()) == 0;
#else
    return setenv(name.c_str(), value.c_str(), 1) == 0;
#endif
}

std::string SystemUtils::get_current_directory() {
    return std::filesystem::current_path().string();
}

std::string SystemUtils::get_home_directory() {
#ifdef _WIN32
    const char* home = std::getenv("USERPROFILE");
    if (home) {
        return std::string(home);
    }
#else
    const char* home = std::getenv("HOME");
    if (home) {
        return std::string(home);
    }

    struct passwd* pw = getpwuid(getuid());
    if (pw) {
        return std::string(pw->pw_dir);
    }
#endif
    return "";
}

unsigned int SystemUtils::get_cpu_count() {
    return std::thread::hardware_concurrency();
}

size_t SystemUtils::get_available_memory() {
#ifdef _WIN32
    MEMORYSTATUSEX status;
    status.dwLength = sizeof(status);
    if (GlobalMemoryStatusEx(&status)) {
        return status.ullAvailPhys;
    }
#elif defined(__linux__)
    struct sysinfo info;
    if (sysinfo(&info) == 0) {
        return info.freeram * info.mem_unit;
    }
#elif defined(__APPLE__)
    vm_statistics64_data_t vm_stat;
    mach_msg_type_number_t count = HOST_VM_INFO64_COUNT;
    if (host_statistics64(mach_host_self(), HOST_VM_INFO64, (host_info64_t)&vm_stat, &count) == KERN_SUCCESS) {
        return vm_stat.free_count * vm_page_size;
    }
#endif
    return 0;
}

int SystemUtils::get_process_id() {
#ifdef _WIN32
    return GetCurrentProcessId();
#else
    return getpid();
#endif
}

std::optional<std::string> SystemUtils::execute_command(const std::string& command) {
    std::array<char, 128> buffer;
    std::string result;

    std::unique_ptr<FILE, decltype(&pclose)> pipe(popen(command.c_str(), "r"), pclose);
    if (!pipe) {
        return std::nullopt;
    }

    while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr) {
        result += buffer.data();
    }

    return result;
}

std::string SystemUtils::get_hostname() {
    char hostname[256];
    if (gethostname(hostname, sizeof(hostname)) == 0) {
        return std::string(hostname);
    }
    return "unknown";
}

std::string SystemUtils::get_username() {
#ifdef _WIN32
    char username[256];
    DWORD size = sizeof(username);
    if (GetUserNameA(username, &size)) {
        return std::string(username);
    }
#else
    const char* user = std::getenv("USER");
    if (user) {
        return std::string(user);
    }

    struct passwd* pw = getpwuid(getuid());
    if (pw) {
        return std::string(pw->pw_name);
    }
#endif
    return "unknown";
}

// CryptoUtils implementation
std::string CryptoUtils::md5(const std::string& data) {
    unsigned char digest[MD5_DIGEST_LENGTH];
    MD5(reinterpret_cast<const unsigned char*>(data.c_str()), data.length(), digest);

    std::stringstream ss;
    ss << std::hex << std::setfill('0');
    for (int i = 0; i < MD5_DIGEST_LENGTH; ++i) {
        ss << std::setw(2) << static_cast<unsigned int>(digest[i]);
    }

    return ss.str();
}

std::string CryptoUtils::sha256(const std::string& data) {
    unsigned char digest[SHA256_DIGEST_LENGTH];
    SHA256(reinterpret_cast<const unsigned char*>(data.c_str()), data.length(), digest);

    std::stringstream ss;
    ss << std::hex << std::setfill('0');
    for (int i = 0; i < SHA256_DIGEST_LENGTH; ++i) {
        ss << std::setw(2) << static_cast<unsigned int>(digest[i]);
    }

    return ss.str();
}

std::string CryptoUtils::base64_encode(const std::vector<uint8_t>& data) {
    BIO* b64 = BIO_new(BIO_f_base64());
    BIO* bio = BIO_new(BIO_s_mem());
    bio = BIO_push(b64, bio);

    BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL);
    BIO_write(bio, data.data(), data.size());
    BIO_flush(bio);

    BUF_MEM* buffer_ptr;
    BIO_get_mem_ptr(bio, &buffer_ptr);

    std::string result(buffer_ptr->data, buffer_ptr->length);
    BIO_free_all(bio);

    return result;
}

std::vector<uint8_t> CryptoUtils::base64_decode(const std::string& encoded) {
    BIO* b64 = BIO_new(BIO_f_base64());
    BIO* bio = BIO_new_mem_buf(encoded.c_str(), encoded.length());
    bio = BIO_push(b64, bio);

    BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL);

    std::vector<uint8_t> result(encoded.length());
    int decoded_length = BIO_read(bio, result.data(), encoded.length());
    BIO_free_all(bio);

    result.resize(decoded_length);
    return result;
}

std::string CryptoUtils::generate_uuid() {
    uuid_t uuid;
    uuid_generate(uuid);

    char uuid_str[37];
    uuid_unparse_lower(uuid, uuid_str);

    return std::string(uuid_str);
}

std::vector<uint8_t> CryptoUtils::random_bytes(size_t length) {
    std::vector<uint8_t> result(length);
    if (RAND_bytes(result.data(), length) != 1) {
        // Fall back to C++ random if OpenSSL fails
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, 255);

        for (size_t i = 0; i < length; ++i) {
            result[i] = static_cast<uint8_t>(dis(gen));
        }
    }

    return result;
}

// ValidationUtils implementation
bool ValidationUtils::is_valid_email(const std::string& email) {
    const std::regex pattern(R"(^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$)");
    return std::regex_match(email, pattern);
}

bool ValidationUtils::is_valid_url(const std::string& url) {
    const std::regex pattern(
        R"(^(https?://)?([a-zA-Z0-9.-]+)\.([a-zA-Z]{2,})(:[0-9]+)?(/.*)?$)");
    return std::regex_match(url, pattern);
}

bool ValidationUtils::is_valid_ip(const std::string& ip) {
    // IPv4 pattern
    const std::regex ipv4_pattern(
        R"(^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$)");

    // Simplified IPv6 pattern
    const std::regex ipv6_pattern(
        R"(^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::1|::)$)");

    return std::regex_match(ip, ipv4_pattern) || std::regex_match(ip, ipv6_pattern);
}

bool ValidationUtils::is_valid_phone(const std::string& phone, const std::string& country_code) {
    if (country_code == "US") {
        const std::regex pattern(R"(^\+?1?\s*\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}$)");
        return std::regex_match(phone, pattern);
    }
    // Add more country-specific patterns as needed
    return false;
}

bool ValidationUtils::is_valid_postal_code(const std::string& postal_code,
                                          const std::string& country_code) {
    if (country_code == "US") {
        const std::regex pattern(R"(^\d{5}(-\d{4})?$)");
        return std::regex_match(postal_code, pattern);
    } else if (country_code == "CA") {
        const std::regex pattern(R"(^[A-Za-z]\d[A-Za-z]\s?\d[A-Za-z]\d$)");
        return std::regex_match(postal_code, pattern);
    }
    // Add more country-specific patterns as needed
    return false;
}

bool ValidationUtils::is_valid_date_format(const std::string& date_str,
                                          const std::string& format) {
    std::tm tm = {};
    std::istringstream ss(date_str);
    ss >> std::get_time(&tm, format.c_str());
    return !ss.fail();
}

bool ValidationUtils::is_valid_json(const std::string& json) {
    try {
        auto parsed = nlohmann::json::parse(json);
        return true;
    } catch (...) {
        return false;
    }
}

bool ValidationUtils::is_valid_sql_identifier(const std::string& identifier) {
    if (identifier.empty()) return false;

    // Check first character (letter or underscore)
    if (!std::isalpha(identifier[0]) && identifier[0] != '_') {
        return false;
    }

    // Check remaining characters (letters, digits, underscores)
    for (size_t i = 1; i < identifier.length(); ++i) {
        if (!std::isalnum(identifier[i]) && identifier[i] != '_') {
            return false;
        }
    }

    // Check against SQL reserved words (simplified list)
    static const std::vector<std::string> reserved_words = {
        "SELECT", "FROM", "WHERE", "INSERT", "UPDATE", "DELETE", "CREATE",
        "DROP", "ALTER", "TABLE", "INDEX", "VIEW", "PROCEDURE", "FUNCTION"
    };

    std::string upper_id = StringUtils::to_upper(identifier);
    return std::find(reserved_words.begin(), reserved_words.end(), upper_id) == reserved_words.end();
}

// PerformanceUtils implementation
PerformanceUtils::MemoryTracker::MemoryTracker() {
#ifdef _WIN32
    PROCESS_MEMORY_COUNTERS pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
        initial_usage_ = pmc.WorkingSetSize;
        peak_usage_ = initial_usage_;
    }
#else
    struct rusage usage;
    if (getrusage(RUSAGE_SELF, &usage) == 0) {
        initial_usage_ = usage.ru_maxrss * 1024; // Convert KB to bytes
        peak_usage_ = initial_usage_;
    }
#endif
}

size_t PerformanceUtils::MemoryTracker::current_usage() const {
#ifdef _WIN32
    PROCESS_MEMORY_COUNTERS pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
        size_t current = pmc.WorkingSetSize;
        if (current > peak_usage_) {
            peak_usage_ = current;
        }
        return current - initial_usage_;
    }
#else
    struct rusage usage;
    if (getrusage(RUSAGE_SELF, &usage) == 0) {
        size_t current = usage.ru_maxrss * 1024;
        if (current > peak_usage_) {
            peak_usage_ = current;
        }
        return current - initial_usage_;
    }
#endif
    return 0;
}

size_t PerformanceUtils::MemoryTracker::peak_usage() const {
    return peak_usage_ - initial_usage_;
}

void PerformanceUtils::MemoryTracker::reset() {
    initial_usage_ = 0;
    peak_usage_ = 0;

#ifdef _WIN32
    PROCESS_MEMORY_COUNTERS pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
        initial_usage_ = pmc.WorkingSetSize;
        peak_usage_ = initial_usage_;
    }
#else
    struct rusage usage;
    if (getrusage(RUSAGE_SELF, &usage) == 0) {
        initial_usage_ = usage.ru_maxrss * 1024;
        peak_usage_ = initial_usage_;
    }
#endif
}

std::string PerformanceUtils::format_bytes(size_t bytes) {
    const char* units[] = {"B", "KB", "MB", "GB", "TB"};
    int unit_index = 0;
    double size = static_cast<double>(bytes);

    while (size >= 1024.0 && unit_index < 4) {
        size /= 1024.0;
        unit_index++;
    }

    std::stringstream ss;
    ss << std::fixed << std::setprecision(2) << size << " " << units[unit_index];
    return ss.str();
}

std::string PerformanceUtils::format_duration(double seconds) {
    if (seconds < 1.0) {
        return std::to_string(static_cast<int>(seconds * 1000)) + "ms";
    }

    int hours = static_cast<int>(seconds / 3600);
    int minutes = static_cast<int>((seconds - hours * 3600) / 60);
    int secs = static_cast<int>(seconds - hours * 3600 - minutes * 60);

    std::stringstream ss;
    if (hours > 0) {
        ss << hours << "h ";
    }
    if (minutes > 0 || hours > 0) {
        ss << minutes << "m ";
    }
    ss << secs << "s";

    return ss.str();
}

double PerformanceUtils::calculate_throughput(size_t items, double seconds) {
    if (seconds <= 0) {
        return 0.0;
    }
    return static_cast<double>(items) / seconds;
}

std::string ProcessingUtils::stage_name(int stage) {
    switch (stage) {
        case 0: // Extract
            return "Extract";
        case 1: // Transform
            return "Transform";
        case 2: // Load
            return "Load";
        default:
            return "Unknown";
    }
}

} // namespace omop::utils