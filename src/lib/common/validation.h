/**
 * @file validation.h
 * @brief Data validation framework for OMOP ETL pipeline
 * <AUTHOR> ETL Team
 * @date 2024
 *
 * This file contains the validation framework for ensuring data quality
 * and integrity throughout the ETL process.
 */

#pragma once

#include "exceptions.h"
#include "logging.h"

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <unordered_map>
#include <variant>
#include <optional>
#include <chrono>
#include <regex>
#include <any>

namespace omop::common {

/**
 * @brief Enumeration of validation rule types
 */
enum class ValidationType {
    NOT_NULL,           ///< Field must not be null
    NOT_EMPTY,          ///< Field must not be empty
    UNIQUE,             ///< Field must be unique
    IN_LIST,            ///< Field must be in specified list
    REGEX,              ///< Field must match regex pattern
    DATE_RANGE,         ///< Date must be within range
    NUMERIC_RANGE,      ///< Number must be within range
    GREATER_THAN,       ///< Number must be greater than value
    LESS_THAN,          ///< Number must be less than value
    BETWEEN,            ///< Value must be between two values
    BEFORE,             ///< Date must be before another date
    AFTER,              ///< Date must be after another date
    LENGTH,             ///< String length validation
    CUSTOM,             ///< Custom validation function
    NOT_ZERO,           ///< Number must not be zero
    NOT_FUTURE_DATE,    ///< Date must not be in the future
    FOREIGN_KEY,        ///< Foreign key constraint
    COMPOSITE_KEY,      ///< Composite key validation
    CONDITIONAL         ///< Conditional validation based on other fields
};

/**
 * @brief Structure to hold validation results
 */
struct ValidationResult {
    bool is_valid;                          ///< Overall validation status
    std::vector<std::string> errors;        ///< List of validation errors
    std::vector<std::string> warnings;      ///< List of validation warnings
    size_t records_validated;               ///< Number of records validated
    size_t records_failed;                  ///< Number of records that failed validation

    /**
     * @brief Check if validation passed
     * @return true if validation passed, false otherwise
     */
    bool passed() const { return is_valid && errors.empty(); }

    /**
     * @brief Merge another validation result into this one
     * @param other The validation result to merge
     */
    void merge(const ValidationResult& other);
};

/**
 * @brief Base class for validation rules
 */
class ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param type Type of validation
     * @param error_message Custom error message
     */
    ValidationRule(const std::string& field_name,
                  ValidationType type,
                  const std::string& error_message = "");

    /**
     * @brief Virtual destructor
     */
    virtual ~ValidationRule() = default;

    /**
     * @brief Validate a value
     * @param value The value to validate
     * @param record The entire record (for context)
     * @return true if validation passes, false otherwise
     */
    virtual bool validate(const std::any& value,
                         const std::unordered_map<std::string, std::any>& record) const = 0;

    /**
     * @brief Get the error message for validation failure
     * @return Error message string
     */
    virtual std::string getErrorMessage() const;

    /**
     * @brief Get the field name
     * @return Field name string
     */
    const std::string& getFieldName() const { return field_name_; }

    /**
     * @brief Get the validation type
     * @return Validation type enum
     */
    ValidationType getType() const { return type_; }

protected:
    std::string field_name_;      ///< Name of the field to validate
    ValidationType type_;         ///< Type of validation
    std::string error_message_;   ///< Custom error message
};

/**
 * @brief Not null validation rule
 */
class NotNullRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     */
    explicit NotNullRule(const std::string& field_name);

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;
};

/**
 * @brief In list validation rule
 */
template<typename T>
class InListRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param allowed_values List of allowed values
     */
    InListRule(const std::string& field_name, const std::vector<T>& allowed_values);

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    std::vector<T> allowed_values_;  ///< List of allowed values
};

/**
 * @brief Date range validation rule
 */
class DateRangeRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param min_date Minimum allowed date (optional)
     * @param max_date Maximum allowed date (optional)
     */
    DateRangeRule(const std::string& field_name,
                  const std::optional<std::chrono::system_clock::time_point>& min_date,
                  const std::optional<std::chrono::system_clock::time_point>& max_date);

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    std::optional<std::chrono::system_clock::time_point> min_date_;  ///< Minimum date
    std::optional<std::chrono::system_clock::time_point> max_date_;  ///< Maximum date
};

/**
 * @brief Numeric range validation rule
 */
template<typename T>
class NumericRangeRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param min_value Minimum allowed value (optional)
     * @param max_value Maximum allowed value (optional)
     */
    NumericRangeRule(const std::string& field_name,
                     const std::optional<T>& min_value,
                     const std::optional<T>& max_value);

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    std::optional<T> min_value_;  ///< Minimum value
    std::optional<T> max_value_;  ///< Maximum value
};

/**
 * @brief Regular expression validation rule
 */
class RegexRule : public ValidationRule {
public:
    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param pattern Regular expression pattern
     */
    RegexRule(const std::string& field_name, const std::string& pattern);

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    std::regex pattern_;  ///< Regular expression pattern
};

/**
 * @brief Custom validation rule with user-defined function
 */
class CustomRule : public ValidationRule {
public:
    using ValidationFunction = std::function<bool(const std::any&, const std::unordered_map<std::string, std::any>&)>;

    /**
     * @brief Constructor
     * @param field_name Name of the field to validate
     * @param validator Custom validation function
     * @param error_message Custom error message
     */
    CustomRule(const std::string& field_name,
               ValidationFunction validator,
               const std::string& error_message = "");

    bool validate(const std::any& value,
                 const std::unordered_map<std::string, std::any>& record) const override;

private:
    ValidationFunction validator_;  ///< Custom validation function
};

/**
 * @brief Validation engine for applying validation rules
 */
class ValidationEngine {
public:
    /**
     * @brief Constructor
     * @param logger Logger instance
     */
    explicit ValidationEngine(std::shared_ptr<Logger> logger = nullptr);

    /**
     * @brief Add a validation rule
     * @param rule Validation rule to add
     */
    void addRule(std::unique_ptr<ValidationRule> rule);

    /**
     * @brief Validate a single record
     * @param record Record to validate
     * @return Validation result
     */
    ValidationResult validateRecord(const std::unordered_map<std::string, std::any>& record);

    /**
     * @brief Validate a batch of records
     * @param records Records to validate
     * @param stop_on_error Stop validation on first error
     * @return Validation result
     */
    ValidationResult validateBatch(const std::vector<std::unordered_map<std::string, std::any>>& records,
                                  bool stop_on_error = false);

    /**
     * @brief Clear all validation rules
     */
    void clearRules();

    /**
     * @brief Get the number of rules
     * @return Number of validation rules
     */
    size_t getRuleCount() const { return rules_.size(); }

    /**
     * @brief Enable or disable specific validation types
     * @param type Validation type to enable/disable
     * @param enabled true to enable, false to disable
     */
    void setValidationTypeEnabled(ValidationType type, bool enabled);

private:
    std::vector<std::unique_ptr<ValidationRule>> rules_;  ///< List of validation rules
    std::unordered_map<ValidationType, bool> enabled_types_;  ///< Enabled validation types
    std::shared_ptr<Logger> logger_;  ///< Logger instance
};

/**
 * @brief Factory class for creating validation rules from configuration
 */
class ValidationRuleFactory {
public:
    /**
     * @brief Create a validation rule from configuration
     * @param config Configuration map
     * @return Unique pointer to validation rule
     */
    static std::unique_ptr<ValidationRule> createRule(const std::unordered_map<std::string, std::any>& config);

    /**
     * @brief Create validation engine from YAML configuration
     * @param yaml_config YAML configuration string
     * @return Unique pointer to validation engine
     */
    static std::unique_ptr<ValidationEngine> createEngineFromYaml(const std::string& yaml_config);
};

/**
 * @brief Utility functions for common validations
 */
namespace ValidationUtils {
    /**
     * @brief Check if a string is a valid email
     * @param email Email string to validate
     * @return true if valid email, false otherwise
     */
    bool isValidEmail(const std::string& email);

    /**
     * @brief Check if a string is a valid phone number
     * @param phone Phone number string to validate
     * @param country_code Country code for validation
     * @return true if valid phone number, false otherwise
     */
    bool isValidPhoneNumber(const std::string& phone, const std::string& country_code = "US");

    /**
     * @brief Check if a string is a valid postal code
     * @param postal_code Postal code string to validate
     * @param country_code Country code for validation
     * @return true if valid postal code, false otherwise
     */
    bool isValidPostalCode(const std::string& postal_code, const std::string& country_code = "US");

    /**
     * @brief Check if a string is a valid UUID
     * @param uuid UUID string to validate
     * @return true if valid UUID, false otherwise
     */
    bool isValidUUID(const std::string& uuid);

    /**
     * @brief Check if a string is a valid URL
     * @param url URL string to validate
     * @return true if valid URL, false otherwise
     */
    bool isValidURL(const std::string& url);

    /**
     * @brief Sanitize a string by removing potentially harmful content
     * @param input Input string to sanitize
     * @return Sanitized string
     */
    std::string sanitizeString(const std::string& input);
}

} // namespace omop::common
