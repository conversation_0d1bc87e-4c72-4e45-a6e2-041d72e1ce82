#include "table_definitions.h"
#include "omop_tables.h"
#include <sstream>
#include <algorithm>
#include <stdexcept>
#include <iomanip>
#include <ctime>
#include <array>

namespace omop::cdm {

// TableDefinition implementation
TableDefinition::TableDefinition(const std::string& name) : name_(name) {}

void TableDefinition::add_field(const FieldDefinition& field) {
    fields_.push_back(field);
}

void TableDefinition::add_index(const IndexDefinition& index) {
    indexes_.push_back(index);
}

void TableDefinition::add_foreign_key(const ForeignKeyDefinition& fk) {
    foreign_keys_.push_back(fk);
}

std::string TableDefinition::field_type_to_sql(
    const std::string& field_type,
    DatabaseDialect dialect) const {

    // Common type mappings
    static const std::unordered_map<std::string, std::unordered_map<DatabaseDialect, std::string>> type_map = {
        {"BIGINT", {
            {DatabaseDialect::PostgreSQL, "BIGINT"},
            {DatabaseDialect::MySQL, "BIGINT"},
            {DatabaseDialect::SQLServer, "BIGINT"},
            {DatabaseDialect::SQLite, "INTEGER"},
            {DatabaseDialect::Oracle, "NUMBER(19)"}
        }},
        {"INTEGER", {
            {DatabaseDialect::PostgreSQL, "INTEGER"},
            {DatabaseDialect::MySQL, "INT"},
            {DatabaseDialect::SQLServer, "INT"},
            {DatabaseDialect::SQLite, "INTEGER"},
            {DatabaseDialect::Oracle, "NUMBER(10)"}
        }},
        {"VARCHAR", {
            {DatabaseDialect::PostgreSQL, "VARCHAR"},
            {DatabaseDialect::MySQL, "VARCHAR"},
            {DatabaseDialect::SQLServer, "VARCHAR"},
            {DatabaseDialect::SQLite, "TEXT"},
            {DatabaseDialect::Oracle, "VARCHAR2"}
        }},
        {"TEXT", {
            {DatabaseDialect::PostgreSQL, "TEXT"},
            {DatabaseDialect::MySQL, "TEXT"},
            {DatabaseDialect::SQLServer, "VARCHAR(MAX)"},
            {DatabaseDialect::SQLite, "TEXT"},
            {DatabaseDialect::Oracle, "CLOB"}
        }},
        {"DATE", {
            {DatabaseDialect::PostgreSQL, "DATE"},
            {DatabaseDialect::MySQL, "DATE"},
            {DatabaseDialect::SQLServer, "DATE"},
            {DatabaseDialect::SQLite, "TEXT"},
            {DatabaseDialect::Oracle, "DATE"}
        }},
        {"DATETIME", {
            {DatabaseDialect::PostgreSQL, "TIMESTAMP"},
            {DatabaseDialect::MySQL, "DATETIME"},
            {DatabaseDialect::SQLServer, "DATETIME2"},
            {DatabaseDialect::SQLite, "TEXT"},
            {DatabaseDialect::Oracle, "TIMESTAMP"}
        }},
        {"FLOAT", {
            {DatabaseDialect::PostgreSQL, "REAL"},
            {DatabaseDialect::MySQL, "FLOAT"},
            {DatabaseDialect::SQLServer, "FLOAT"},
            {DatabaseDialect::SQLite, "REAL"},
            {DatabaseDialect::Oracle, "FLOAT"}
        }},
        {"DECIMAL", {
            {DatabaseDialect::PostgreSQL, "DECIMAL"},
            {DatabaseDialect::MySQL, "DECIMAL"},
            {DatabaseDialect::SQLServer, "DECIMAL"},
            {DatabaseDialect::SQLite, "REAL"},
            {DatabaseDialect::Oracle, "NUMBER"}
        }}
    };

    // Extract base type and parameters
    std::string base_type = field_type;
    std::string params;
    size_t paren_pos = field_type.find('(');
    if (paren_pos != std::string::npos) {
        base_type = field_type.substr(0, paren_pos);
        params = field_type.substr(paren_pos);
    }

    auto type_it = type_map.find(base_type);
    if (type_it != type_map.end()) {
        auto dialect_it = type_it->second.find(dialect);
        if (dialect_it != type_it->second.end()) {
            return dialect_it->second + params;
        }
    }

    // Default: return as-is
    return field_type;
}

std::string TableDefinition::generate_create_table_sql(
    const std::string& schema_name,
    DatabaseDialect dialect) const {

    std::stringstream ss;
    ss << "CREATE TABLE " << SqlGenerator::format_table_name(schema_name, name_, dialect) << " (\n";

    // Add fields
    for (size_t i = 0; i < fields_.size(); ++i) {
        const auto& field = fields_[i];
        ss << "    " << SqlGenerator::quote_identifier(field.name, dialect) << " "
           << field_type_to_sql(field.data_type, dialect);

        if (!field.default_value.empty()) {
            ss << " DEFAULT " << field.default_value;
        }

        if (!field.is_nullable) {
            ss << " NOT NULL";
        }

        if (i < fields_.size() - 1) {
            ss << ",";
        }

        if (!field.comment.empty()) {
            ss << " -- " << field.comment;
        }

        ss << "\n";
    }

    // Add primary key constraint
    std::vector<std::string> pk_fields;
    for (const auto& field : fields_) {
        if (field.is_primary_key) {
            pk_fields.push_back(field.name);
        }
    }

    if (!pk_fields.empty()) {
        ss << ",\n    CONSTRAINT " << SqlGenerator::quote_identifier("pk_" + name_, dialect)
           << " PRIMARY KEY (";
        for (size_t i = 0; i < pk_fields.size(); ++i) {
            if (i > 0) ss << ", ";
            ss << SqlGenerator::quote_identifier(pk_fields[i], dialect);
        }
        ss << ")\n";
    }

    ss << ");";
    return ss.str();
}

std::vector<std::string> TableDefinition::generate_create_index_sql(
    const std::string& schema_name,
    DatabaseDialect dialect) const {

    std::vector<std::string> sql_statements;

    for (const auto& index : indexes_) {
        std::stringstream ss;
        ss << "CREATE ";

        if (index.is_unique) {
            ss << "UNIQUE ";
        }

        if (index.is_clustered && dialect == DatabaseDialect::SQLServer) {
            ss << "CLUSTERED ";
        }

        ss << "INDEX " << SqlGenerator::quote_identifier(index.name, dialect)
           << " ON " << SqlGenerator::format_table_name(schema_name, name_, dialect)
           << " (";

        for (size_t i = 0; i < index.columns.size(); ++i) {
            if (i > 0) ss << ", ";
            ss << SqlGenerator::quote_identifier(index.columns[i], dialect);
        }

        ss << ");";
        sql_statements.push_back(ss.str());
    }

    return sql_statements;
}

std::vector<std::string> TableDefinition::generate_foreign_key_sql(
    const std::string& schema_name,
    DatabaseDialect dialect) const {

    std::vector<std::string> sql_statements;

    for (const auto& fk : foreign_keys_) {
        std::stringstream ss;
        ss << "ALTER TABLE " << SqlGenerator::format_table_name(schema_name, name_, dialect)
           << " ADD CONSTRAINT " << SqlGenerator::quote_identifier(fk.name, dialect)
           << " FOREIGN KEY (" << SqlGenerator::quote_identifier(fk.column_name, dialect) << ")"
           << " REFERENCES " << SqlGenerator::format_table_name(schema_name, fk.referenced_table, dialect)
           << " (" << SqlGenerator::quote_identifier(fk.referenced_column, dialect) << ");";

        sql_statements.push_back(ss.str());
    }

    return sql_statements;
}

// SchemaDefinitions implementation
SchemaDefinitions& SchemaDefinitions::instance() {
    static SchemaDefinitions instance;
    return instance;
}

SchemaDefinitions::SchemaDefinitions() {
    initialize_tables();
}

void SchemaDefinitions::initialize_tables() {
    std::lock_guard<std::mutex> lock(mutex_);

    // Initialize concept table first (referenced by many other tables)
    initialize_concept_table();

    // Initialize tables in dependency order
    initialize_location_table();
    initialize_care_site_table();
    initialize_provider_table();
    initialize_person_table();
    initialize_observation_period_table();
    initialize_visit_occurrence_table();
    initialize_visit_detail_table();
    initialize_condition_occurrence_table();
    initialize_drug_exposure_table();
    initialize_procedure_occurrence_table();
    initialize_measurement_table();
    initialize_observation_table();
    initialize_death_table();
    initialize_note_table();
}

void SchemaDefinitions::initialize_concept_table() {
    auto table = std::make_unique<TableDefinition>("concept");

    table->add_field({"concept_id", "INTEGER", false, true, "", "Unique identifier for each concept"});
    table->add_field({"concept_name", "VARCHAR(255)", false, false, "", "Concept name"});
    table->add_field({"domain_id", "VARCHAR(20)", false, false, "", "Domain identifier"});
    table->add_field({"vocabulary_id", "VARCHAR(20)", false, false, "", "Vocabulary identifier"});
    table->add_field({"concept_class_id", "VARCHAR(20)", false, false, "", "Concept class identifier"});
    table->add_field({"standard_concept", "VARCHAR(1)", true, false, "", "Standard concept flag"});
    table->add_field({"concept_code", "VARCHAR(50)", false, false, "", "Concept code"});
    table->add_field({"valid_start_date", "DATE", false, false, "", "Valid start date"});
    table->add_field({"valid_end_date", "DATE", false, false, "", "Valid end date"});
    table->add_field({"invalid_reason", "VARCHAR(1)", true, false, "", "Invalid reason"});

    // Indexes
    table->add_index({"idx_concept_code", "concept", {"concept_code"}, false, false});
    table->add_index({"idx_concept_vocabulary", "concept", {"vocabulary_id"}, false, false});
    table->add_index({"idx_concept_domain", "concept", {"domain_id"}, false, false});

    tables_["concept"] = std::move(table);
}

void SchemaDefinitions::initialize_person_table() {
    auto table = std::make_unique<TableDefinition>("person");

    table->add_field({"person_id", "BIGINT", false, true, "", "Unique identifier for each person"});
    table->add_field({"gender_concept_id", "INTEGER", false, false, "", "Foreign key to gender concept"});
    table->add_field({"year_of_birth", "INTEGER", false, false, "", "Year of birth"});
    table->add_field({"month_of_birth", "INTEGER", true, false, "", "Month of birth"});
    table->add_field({"day_of_birth", "INTEGER", true, false, "", "Day of birth"});
    table->add_field({"birth_datetime", "DATETIME", true, false, "", "Exact birth datetime"});
    table->add_field({"race_concept_id", "INTEGER", false, false, "", "Foreign key to race concept"});
    table->add_field({"ethnicity_concept_id", "INTEGER", false, false, "", "Foreign key to ethnicity concept"});
    table->add_field({"location_id", "INTEGER", true, false, "", "Foreign key to location"});
    table->add_field({"provider_id", "INTEGER", true, false, "", "Foreign key to provider"});
    table->add_field({"care_site_id", "INTEGER", true, false, "", "Foreign key to care site"});
    table->add_field({"person_source_value", "VARCHAR(50)", true, false, "", "Source identifier"});
    table->add_field({"gender_source_value", "VARCHAR(50)", true, false, "", "Source gender value"});
    table->add_field({"gender_source_concept_id", "INTEGER", true, false, "", "Source gender concept"});
    table->add_field({"race_source_value", "VARCHAR(50)", true, false, "", "Source race value"});
    table->add_field({"race_source_concept_id", "INTEGER", true, false, "", "Source race concept"});
    table->add_field({"ethnicity_source_value", "VARCHAR(50)", true, false, "", "Source ethnicity value"});
    table->add_field({"ethnicity_source_concept_id", "INTEGER", true, false, "", "Source ethnicity concept"});

    // Indexes
    table->add_index({"idx_person_gender", "person", {"gender_concept_id"}, false, false});
    table->add_index({"idx_person_location", "person", {"location_id"}, false, false});
    table->add_index({"idx_person_provider", "person", {"provider_id"}, false, false});
    table->add_index({"idx_person_care_site", "person", {"care_site_id"}, false, false});

    // Foreign keys
    table->add_foreign_key({"fk_person_gender", "person", "gender_concept_id", "concept", "concept_id"});
    table->add_foreign_key({"fk_person_race", "person", "race_concept_id", "concept", "concept_id"});
    table->add_foreign_key({"fk_person_ethnicity", "person", "ethnicity_concept_id", "concept", "concept_id"});
    table->add_foreign_key({"fk_person_location", "person", "location_id", "location", "location_id"});
    table->add_foreign_key({"fk_person_provider", "person", "provider_id", "provider", "provider_id"});
    table->add_foreign_key({"fk_person_care_site", "person", "care_site_id", "care_site", "care_site_id"});

    tables_["person"] = std::move(table);
}

void SchemaDefinitions::initialize_observation_period_table() {
    auto table = std::make_unique<TableDefinition>("observation_period");

    table->add_field({"observation_period_id", "BIGINT", false, true, "", "Unique identifier"});
    table->add_field({"person_id", "BIGINT", false, false, "", "Foreign key to person"});
    table->add_field({"observation_period_start_date", "DATE", false, false, "", "Start date"});
    table->add_field({"observation_period_end_date", "DATE", false, false, "", "End date"});
    table->add_field({"period_type_concept_id", "INTEGER", false, false, "", "Type of period"});

    // Indexes
    table->add_index({"idx_observation_period_person", "observation_period", {"person_id"}, false, false});

    // Foreign keys
    table->add_foreign_key({"fk_observation_period_person", "observation_period", "person_id", "person", "person_id"});
    table->add_foreign_key({"fk_observation_period_type", "observation_period", "period_type_concept_id", "concept", "concept_id"});

    tables_["observation_period"] = std::move(table);
}

void SchemaDefinitions::initialize_visit_occurrence_table() {
    auto table = std::make_unique<TableDefinition>("visit_occurrence");

    table->add_field({"visit_occurrence_id", "BIGINT", false, true, "", "Unique identifier"});
    table->add_field({"person_id", "BIGINT", false, false, "", "Foreign key to person"});
    table->add_field({"visit_concept_id", "INTEGER", false, false, "", "Visit type concept"});
    table->add_field({"visit_start_date", "DATE", false, false, "", "Visit start date"});
    table->add_field({"visit_start_datetime", "DATETIME", true, false, "", "Visit start datetime"});
    table->add_field({"visit_end_date", "DATE", false, false, "", "Visit end date"});
    table->add_field({"visit_end_datetime", "DATETIME", true, false, "", "Visit end datetime"});
    table->add_field({"visit_type_concept_id", "INTEGER", false, false, "", "Visit type"});
    table->add_field({"provider_id", "INTEGER", true, false, "", "Provider"});
    table->add_field({"care_site_id", "INTEGER", true, false, "", "Care site"});
    table->add_field({"visit_source_value", "VARCHAR(50)", true, false, "", "Source value"});
    table->add_field({"visit_source_concept_id", "INTEGER", true, false, "", "Source concept"});
    table->add_field({"admitted_from_concept_id", "INTEGER", true, false, "", "Admission source"});
    table->add_field({"admitted_from_source_value", "VARCHAR(50)", true, false, "", "Admission source value"});
    table->add_field({"discharged_to_concept_id", "INTEGER", true, false, "", "Discharge destination"});
    table->add_field({"discharged_to_source_value", "VARCHAR(50)", true, false, "", "Discharge destination value"});
    table->add_field({"preceding_visit_occurrence_id", "BIGINT", true, false, "", "Previous visit"});

    // Indexes
    table->add_index({"idx_visit_person", "visit_occurrence", {"person_id"}, false, false});
    table->add_index({"idx_visit_concept", "visit_occurrence", {"visit_concept_id"}, false, false});

    // Foreign keys
    table->add_foreign_key({"fk_visit_person", "visit_occurrence", "person_id", "person", "person_id"});
    table->add_foreign_key({"fk_visit_concept", "visit_occurrence", "visit_concept_id", "concept", "concept_id"});
    table->add_foreign_key({"fk_visit_type", "visit_occurrence", "visit_type_concept_id", "concept", "concept_id"});
    table->add_foreign_key({"fk_visit_provider", "visit_occurrence", "provider_id", "provider", "provider_id"});
    table->add_foreign_key({"fk_visit_care_site", "visit_occurrence", "care_site_id", "care_site", "care_site_id"});

    tables_["visit_occurrence"] = std::move(table);
}

void SchemaDefinitions::initialize_condition_occurrence_table() {
    auto table = std::make_unique<TableDefinition>("condition_occurrence");

    table->add_field({"condition_occurrence_id", "BIGINT", false, true, "", "Unique identifier"});
    table->add_field({"person_id", "BIGINT", false, false, "", "Foreign key to person"});
    table->add_field({"condition_concept_id", "INTEGER", false, false, "", "Condition concept"});
    table->add_field({"condition_start_date", "DATE", false, false, "", "Condition start date"});
    table->add_field({"condition_start_datetime", "DATETIME", true, false, "", "Condition start datetime"});
    table->add_field({"condition_end_date", "DATE", true, false, "", "Condition end date"});
    table->add_field({"condition_end_datetime", "DATETIME", true, false, "", "Condition end datetime"});
    table->add_field({"condition_type_concept_id", "INTEGER", false, false, "", "Condition type"});
    table->add_field({"condition_status_concept_id", "INTEGER", true, false, "", "Condition status"});
    table->add_field({"stop_reason", "VARCHAR(20)", true, false, "", "Stop reason"});
    table->add_field({"provider_id", "INTEGER", true, false, "", "Provider"});
    table->add_field({"visit_occurrence_id", "BIGINT", true, false, "", "Visit occurrence"});
    table->add_field({"visit_detail_id", "BIGINT", true, false, "", "Visit detail"});
    table->add_field({"condition_source_value", "VARCHAR(50)", true, false, "", "Source value"});
    table->add_field({"condition_source_concept_id", "INTEGER", true, false, "", "Source concept"});
    table->add_field({"condition_status_source_value", "VARCHAR(50)", true, false, "", "Status source value"});

    // Indexes
    table->add_index({"idx_condition_person", "condition_occurrence", {"person_id"}, false, false});
    table->add_index({"idx_condition_concept", "condition_occurrence", {"condition_concept_id"}, false, false});
    table->add_index({"idx_condition_visit", "condition_occurrence", {"visit_occurrence_id"}, false, false});

    // Foreign keys
    table->add_foreign_key({"fk_condition_person", "condition_occurrence", "person_id", "person", "person_id"});
    table->add_foreign_key({"fk_condition_concept", "condition_occurrence", "condition_concept_id", "concept", "concept_id"});
    table->add_foreign_key({"fk_condition_type", "condition_occurrence", "condition_type_concept_id", "concept", "concept_id"});
    table->add_foreign_key({"fk_condition_visit", "condition_occurrence", "visit_occurrence_id", "visit_occurrence", "visit_occurrence_id"});

    tables_["condition_occurrence"] = std::move(table);
}

void SchemaDefinitions::initialize_drug_exposure_table() {
    auto table = std::make_unique<TableDefinition>("drug_exposure");

    table->add_field({"drug_exposure_id", "BIGINT", false, true, "", "Unique identifier"});
    table->add_field({"person_id", "BIGINT", false, false, "", "Foreign key to person"});
    table->add_field({"drug_concept_id", "INTEGER", false, false, "", "Drug concept"});
    table->add_field({"drug_exposure_start_date", "DATE", false, false, "", "Drug start date"});
    table->add_field({"drug_exposure_start_datetime", "DATETIME", true, false, "", "Drug start datetime"});
    table->add_field({"drug_exposure_end_date", "DATE", false, false, "", "Drug end date"});
    table->add_field({"drug_exposure_end_datetime", "DATETIME", true, false, "", "Drug end datetime"});
    table->add_field({"verbatim_end_date", "DATE", true, false, "", "Verbatim end date"});
    table->add_field({"drug_type_concept_id", "INTEGER", false, false, "", "Drug type"});
    table->add_field({"stop_reason", "VARCHAR(20)", true, false, "", "Stop reason"});
    table->add_field({"refills", "INTEGER", true, false, "", "Number of refills"});
    table->add_field({"quantity", "FLOAT", true, false, "", "Quantity"});
    table->add_field({"days_supply", "INTEGER", true, false, "", "Days supply"});
    table->add_field({"sig", "TEXT", true, false, "", "Sig"});
    table->add_field({"route_concept_id", "INTEGER", true, false, "", "Route concept"});
    table->add_field({"lot_number", "VARCHAR(50)", true, false, "", "Lot number"});
    table->add_field({"provider_id", "INTEGER", true, false, "", "Provider"});
    table->add_field({"visit_occurrence_id", "BIGINT", true, false, "", "Visit occurrence"});
    table->add_field({"visit_detail_id", "BIGINT", true, false, "", "Visit detail"});
    table->add_field({"drug_source_value", "VARCHAR(50)", true, false, "", "Source value"});
    table->add_field({"drug_source_concept_id", "INTEGER", true, false, "", "Source concept"});
    table->add_field({"route_source_value", "VARCHAR(50)", true, false, "", "Route source value"});
    table->add_field({"dose_unit_source_value", "VARCHAR(50)", true, false, "", "Dose unit source value"});

    // Indexes
    table->add_index({"idx_drug_person", "drug_exposure", {"person_id"}, false, false});
    table->add_index({"idx_drug_concept", "drug_exposure", {"drug_concept_id"}, false, false});
    table->add_index({"idx_drug_visit", "drug_exposure", {"visit_occurrence_id"}, false, false});

    // Foreign keys
    table->add_foreign_key({"fk_drug_person", "drug_exposure", "person_id", "person", "person_id"});
    table->add_foreign_key({"fk_drug_concept", "drug_exposure", "drug_concept_id", "concept", "concept_id"});
    table->add_foreign_key({"fk_drug_type", "drug_exposure", "drug_type_concept_id", "concept", "concept_id"});
    table->add_foreign_key({"fk_drug_visit", "drug_exposure", "visit_occurrence_id", "visit_occurrence", "visit_occurrence_id"});

    tables_["drug_exposure"] = std::move(table);
}

void SchemaDefinitions::initialize_procedure_occurrence_table() {
    auto table = std::make_unique<TableDefinition>("procedure_occurrence");

    table->add_field({"procedure_occurrence_id", "BIGINT", false, true, "", "Unique identifier"});
    table->add_field({"person_id", "BIGINT", false, false, "", "Foreign key to person"});
    table->add_field({"procedure_concept_id", "INTEGER", false, false, "", "Procedure concept"});
    table->add_field({"procedure_date", "DATE", false, false, "", "Procedure date"});
    table->add_field({"procedure_datetime", "DATETIME", true, false, "", "Procedure datetime"});
    table->add_field({"procedure_end_date", "DATE", true, false, "", "Procedure end date"});
    table->add_field({"procedure_end_datetime", "DATETIME", true, false, "", "Procedure end datetime"});
    table->add_field({"procedure_type_concept_id", "INTEGER", false, false, "", "Procedure type"});
    table->add_field({"modifier_concept_id", "INTEGER", true, false, "", "Modifier concept"});
    table->add_field({"quantity", "INTEGER", true, false, "", "Quantity"});
    table->add_field({"provider_id", "INTEGER", true, false, "", "Provider"});
    table->add_field({"visit_occurrence_id", "BIGINT", true, false, "", "Visit occurrence"});
    table->add_field({"visit_detail_id", "BIGINT", true, false, "", "Visit detail"});
    table->add_field({"procedure_source_value", "VARCHAR(50)", true, false, "", "Source value"});
    table->add_field({"procedure_source_concept_id", "INTEGER", true, false, "", "Source concept"});
    table->add_field({"modifier_source_value", "VARCHAR(50)", true, false, "", "Modifier source value"});

    // Indexes
    table->add_index({"idx_procedure_person", "procedure_occurrence", {"person_id"}, false, false});
    table->add_index({"idx_procedure_concept", "procedure_occurrence", {"procedure_concept_id"}, false, false});
    table->add_index({"idx_procedure_visit", "procedure_occurrence", {"visit_occurrence_id"}, false, false});

    // Foreign keys
    table->add_foreign_key({"fk_procedure_person", "procedure_occurrence", "person_id", "person", "person_id"});
    table->add_foreign_key({"fk_procedure_concept", "procedure_occurrence", "procedure_concept_id", "concept", "concept_id"});
    table->add_foreign_key({"fk_procedure_type", "procedure_occurrence", "procedure_type_concept_id", "concept", "concept_id"});
    table->add_foreign_key({"fk_procedure_visit", "procedure_occurrence", "visit_occurrence_id", "visit_occurrence", "visit_occurrence_id"});

    tables_["procedure_occurrence"] = std::move(table);
}

void SchemaDefinitions::initialize_measurement_table() {
    auto table = std::make_unique<TableDefinition>("measurement");

    table->add_field({"measurement_id", "BIGINT", false, true, "", "Unique identifier"});
    table->add_field({"person_id", "BIGINT", false, false, "", "Foreign key to person"});
    table->add_field({"measurement_concept_id", "INTEGER", false, false, "", "Measurement concept"});
    table->add_field({"measurement_date", "DATE", false, false, "", "Measurement date"});
    table->add_field({"measurement_datetime", "DATETIME", true, false, "", "Measurement datetime"});
    table->add_field({"measurement_time", "VARCHAR(10)", true, false, "", "Measurement time"});
    table->add_field({"measurement_type_concept_id", "INTEGER", false, false, "", "Measurement type"});
    table->add_field({"operator_concept_id", "INTEGER", true, false, "", "Operator concept"});
    table->add_field({"value_as_number", "FLOAT", true, false, "", "Numeric value"});
    table->add_field({"value_as_concept_id", "INTEGER", true, false, "", "Value as concept"});
    table->add_field({"unit_concept_id", "INTEGER", true, false, "", "Unit concept"});
    table->add_field({"range_low", "FLOAT", true, false, "", "Range low"});
    table->add_field({"range_high", "FLOAT", true, false, "", "Range high"});
    table->add_field({"provider_id", "INTEGER", true, false, "", "Provider"});
    table->add_field({"visit_occurrence_id", "BIGINT", true, false, "", "Visit occurrence"});
    table->add_field({"visit_detail_id", "BIGINT", true, false, "", "Visit detail"});
    table->add_field({"measurement_source_value", "VARCHAR(50)", true, false, "", "Source value"});
    table->add_field({"measurement_source_concept_id", "INTEGER", true, false, "", "Source concept"});
    table->add_field({"unit_source_value", "VARCHAR(50)", true, false, "", "Unit source value"});
    table->add_field({"unit_source_concept_id", "INTEGER", true, false, "", "Unit source concept"});
    table->add_field({"value_source_value", "VARCHAR(50)", true, false, "", "Value source value"});
    table->add_field({"measurement_event_id", "BIGINT", true, false, "", "Measurement event"});
    table->add_field({"meas_event_field_concept_id", "INTEGER", true, false, "", "Measurement event field"});

    // Indexes
    table->add_index({"idx_measurement_person", "measurement", {"person_id"}, false, false});
    table->add_index({"idx_measurement_concept", "measurement", {"measurement_concept_id"}, false, false});
    table->add_index({"idx_measurement_visit", "measurement", {"visit_occurrence_id"}, false, false});

    // Foreign keys
    table->add_foreign_key({"fk_measurement_person", "measurement", "person_id", "person", "person_id"});
    table->add_foreign_key({"fk_measurement_concept", "measurement", "measurement_concept_id", "concept", "concept_id"});
    table->add_foreign_key({"fk_measurement_type", "measurement", "measurement_type_concept_id", "concept", "concept_id"});
    table->add_foreign_key({"fk_measurement_visit", "measurement", "visit_occurrence_id", "visit_occurrence", "visit_occurrence_id"});

    tables_["measurement"] = std::move(table);
}

void SchemaDefinitions::initialize_observation_table() {
    auto table = std::make_unique<TableDefinition>("observation");

    table->add_field({"observation_id", "BIGINT", false, true, "", "Unique identifier"});
    table->add_field({"person_id", "BIGINT", false, false, "", "Foreign key to person"});
    table->add_field({"observation_concept_id", "INTEGER", false, false, "", "Observation concept"});
    table->add_field({"observation_date", "DATE", false, false, "", "Observation date"});
    table->add_field({"observation_datetime", "DATETIME", true, false, "", "Observation datetime"});
    table->add_field({"observation_type_concept_id", "INTEGER", false, false, "", "Observation type"});
    table->add_field({"value_as_number", "FLOAT", true, false, "", "Numeric value"});
    table->add_field({"value_as_string", "VARCHAR(60)", true, false, "", "String value"});
    table->add_field({"value_as_concept_id", "INTEGER", true, false, "", "Value as concept"});
    table->add_field({"qualifier_concept_id", "INTEGER", true, false, "", "Qualifier concept"});
    table->add_field({"unit_concept_id", "INTEGER", true, false, "", "Unit concept"});
    table->add_field({"provider_id", "INTEGER", true, false, "", "Provider"});
    table->add_field({"visit_occurrence_id", "BIGINT", true, false, "", "Visit occurrence"});
    table->add_field({"visit_detail_id", "BIGINT", true, false, "", "Visit detail"});
    table->add_field({"observation_source_value", "VARCHAR(50)", true, false, "", "Source value"});
    table->add_field({"observation_source_concept_id", "INTEGER", true, false, "", "Source concept"});
    table->add_field({"unit_source_value", "VARCHAR(50)", true, false, "", "Unit source value"});
    table->add_field({"qualifier_source_value", "VARCHAR(50)", true, false, "", "Qualifier source value"});
    table->add_field({"value_source_value", "VARCHAR(50)", true, false, "", "Value source value"});
    table->add_field({"observation_event_id", "BIGINT", true, false, "", "Observation event"});
    table->add_field({"obs_event_field_concept_id", "INTEGER", true, false, "", "Observation event field"});

    // Indexes
    table->add_index({"idx_observation_person", "observation", {"person_id"}, false, false});
    table->add_index({"idx_observation_concept", "observation", {"observation_concept_id"}, false, false});
    table->add_index({"idx_observation_visit", "observation", {"visit_occurrence_id"}, false, false});

    // Foreign keys
    table->add_foreign_key({"fk_observation_person", "observation", "person_id", "person", "person_id"});
    table->add_foreign_key({"fk_observation_concept", "observation", "observation_concept_id", "concept", "concept_id"});
    table->add_foreign_key({"fk_observation_type", "observation", "observation_type_concept_id", "concept", "concept_id"});
    table->add_foreign_key({"fk_observation_visit", "observation", "visit_occurrence_id", "visit_occurrence", "visit_occurrence_id"});

    tables_["observation"] = std::move(table);
}

void SchemaDefinitions::initialize_death_table() {
    auto table = std::make_unique<TableDefinition>("death");

    table->add_field({"person_id", "BIGINT", false, true, "", "Foreign key to person"});
    table->add_field({"death_date", "DATE", false, false, "", "Death date"});
    table->add_field({"death_datetime", "DATETIME", true, false, "", "Death datetime"});
    table->add_field({"death_type_concept_id", "INTEGER", true, false, "", "Death type"});
    table->add_field({"cause_concept_id", "INTEGER", true, false, "", "Cause concept"});
    table->add_field({"cause_source_value", "VARCHAR(50)", true, false, "", "Cause source value"});
    table->add_field({"cause_source_concept_id", "INTEGER", true, false, "", "Cause source concept"});

    // Indexes
    table->add_index({"idx_death_person", "death", {"person_id"}, false, false});

    // Foreign keys
    table->add_foreign_key({"fk_death_person", "death", "person_id", "person", "person_id"});
    table->add_foreign_key({"fk_death_type", "death", "death_type_concept_id", "concept", "concept_id"});
    table->add_foreign_key({"fk_death_cause", "death", "cause_concept_id", "concept", "concept_id"});

    tables_["death"] = std::move(table);
}

void SchemaDefinitions::initialize_note_table() {
    auto table = std::make_unique<TableDefinition>("note");

    table->add_field({"note_id", "BIGINT", false, true, "", "Unique identifier"});
    table->add_field({"person_id", "BIGINT", false, false, "", "Foreign key to person"});
    table->add_field({"note_date", "DATE", false, false, "", "Note date"});
    table->add_field({"note_datetime", "DATETIME", true, false, "", "Note datetime"});
    table->add_field({"note_type_concept_id", "INTEGER", false, false, "", "Note type"});
    table->add_field({"note_class_concept_id", "INTEGER", false, false, "", "Note class"});
    table->add_field({"note_title", "VARCHAR(250)", true, false, "", "Note title"});
    table->add_field({"note_text", "TEXT", false, false, "", "Note text"});
    table->add_field({"encoding_concept_id", "INTEGER", false, false, "", "Encoding concept"});
    table->add_field({"language_concept_id", "INTEGER", false, false, "", "Language concept"});
    table->add_field({"provider_id", "INTEGER", true, false, "", "Provider"});
    table->add_field({"visit_occurrence_id", "BIGINT", true, false, "", "Visit occurrence"});
    table->add_field({"visit_detail_id", "BIGINT", true, false, "", "Visit detail"});
    table->add_field({"note_source_value", "VARCHAR(50)", true, false, "", "Source value"});
    table->add_field({"note_event_id", "BIGINT", true, false, "", "Note event"});
    table->add_field({"note_event_field_concept_id", "INTEGER", true, false, "", "Note event field"});

    // Indexes
    table->add_index({"idx_note_person", "note", {"person_id"}, false, false});
    table->add_index({"idx_note_visit", "note", {"visit_occurrence_id"}, false, false});

    // Foreign keys
    table->add_foreign_key({"fk_note_person", "note", "person_id", "person", "person_id"});
    table->add_foreign_key({"fk_note_type", "note", "note_type_concept_id", "concept", "concept_id"});
    table->add_foreign_key({"fk_note_class", "note", "note_class_concept_id", "concept", "concept_id"});
    table->add_foreign_key({"fk_note_visit", "note", "visit_occurrence_id", "visit_occurrence", "visit_occurrence_id"});

    tables_["note"] = std::move(table);
}

void SchemaDefinitions::initialize_location_table() {
    auto table = std::make_unique<TableDefinition>("location");

    table->add_field({"location_id", "INTEGER", false, true, "", "Unique identifier"});
    table->add_field({"address_1", "VARCHAR(50)", true, false, "", "Address line 1"});
    table->add_field({"address_2", "VARCHAR(50)", true, false, "", "Address line 2"});
    table->add_field({"city", "VARCHAR(50)", true, false, "", "City"});
    table->add_field({"state", "VARCHAR(2)", true, false, "", "State"});
    table->add_field({"zip", "VARCHAR(9)", true, false, "", "Zip code"});
    table->add_field({"county", "VARCHAR(20)", true, false, "", "County"});
    table->add_field({"country", "VARCHAR(100)", true, false, "", "Country"});
    table->add_field({"location_source_value", "VARCHAR(50)", true, false, "", "Source value"});
    table->add_field({"latitude", "FLOAT", true, false, "", "Latitude"});
    table->add_field({"longitude", "FLOAT", true, false, "", "Longitude"});

    tables_["location"] = std::move(table);
}

void SchemaDefinitions::initialize_care_site_table() {
    auto table = std::make_unique<TableDefinition>("care_site");

    table->add_field({"care_site_id", "INTEGER", false, true, "", "Unique identifier"});
    table->add_field({"care_site_name", "VARCHAR(255)", true, false, "", "Care site name"});
    table->add_field({"place_of_service_concept_id", "INTEGER", true, false, "", "Place of service"});
    table->add_field({"location_id", "INTEGER", true, false, "", "Location"});
    table->add_field({"care_site_source_value", "VARCHAR(50)", true, false, "", "Source value"});
    table->add_field({"place_of_service_source_value", "VARCHAR(50)", true, false, "", "Place of service source"});

    // Indexes
    table->add_index({"idx_care_site_location", "care_site", {"location_id"}, false, false});

    // Foreign keys
    table->add_foreign_key({"fk_care_site_location", "care_site", "location_id", "location", "location_id"});
    table->add_foreign_key({"fk_care_site_place", "care_site", "place_of_service_concept_id", "concept", "concept_id"});

    tables_["care_site"] = std::move(table);
}

void SchemaDefinitions::initialize_provider_table() {
    auto table = std::make_unique<TableDefinition>("provider");

    table->add_field({"provider_id", "INTEGER", false, true, "", "Unique identifier"});
    table->add_field({"provider_name", "VARCHAR(255)", true, false, "", "Provider name"});
    table->add_field({"npi", "VARCHAR(20)", true, false, "", "NPI"});
    table->add_field({"dea", "VARCHAR(20)", true, false, "", "DEA"});
    table->add_field({"specialty_concept_id", "INTEGER", true, false, "", "Specialty concept"});
    table->add_field({"care_site_id", "INTEGER", true, false, "", "Care site"});
    table->add_field({"year_of_birth", "INTEGER", true, false, "", "Year of birth"});
    table->add_field({"gender_concept_id", "INTEGER", true, false, "", "Gender concept"});
    table->add_field({"provider_source_value", "VARCHAR(50)", true, false, "", "Source value"});
    table->add_field({"specialty_source_value", "VARCHAR(50)", true, false, "", "Specialty source value"});
    table->add_field({"specialty_source_concept_id", "INTEGER", true, false, "", "Specialty source concept"});
    table->add_field({"gender_source_value", "VARCHAR(50)", true, false, "", "Gender source value"});
    table->add_field({"gender_source_concept_id", "INTEGER", true, false, "", "Gender source concept"});

    // Indexes
    table->add_index({"idx_provider_care_site", "provider", {"care_site_id"}, false, false});

    // Foreign keys
    table->add_foreign_key({"fk_provider_specialty", "provider", "specialty_concept_id", "concept", "concept_id"});
    table->add_foreign_key({"fk_provider_care_site", "provider", "care_site_id", "care_site", "care_site_id"});
    table->add_foreign_key({"fk_provider_gender", "provider", "gender_concept_id", "concept", "concept_id"});

    tables_["provider"] = std::move(table);
}

void SchemaDefinitions::initialize_visit_detail_table() {
    auto table = std::make_unique<TableDefinition>("visit_detail");

    table->add_field({"visit_detail_id", "BIGINT", false, true, "", "Unique identifier"});
    table->add_field({"person_id", "BIGINT", false, false, "", "Foreign key to person"});
    table->add_field({"visit_detail_concept_id", "INTEGER", false, false, "", "Visit detail concept"});
    table->add_field({"visit_detail_start_date", "DATE", false, false, "", "Visit detail start date"});
    table->add_field({"visit_detail_start_datetime", "DATETIME", true, false, "", "Visit detail start datetime"});
    table->add_field({"visit_detail_end_date", "DATE", false, false, "", "Visit detail end date"});
    table->add_field({"visit_detail_end_datetime", "DATETIME", true, false, "", "Visit detail end datetime"});
    table->add_field({"visit_detail_type_concept_id", "INTEGER", false, false, "", "Visit detail type"});
    table->add_field({"provider_id", "INTEGER", true, false, "", "Provider"});
    table->add_field({"care_site_id", "INTEGER", true, false, "", "Care site"});
    table->add_field({"visit_detail_source_value", "VARCHAR(50)", true, false, "", "Source value"});
    table->add_field({"visit_detail_source_concept_id", "INTEGER", true, false, "", "Source concept"});
    table->add_field({"admitted_from_concept_id", "INTEGER", true, false, "", "Admitted from"});
    table->add_field({"admitted_from_source_value", "VARCHAR(50)", true, false, "", "Admitted from source"});
    table->add_field({"discharged_to_concept_id", "INTEGER", true, false, "", "Discharged to"});
    table->add_field({"discharged_to_source_value", "VARCHAR(50)", true, false, "", "Discharged to source"});
    table->add_field({"preceding_visit_detail_id", "BIGINT", true, false, "", "Preceding visit detail"});
    table->add_field({"parent_visit_detail_id", "BIGINT", true, false, "", "Parent visit detail"});
    table->add_field({"visit_occurrence_id", "BIGINT", false, false, "", "Visit occurrence"});

    // Indexes
    table->add_index({"idx_visit_detail_person", "visit_detail", {"person_id"}, false, false});
    table->add_index({"idx_visit_detail_concept", "visit_detail", {"visit_detail_concept_id"}, false, false});
    table->add_index({"idx_visit_detail_visit", "visit_detail", {"visit_occurrence_id"}, false, false});

    // Foreign keys
    table->add_foreign_key({"fk_visit_detail_person", "visit_detail", "person_id", "person", "person_id"});
    table->add_foreign_key({"fk_visit_detail_concept", "visit_detail", "visit_detail_concept_id", "concept", "concept_id"});
    table->add_foreign_key({"fk_visit_detail_type", "visit_detail", "visit_detail_type_concept_id", "concept", "concept_id"});
    table->add_foreign_key({"fk_visit_detail_visit", "visit_detail", "visit_occurrence_id", "visit_occurrence", "visit_occurrence_id"});

    tables_["visit_detail"] = std::move(table);
}

const TableDefinition* SchemaDefinitions::get_table(const std::string& table_name) const {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = tables_.find(table_name);
    return it != tables_.end() ? it->second.get() : nullptr;
}

std::vector<std::string> SchemaDefinitions::get_table_names() const {
    std::lock_guard<std::mutex> lock(mutex_);
    std::vector<std::string> names;
    names.reserve(tables_.size());
    for (const auto& [name, table] : tables_) {
        names.push_back(name);
    }
    return names;
}

std::vector<std::string> SchemaDefinitions::get_creation_order() const {
    // Tables in dependency order (concept table first as it's referenced by many others)
    return {
        "concept",
        "location",
        "care_site",
        "provider",
        "person",
        "observation_period",
        "visit_occurrence",
        "visit_detail",
        "condition_occurrence",
        "drug_exposure",
        "procedure_occurrence",
        "measurement",
        "observation",
        "death",
        "note"
    };
}

std::vector<std::string> SchemaDefinitions::get_drop_order() const {
    auto creation_order = get_creation_order();
    std::reverse(creation_order.begin(), creation_order.end());
    return creation_order;
}

std::vector<std::string> SchemaDefinitions::generate_schema_sql(
    const std::string& schema_name,
    DatabaseDialect dialect,
    bool include_indexes,
    bool include_constraints) const {

    // Get table order and cache table pointers to avoid repeated locking
    auto table_order = get_creation_order();
    std::vector<const TableDefinition*> tables;
    tables.reserve(table_order.size());

    // Cache all table pointers in one lock
    {
        std::lock_guard<std::mutex> lock(mutex_);
        for (const auto& table_name : table_order) {
            auto it = tables_.find(table_name);
            if (it != tables_.end()) {
                tables.push_back(it->second.get());
            }
        }
    }

    std::vector<std::string> sql_statements;

    // Create schema
    sql_statements.push_back("CREATE SCHEMA IF NOT EXISTS " +
        SqlGenerator::quote_identifier(schema_name, dialect) + ";");

    // Create tables in dependency order
    for (const auto* table : tables) {
        if (table) {
            sql_statements.push_back(table->generate_create_table_sql(schema_name, dialect));
        }
    }

    // Create indexes
    if (include_indexes) {
        for (const auto* table : tables) {
            if (table) {
                auto index_sql = table->generate_create_index_sql(schema_name, dialect);
                sql_statements.insert(sql_statements.end(), index_sql.begin(), index_sql.end());
            }
        }
    }

    // Create foreign key constraints
    if (include_constraints) {
        for (const auto* table : tables) {
            if (table) {
                auto fk_sql = table->generate_foreign_key_sql(schema_name, dialect);
                sql_statements.insert(sql_statements.end(), fk_sql.begin(), fk_sql.end());
            }
        }
    }

    return sql_statements;
}

// SqlGenerator implementation
std::string SqlGenerator::quote_identifier(
    const std::string& identifier,
    DatabaseDialect dialect) {

    switch (dialect) {
        case DatabaseDialect::MySQL:
            return "`" + identifier + "`";
        case DatabaseDialect::SQLServer:
            return "[" + identifier + "]";
        case DatabaseDialect::PostgreSQL:
        case DatabaseDialect::SQLite:
        case DatabaseDialect::Oracle:
        default:
            return "\"" + identifier + "\"";
    }
}

std::string SqlGenerator::format_table_name(
    const std::string& schema_name,
    const std::string& table_name,
    DatabaseDialect dialect) {

    if (schema_name.empty()) {
        return quote_identifier(table_name, dialect);
    }

    return quote_identifier(schema_name, dialect) + "." +
           quote_identifier(table_name, dialect);
}

std::string SqlGenerator::get_auto_increment_syntax(DatabaseDialect dialect) {
    switch (dialect) {
        case DatabaseDialect::MySQL:
            return "AUTO_INCREMENT";
        case DatabaseDialect::SQLServer:
            return "IDENTITY(1,1)";
        case DatabaseDialect::PostgreSQL:
            return "GENERATED ALWAYS AS IDENTITY";
        case DatabaseDialect::SQLite:
            return "AUTOINCREMENT";
        case DatabaseDialect::Oracle:
            return "GENERATED BY DEFAULT AS IDENTITY";
        default:
            return "";
    }
}

std::string SqlGenerator::get_current_timestamp_function(DatabaseDialect dialect) {
    switch (dialect) {
        case DatabaseDialect::MySQL:
            return "NOW()";
        case DatabaseDialect::SQLServer:
            return "GETDATE()";
        case DatabaseDialect::PostgreSQL:
            return "CURRENT_TIMESTAMP";
        case DatabaseDialect::SQLite:
            return "DATETIME('now')";
        case DatabaseDialect::Oracle:
            return "SYSDATE";
        default:
            return "CURRENT_TIMESTAMP";
    }
}

std::string SqlGenerator::quote_value(
    const std::string& value,
    DatabaseDialect dialect) {
    
    std::string escaped = value;
    
    // Escape single quotes by doubling them (standard SQL)
    size_t pos = 0;
    while ((pos = escaped.find('\'', pos)) != std::string::npos) {
        escaped.replace(pos, 1, "''");
        pos += 2;
    }
    
    // MySQL-specific escaping
    if (dialect == DatabaseDialect::MySQL) {
        // Escape backslashes
        pos = 0;
        while ((pos = escaped.find('\\', pos)) != std::string::npos) {
            escaped.replace(pos, 1, "\\\\");
            pos += 2;
        }
    }
    
    return "'" + escaped + "'";
}

std::string SqlGenerator::format_datetime(
    const std::chrono::system_clock::time_point& time_point,
    DatabaseDialect dialect) {
    
    // Convert to time_t
    auto time_t_val = std::chrono::system_clock::to_time_t(time_point);
    
    // Thread-safe formatting using put_time
    std::stringstream ss;
    
    // Use thread-local buffer for thread safety
    thread_local std::array<char, 100> buffer;
    struct tm tm_buf;
    
#ifdef _WIN32
    localtime_s(&tm_buf, &time_t_val);
#else
    localtime_r(&time_t_val, &tm_buf);
#endif
    
    // Format based on dialect
    const char* format = nullptr;
    switch (dialect) {
        case DatabaseDialect::PostgreSQL:
        case DatabaseDialect::MySQL:
        case DatabaseDialect::SQLServer:
            format = "%Y-%m-%d %H:%M:%S";
            break;
        case DatabaseDialect::Oracle:
            // Oracle uses specific date format
            ss << "TO_DATE('";
            format = "%Y-%m-%d %H:%M:%S";
            break;
        case DatabaseDialect::SQLite:
        default:
            format = "%Y-%m-%d %H:%M:%S";
            break;
    }
    
    ss << std::put_time(&tm_buf, format);
    
    if (dialect == DatabaseDialect::Oracle) {
        ss << "', 'YYYY-MM-DD HH24:MI:SS')";
    } else {
        // Wrap in quotes for other databases
        return "'" + ss.str() + "'";
    }

    return ss.str();
}

} // namespace omop::cdm