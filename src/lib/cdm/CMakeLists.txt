# OMOP CDM Library Configuration

# Define the library
add_library(omop_cdm STATIC)

# Set source files
set(CDM_SOURCES
    omop_tables.cpp
    table_definitions.cpp
)

# Set header files
set(CDM_HEADERS
    omop_tables.h
    table_definitions.h
)

# Add source files to the library
target_sources(omop_cdm PRIVATE ${CDM_SOURCES})

# Set include directories
target_include_directories(omop_cdm
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
)

# Link dependencies
target_link_libraries(omop_cdm
    PUBLIC
        omop_common
)

# Install rules
install(TARGETS omop_cdm
    # EXPORT omop-targets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)

install(FILES ${CDM_HEADERS}
    DESTINATION include/omop/cdm
)

# SQL Schema Generation
set(SQL_TEMPLATES
    sql/create_tables.sql.in
    sql/create_indexes.sql.in
    sql/create_constraints.sql.in
    sql/create_provider_care_site.sql.in
    sql/create_location.sql.in
)

set(SQL_OUTPUTS
    ${CMAKE_CURRENT_BINARY_DIR}/sql/create_tables.sql
    ${CMAKE_CURRENT_BINARY_DIR}/sql/create_indexes.sql
    ${CMAKE_CURRENT_BINARY_DIR}/sql/create_constraints.sql
    ${CMAKE_CURRENT_BINARY_DIR}/sql/create_provider_care_site.sql
    ${CMAKE_CURRENT_BINARY_DIR}/sql/create_location.sql
)

# Create output directory
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/sql)

# Configure SQL files if templates exist
foreach(template ${SQL_TEMPLATES})
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/${template})
        get_filename_component(filename ${template} NAME_WE)
        configure_file(
            ${CMAKE_CURRENT_SOURCE_DIR}/${template}
            ${CMAKE_CURRENT_BINARY_DIR}/sql/${filename}.sql
            @ONLY
        )
    endif()
endforeach()

# Install SQL files
install(FILES ${SQL_OUTPUTS}
    DESTINATION share/omop/sql
    OPTIONAL
)