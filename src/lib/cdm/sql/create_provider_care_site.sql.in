-- OMOP CDM v5.4 Provider and Care Site Table Creation Script

-- Create provider and care site tables
SET search_path TO @CDM_SCHEMA@;

-- Provider table
CREATE TABLE IF NOT EXISTS provider (
    provider_id BIGINT PRIMARY KEY,
    provider_name VARCHAR(255),
    npi VARCHAR(20),
    dea VA<PERSON>(20),
    specialty_concept_id INTEGER,
    care_site_id BIGINT,
    year_of_birth INTEGER,
    gender_concept_id INTEGER,
    provider_source_value VARCHAR(50),
    specialty_source_value VARCHAR(50),
    specialty_source_concept_id INTEGER,
    gender_source_value VARCHAR(50),
    gender_source_concept_id INTEGER
);

-- Care site table
CREATE TABLE IF NOT EXISTS care_site (
    care_site_id BIGINT PRIMARY KEY,
    care_site_name VARCHAR(255),
    place_of_service_concept_id INTEGER,
    location_id BIGINT,
    care_site_source_value VARCHAR(50),
    place_of_service_source_value VARCHAR(50)
);

-- Provider indexes
CREATE INDEX IF NOT EXISTS idx_provider_specialty_concept_id ON provider (specialty_concept_id);
CREATE INDEX IF NOT EXISTS idx_provider_care_site_id ON provider (care_site_id);
CREATE INDEX IF NOT EXISTS idx_provider_gender_concept_id ON provider (gender_concept_id);

-- Care site indexes
CREATE INDEX IF NOT EXISTS idx_care_site_place_of_service_concept_id ON care_site (place_of_service_concept_id);
CREATE INDEX IF NOT EXISTS idx_care_site_location_id ON care_site (location_id);

-- Provider constraints
ALTER TABLE provider
    ADD CONSTRAINT fk_provider_specialty_concept FOREIGN KEY (specialty_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id),
    ADD CONSTRAINT fk_provider_care_site FOREIGN KEY (care_site_id)
    REFERENCES care_site (care_site_id),
    ADD CONSTRAINT fk_provider_gender_concept FOREIGN KEY (gender_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id),
    ADD CONSTRAINT fk_provider_specialty_source_concept FOREIGN KEY (specialty_source_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id),
    ADD CONSTRAINT fk_provider_gender_source_concept FOREIGN KEY (gender_source_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id);

-- Care site constraints
ALTER TABLE care_site
    ADD CONSTRAINT fk_care_site_place_of_service_concept FOREIGN KEY (place_of_service_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id);