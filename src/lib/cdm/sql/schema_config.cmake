# OMOP CDM Schema Configuration

# Default schema names
set(CDM_SCHEMA "cdm" CACHE STRING "Name of the CDM schema")
set(VOCAB_SCHEMA "vocab" CACHE STRING "Name of the vocabulary schema")

# Create output directory for SQL files
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR})

# Process SQL template files
add_custom_command(
    OUTPUT
        ${CMAKE_CURRENT_BINARY_DIR}/create_tables.sql
        ${CMAKE_CURRENT_BINARY_DIR}/create_indexes.sql
        ${CMAKE_CURRENT_BINARY_DIR}/create_constraints.sql
        ${CMAKE_CURRENT_BINARY_DIR}/create_provider_care_site.sql
        ${CMAKE_CURRENT_BINARY_DIR}/create_location.sql
    COMMAND ${CMAKE_COMMAND}
        -DCDM_SCHEMA=${CDM_SCHEMA}
        -DVOCAB_SCHEMA=${VOCAB_SCHEMA}
        -DCMAKE_CURRENT_SOURCE_DIR=${CMAKE_CURRENT_SOURCE_DIR}
        -DCMAKE_CURRENT_BINARY_DIR=${CMAKE_CURRENT_BINARY_DIR}
        -P ${CMAKE_CURRENT_SOURCE_DIR}/process_sql.cmake
    DEPENDS
        ${CMAKE_CURRENT_SOURCE_DIR}/create_tables.sql.in
        ${CMAKE_CURRENT_SOURCE_DIR}/create_indexes.sql.in
        ${CMAKE_CURRENT_SOURCE_DIR}/create_constraints.sql.in
        ${CMAKE_CURRENT_SOURCE_DIR}/create_provider_care_site.sql.in
        ${CMAKE_CURRENT_SOURCE_DIR}/create_location.sql.in
        ${CMAKE_CURRENT_SOURCE_DIR}/process_sql.cmake
)

# Add custom target to ensure SQL files are generated
add_custom_target(generate_sql_files
    DEPENDS
        ${CMAKE_CURRENT_BINARY_DIR}/create_tables.sql
        ${CMAKE_CURRENT_BINARY_DIR}/create_indexes.sql
        ${CMAKE_CURRENT_BINARY_DIR}/create_constraints.sql
        ${CMAKE_CURRENT_BINARY_DIR}/create_provider_care_site.sql
        ${CMAKE_CURRENT_BINARY_DIR}/create_location.sql
)

# Install SQL files
install(FILES
    ${CMAKE_CURRENT_BINARY_DIR}/create_tables.sql
    ${CMAKE_CURRENT_BINARY_DIR}/create_indexes.sql
    ${CMAKE_CURRENT_BINARY_DIR}/create_constraints.sql
    ${CMAKE_CURRENT_BINARY_DIR}/create_provider_care_site.sql
    ${CMAKE_CURRENT_BINARY_DIR}/create_location.sql
    DESTINATION ${CMAKE_INSTALL_DATADIR}/omop-etl/sql
)