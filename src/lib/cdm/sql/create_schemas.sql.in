-- OMOP CDM Schema Creation Script

-- Create CDM schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS @CDM_SCHEMA@;

-- Create vocabulary schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS @VOCAB_SCHEMA@;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA @CDM_SCHEMA@ TO PUBLIC;
GRANT USAGE ON SCHEMA @VOCAB_SCHEMA@ TO PUBLIC;

-- Set search path
SET search_path TO @CDM_SCHEMA@, @VOCAB_SCHEMA@, public;