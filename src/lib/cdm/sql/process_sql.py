#!/usr/bin/env python3

import os
import sys
import argparse

def process_sql_file(input_file, output_file, cdm_schema, vocab_schema):
    """Process a SQL template file by replacing variables."""
    with open(input_file, 'r') as f:
        content = f.read()

    # Replace variables
    content = content.replace('@CDM_SCHEMA@', cdm_schema)
    content = content.replace('@VOCAB_SCHEMA@', vocab_schema)

    # Write processed file
    with open(output_file, 'w') as f:
        f.write(content)

def main():
    parser = argparse.ArgumentParser(description='Process SQL template files')
    parser.add_argument('--input', required=True, help='Input SQL template file')
    parser.add_argument('--output', required=True, help='Output SQL file')
    parser.add_argument('--cdm-schema', required=True, help='CDM schema name')
    parser.add_argument('--vocab-schema', required=True, help='Vocabulary schema name')

    args = parser.parse_args()

    process_sql_file(args.input, args.output, args.cdm_schema, args.vocab_schema)

if __name__ == '__main__':
    main()