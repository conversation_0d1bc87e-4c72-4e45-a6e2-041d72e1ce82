-- OMOP CDM v5.4 Constraint Creation Script

-- Create constraints for OMOP CDM tables
SET search_path TO @CDM_SCHEMA@;

-- Person constraints
ALTER TABLE person
    ADD CONSTRAINT fk_person_gender_concept FOREIGN KEY (gender_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id),
    ADD CONSTRAINT fk_person_race_concept FOREIGN KEY (race_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id),
    ADD CONSTRAINT fk_person_ethnicity_concept FOREIGN KEY (ethnicity_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id);

-- Visit occurrence constraints
ALTER TABLE visit_occurrence
    ADD CONSTRAINT fk_visit_person FOREIGN KEY (person_id)
    REFERENCES person (person_id),
    ADD CONSTRAINT fk_visit_concept FOREIGN KEY (visit_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id),
    ADD CONSTRAINT fk_visit_type_concept FOREIG<PERSON> KEY (visit_type_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id);

-- Condition occurrence constraints
ALTER TABLE condition_occurrence
    ADD CONSTRAINT fk_condition_person FOREIGN KEY (person_id)
    REFERENCES person (person_id),
    ADD CONSTRAINT fk_condition_concept FOREIGN KEY (condition_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id),
    ADD CONSTRAINT fk_condition_type_concept FOREIGN KEY (condition_type_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id),
    ADD CONSTRAINT fk_condition_visit FOREIGN KEY (visit_occurrence_id)
    REFERENCES visit_occurrence (visit_occurrence_id);

-- Drug exposure constraints
ALTER TABLE drug_exposure
    ADD CONSTRAINT fk_drug_person FOREIGN KEY (person_id)
    REFERENCES person (person_id),
    ADD CONSTRAINT fk_drug_concept FOREIGN KEY (drug_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id),
    ADD CONSTRAINT fk_drug_type_concept FOREIGN KEY (drug_type_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id),
    ADD CONSTRAINT fk_drug_visit FOREIGN KEY (visit_occurrence_id)
    REFERENCES visit_occurrence (visit_occurrence_id);

-- Procedure occurrence constraints
ALTER TABLE procedure_occurrence
    ADD CONSTRAINT fk_procedure_person FOREIGN KEY (person_id)
    REFERENCES person (person_id),
    ADD CONSTRAINT fk_procedure_concept FOREIGN KEY (procedure_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id),
    ADD CONSTRAINT fk_procedure_type_concept FOREIGN KEY (procedure_type_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id),
    ADD CONSTRAINT fk_procedure_visit FOREIGN KEY (visit_occurrence_id)
    REFERENCES visit_occurrence (visit_occurrence_id);

-- Measurement constraints
ALTER TABLE measurement
    ADD CONSTRAINT fk_measurement_person FOREIGN KEY (person_id)
    REFERENCES person (person_id),
    ADD CONSTRAINT fk_measurement_concept FOREIGN KEY (measurement_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id),
    ADD CONSTRAINT fk_measurement_type_concept FOREIGN KEY (measurement_type_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id),
    ADD CONSTRAINT fk_measurement_visit FOREIGN KEY (visit_occurrence_id)
    REFERENCES visit_occurrence (visit_occurrence_id);

-- Observation constraints
ALTER TABLE observation
    ADD CONSTRAINT fk_observation_person FOREIGN KEY (person_id)
    REFERENCES person (person_id),
    ADD CONSTRAINT fk_observation_concept FOREIGN KEY (observation_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id),
    ADD CONSTRAINT fk_observation_type_concept FOREIGN KEY (observation_type_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id),
    ADD CONSTRAINT fk_observation_visit FOREIGN KEY (visit_occurrence_id)
    REFERENCES visit_occurrence (visit_occurrence_id);

-- Death constraints
ALTER TABLE death
    ADD CONSTRAINT fk_death_person FOREIGN KEY (person_id)
    REFERENCES person (person_id),
    ADD CONSTRAINT fk_death_type_concept FOREIGN KEY (death_type_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id),
    ADD CONSTRAINT fk_death_cause_concept FOREIGN KEY (cause_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id);

-- Note constraints
ALTER TABLE note
    ADD CONSTRAINT fk_note_person FOREIGN KEY (person_id)
    REFERENCES person (person_id),
    ADD CONSTRAINT fk_note_type_concept FOREIGN KEY (note_type_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id),
    ADD CONSTRAINT fk_note_class_concept FOREIGN KEY (note_class_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id),
    ADD CONSTRAINT fk_note_encoding_concept FOREIGN KEY (encoding_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id),
    ADD CONSTRAINT fk_note_language_concept FOREIGN KEY (language_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id),
    ADD CONSTRAINT fk_note_visit FOREIGN KEY (visit_occurrence_id)
    REFERENCES visit_occurrence (visit_occurrence_id);

-- Note NLP constraints
ALTER TABLE note_nlp
    ADD CONSTRAINT fk_note_nlp_note FOREIGN KEY (note_id)
    REFERENCES note (note_id),
    ADD CONSTRAINT fk_note_nlp_concept FOREIGN KEY (note_nlp_concept_id)
    REFERENCES @VOCAB_SCHEMA@.concept (concept_id);

-- Observation Period table constraints
ALTER TABLE observation_period
    ADD CONSTRAINT pk_observation_period PRIMARY KEY (observation_period_id),
    ADD CONSTRAINT fk_observation_period_person FOREIGN KEY (person_id) REFERENCES person(person_id),
    ADD CONSTRAINT fk_observation_period_concept FOREIGN KEY (period_type_concept_id) REFERENCES @VOCAB_SCHEMA@.concept(concept_id);

-- Visit Occurrence table constraints
ALTER TABLE visit_occurrence
    ADD CONSTRAINT pk_visit_occurrence PRIMARY KEY (visit_occurrence_id),
    ADD CONSTRAINT fk_visit_occurrence_source FOREIGN KEY (visit_source_concept_id) REFERENCES @VOCAB_SCHEMA@.concept(concept_id),
    ADD CONSTRAINT fk_visit_occurrence_provider FOREIGN KEY (provider_id) REFERENCES @CDM_SCHEMA@.provider(provider_id),
    ADD CONSTRAINT fk_visit_occurrence_care_site FOREIGN KEY (care_site_id) REFERENCES @CDM_SCHEMA@.care_site(care_site_id);

-- Condition Occurrence table constraints
ALTER TABLE condition_occurrence
    ADD CONSTRAINT pk_condition_occurrence PRIMARY KEY (condition_occurrence_id),
    ADD CONSTRAINT fk_condition_occurrence_source FOREIGN KEY (condition_source_concept_id) REFERENCES @VOCAB_SCHEMA@.concept(concept_id),
    ADD CONSTRAINT fk_condition_occurrence_provider FOREIGN KEY (provider_id) REFERENCES @CDM_SCHEMA@.provider(provider_id);

-- Drug Exposure table constraints
ALTER TABLE drug_exposure
    ADD CONSTRAINT pk_drug_exposure PRIMARY KEY (drug_exposure_id),
    ADD CONSTRAINT fk_drug_exposure_source FOREIGN KEY (drug_source_concept_id) REFERENCES @VOCAB_SCHEMA@.concept(concept_id);

-- Measurement table constraints
ALTER TABLE measurement
    ADD CONSTRAINT fk_measurement_source FOREIGN KEY (measurement_source_concept_id) REFERENCES @VOCAB_SCHEMA@.concept(concept_id),
    ADD CONSTRAINT fk_measurement_unit FOREIGN KEY (unit_concept_id) REFERENCES @VOCAB_SCHEMA@.concept(concept_id);

-- Observation table constraints
ALTER TABLE observation
    ADD CONSTRAINT fk_observation_source FOREIGN KEY (observation_source_concept_id) REFERENCES @VOCAB_SCHEMA@.concept(concept_id),
    ADD CONSTRAINT fk_observation_unit FOREIGN KEY (unit_concept_id) REFERENCES @VOCAB_SCHEMA@.concept(concept_id);

-- Death table constraints
ALTER TABLE death
    ADD CONSTRAINT fk_death_source FOREIGN KEY (cause_source_concept_id) REFERENCES @VOCAB_SCHEMA@.concept(concept_id);

-- Note table constraints
ALTER TABLE note
    ADD CONSTRAINT fk_note_class FOREIGN KEY (note_class_concept_id) REFERENCES @VOCAB_SCHEMA@.concept(concept_id),
    ADD CONSTRAINT fk_note_encoding FOREIGN KEY (encoding_concept_id) REFERENCES @VOCAB_SCHEMA@.concept(concept_id),
    ADD CONSTRAINT fk_note_language FOREIGN KEY (language_concept_id) REFERENCES @VOCAB_SCHEMA@.concept(concept_id);