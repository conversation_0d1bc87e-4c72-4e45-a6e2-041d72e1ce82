# Process SQL template files
foreach(sql_file
    create_tables.sql
    create_indexes.sql
    create_constraints.sql
    create_provider_care_site.sql
    create_location.sql
)
    # Read template file
    file(READ ${CMAKE_CURRENT_SOURCE_DIR}/${sql_file}.in sql_content)

    # Replace variables
    string(REPLACE "@CDM_SCHEMA@" "${CDM_SCHEMA}" sql_content "${sql_content}")
    string(REPLACE "@VOCAB_SCHEMA@" "${VOCAB_SCHEMA}" sql_content "${sql_content}")

    # Write processed file
    file(WRITE ${CMAKE_CURRENT_BINARY_DIR}/${sql_file} "${sql_content}")
endforeach()