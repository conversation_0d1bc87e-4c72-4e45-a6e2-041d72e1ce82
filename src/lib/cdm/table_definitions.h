#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <any>
#include <mutex>

namespace omop::cdm {

/**
 * @brief Field definition for OMOP CDM tables
 * 
 * Represents a single column in an OMOP CDM table with all its metadata
 * including data type, nullability, and constraints.
 */
struct FieldDefinition {
    std::string name;
    std::string data_type;
    bool is_nullable;
    bool is_primary_key;
    std::string default_value;
    std::string comment;
};

/**
 * @brief Index definition for OMOP CDM tables
 * 
 * Defines database indexes to improve query performance on OMOP tables.
 * Supports both unique and non-unique indexes, as well as clustered indexes
 * for databases that support them (e.g., SQL Server).
 */
struct IndexDefinition {
    std::string name;
    std::string table_name;
    std::vector<std::string> columns;
    bool is_unique;
    bool is_clustered;
};

/**
 * @brief Foreign key constraint definition
 * 
 * Defines referential integrity constraints between OMOP tables to ensure
 * data consistency across the CDM schema.
 */
struct ForeignKeyDefinition {
    std::string name;
    std::string table_name;
    std::string column_name;
    std::string referenced_table;
    std::string referenced_column;
};

/**
 * @brief Database dialect for SQL generation
 * 
 * Enumeration of supported database platforms. Each dialect has specific
 * SQL syntax requirements for identifiers, data types, and features.
 */
enum class DatabaseDialect {
    PostgreSQL,
    MySQL,
    SQLServer,
    SQLite,
    Oracle
};

/**
 * @brief Table definition containing all metadata
 * 
 * Comprehensive representation of an OMOP CDM table including fields,
 * indexes, and constraints. Provides SQL generation methods for different
 * database dialects.
 */
class TableDefinition {
public:
    /**
     * @brief Constructor
     * @param name Table name
     */
    explicit TableDefinition(const std::string& name);

    /**
     * @brief Add field to table
     * @param field Field definition
     */
    void add_field(const FieldDefinition& field);

    /**
     * @brief Add index to table
     * @param index Index definition
     */
    void add_index(const IndexDefinition& index);

    /**
     * @brief Add foreign key constraint
     * @param fk Foreign key definition
     */
    void add_foreign_key(const ForeignKeyDefinition& fk);

    /**
     * @brief Get table name
     * @return std::string Table name
     */
    [[nodiscard]] const std::string& get_name() const { return name_; }

    /**
     * @brief Get all fields
     * @return const std::vector<FieldDefinition>& Fields
     */
    [[nodiscard]] const std::vector<FieldDefinition>& get_fields() const { return fields_; }

    /**
     * @brief Get all indexes
     * @return const std::vector<IndexDefinition>& Indexes
     */
    [[nodiscard]] const std::vector<IndexDefinition>& get_indexes() const { return indexes_; }

    /**
     * @brief Get all foreign keys
     * @return const std::vector<ForeignKeyDefinition>& Foreign keys
     */
    [[nodiscard]] const std::vector<ForeignKeyDefinition>& get_foreign_keys() const { return foreign_keys_; }

    /**
     * @brief Generate CREATE TABLE SQL
     * 
     * Creates a complete CREATE TABLE statement for the specified database dialect.
     * Includes all fields, data types, constraints, and primary key definition.
     * 
     * @param schema_name Schema name (e.g., "cdm", "public")
     * @param dialect Database dialect for SQL syntax
     * @return std::string CREATE TABLE SQL statement
     */
    [[nodiscard]] std::string generate_create_table_sql(
        const std::string& schema_name,
        DatabaseDialect dialect = DatabaseDialect::PostgreSQL) const;

    /**
     * @brief Generate CREATE INDEX SQL statements
     * 
     * Creates all index definitions for the table. Returns a vector as multiple
     * indexes may be defined for a single table.
     * 
     * @param schema_name Schema name
     * @param dialect Database dialect
     * @return std::vector<std::string> CREATE INDEX statements
     */
    [[nodiscard]] std::vector<std::string> generate_create_index_sql(
        const std::string& schema_name,
        DatabaseDialect dialect = DatabaseDialect::PostgreSQL) const;

    /**
     * @brief Generate ALTER TABLE ADD CONSTRAINT SQL statements
     * 
     * Creates foreign key constraint definitions. These are typically applied
     * after all tables are created to avoid dependency issues.
     * 
     * @param schema_name Schema name
     * @param dialect Database dialect
     * @return std::vector<std::string> ALTER TABLE statements
     */
    [[nodiscard]] std::vector<std::string> generate_foreign_key_sql(
        const std::string& schema_name,
        DatabaseDialect dialect = DatabaseDialect::PostgreSQL) const;

private:
    std::string name_;
    std::vector<FieldDefinition> fields_;
    std::vector<IndexDefinition> indexes_;
    std::vector<ForeignKeyDefinition> foreign_keys_;

    /**
     * @brief Convert field type to SQL data type
     * @param field_type Field type
     * @param dialect Database dialect
     * @return std::string SQL data type
     */
    [[nodiscard]] std::string field_type_to_sql(
        const std::string& field_type,
        DatabaseDialect dialect) const;
};

/**
 * @brief OMOP CDM schema definitions
 * 
 * Singleton class containing all OMOP CDM v5.4 table definitions.
 * Provides methods to retrieve table metadata and generate SQL DDL
 * statements for schema creation.
 */
class SchemaDefinitions {
public:
    /**
     * @brief Get singleton instance
     * @return SchemaDefinitions& Instance
     */
    static SchemaDefinitions& instance();

    /**
     * @brief Get table definition
     * @param table_name Table name
     * @return const TableDefinition* Table definition or nullptr if not found
     */
    [[nodiscard]] const TableDefinition* get_table(const std::string& table_name) const;

    /**
     * @brief Get all table names
     * @return std::vector<std::string> Table names
     */
    [[nodiscard]] std::vector<std::string> get_table_names() const;

    /**
     * @brief Get tables in dependency order for creation
     * 
     * Returns table names ordered to respect foreign key dependencies.
     * Tables without dependencies come first, followed by dependent tables.
     * 
     * @return std::vector<std::string> Ordered table names
     */
    [[nodiscard]] std::vector<std::string> get_creation_order() const;

    /**
     * @brief Get tables in dependency order for dropping
     * 
     * Returns table names in reverse dependency order for safe deletion.
     * Dependent tables come first to avoid foreign key constraint violations.
     * 
     * @return std::vector<std::string> Ordered table names
     */
    [[nodiscard]] std::vector<std::string> get_drop_order() const;

    /**
     * @brief Generate full schema SQL
     * 
     * Generates all SQL statements needed to create a complete OMOP CDM schema.
     * Includes schema creation, tables, indexes, and foreign key constraints.
     * 
     * @param schema_name Schema name (default: "cdm")
     * @param dialect Database dialect (default: PostgreSQL)
     * @param include_indexes Include index creation statements (default: true)
     * @param include_constraints Include foreign key constraints (default: true)
     * @return std::vector<std::string> Ordered SQL statements for execution
     */
    [[nodiscard]] std::vector<std::string> generate_schema_sql(
        const std::string& schema_name = "cdm",
        DatabaseDialect dialect = DatabaseDialect::PostgreSQL,
        bool include_indexes = true,
        bool include_constraints = true) const;

private:
    SchemaDefinitions();
    ~SchemaDefinitions() = default;
    SchemaDefinitions(const SchemaDefinitions&) = delete;
    SchemaDefinitions& operator=(const SchemaDefinitions&) = delete;

    void initialize_tables();
    void initialize_concept_table();
    void initialize_person_table();
    void initialize_observation_period_table();
    void initialize_visit_occurrence_table();
    void initialize_condition_occurrence_table();
    void initialize_drug_exposure_table();
    void initialize_procedure_occurrence_table();
    void initialize_measurement_table();
    void initialize_observation_table();
    void initialize_death_table();
    void initialize_note_table();
    void initialize_location_table();
    void initialize_care_site_table();
    void initialize_provider_table();
    void initialize_visit_detail_table();

    std::unordered_map<std::string, std::unique_ptr<TableDefinition>> tables_;
    mutable std::mutex mutex_; // Thread safety for singleton access
};

/**
 * @brief SQL generator for different database dialects
 * 
 * Utility class providing static methods for generating SQL syntax
 * specific to different database platforms. Handles identifier quoting,
 * data type mapping, and platform-specific features.
 */
class SqlGenerator {
public:
    /**
     * @brief Quote identifier based on dialect
     * @param identifier Identifier to quote
     * @param dialect Database dialect
     * @return std::string Quoted identifier
     */
    [[nodiscard]] static std::string quote_identifier(
        const std::string& identifier,
        DatabaseDialect dialect);

    /**
     * @brief Format schema and table name
     * @param schema_name Schema name
     * @param table_name Table name
     * @param dialect Database dialect
     * @return std::string Formatted table name
     */
    [[nodiscard]] static std::string format_table_name(
        const std::string& schema_name,
        const std::string& table_name,
        DatabaseDialect dialect);

    /**
     * @brief Get auto-increment syntax
     * @param dialect Database dialect
     * @return std::string Auto-increment syntax
     */
    [[nodiscard]] static std::string get_auto_increment_syntax(DatabaseDialect dialect);

    /**
     * @brief Get current timestamp function
     * @param dialect Database dialect
     * @return std::string Current timestamp function
     */
    [[nodiscard]] static std::string get_current_timestamp_function(DatabaseDialect dialect);

    /**
     * @brief Escape string value for SQL
     * 
     * Properly escapes string values to prevent SQL injection.
     * Doubles single quotes and handles special characters based on dialect.
     * 
     * @param value String value to escape
     * @param dialect Database dialect
     * @return std::string Escaped string value with quotes
     */
    [[nodiscard]] static std::string quote_value(
        const std::string& value,
        DatabaseDialect dialect);

    /**
     * @brief Format date/time value for SQL
     * 
     * Thread-safe formatting of date/time values for SQL statements.
     * 
     * @param time_point Time point to format
     * @param dialect Database dialect
     * @return std::string Formatted date/time string
     */
    [[nodiscard]] static std::string format_datetime(
        const std::chrono::system_clock::time_point& time_point,
        DatabaseDialect dialect);
};

} // namespace omop::cdm