#include "omop_tables.h"
#include "table_definitions.h"
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <any>
#include <array>
#include <ctime>
#include <stdexcept>

namespace omop::cdm {

// Helper functions for OmopTable base class
std::string OmopTable::format_datetime_sql(
    const std::chrono::system_clock::time_point& time_point) {
    
    if (time_point == std::chrono::system_clock::time_point{}) {
        return "NULL";
    }
    
    // Thread-safe date formatting
    auto time_t_val = std::chrono::system_clock::to_time_t(time_point);
    std::stringstream ss;
    
    thread_local std::array<char, 100> buffer;
    struct tm tm_buf;
    
#ifdef _WIN32
    localtime_s(&tm_buf, &time_t_val);
#else
    localtime_r(&time_t_val, &tm_buf);
#endif
    
    ss << "'" << std::put_time(&tm_buf, "%Y-%m-%d %H:%M:%S") << "'";
    return ss.str();
}

std::string OmopTable::escape_string_sql(const std::string& value) {
    if (value.empty()) {
        return "NULL";
    }
    
    std::string escaped = value;
    
    // Escape single quotes by doubling them
    size_t pos = 0;
    while ((pos = escaped.find('\'', pos)) != std::string::npos) {
        escaped.replace(pos, 1, "''");
        pos += 2;
    }
    
    // Escape backslashes for safety
    pos = 0;
    while ((pos = escaped.find('\\', pos)) != std::string::npos) {
        escaped.replace(pos, 1, "\\\\");
        pos += 2;
    }
    
    return "'" + escaped + "'";
}

// Person implementation
std::string Person::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";
    
    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }
    
    ss << ") VALUES (";
    
    // Build values with proper escaping
    ss << person_id << ", ";
    ss << gender_concept_id << ", ";
    ss << year_of_birth << ", ";
    ss << (month_of_birth.has_value() ? std::to_string(month_of_birth.value()) : "NULL") << ", ";
    ss << (day_of_birth.has_value() ? std::to_string(day_of_birth.value()) : "NULL") << ", ";
    ss << (birth_datetime.has_value() ? format_datetime_sql(birth_datetime.value()) : "NULL") << ", ";
    ss << race_concept_id << ", ";
    ss << ethnicity_concept_id << ", ";
    ss << (location_id.has_value() ? std::to_string(location_id.value()) : "NULL") << ", ";
    ss << (provider_id.has_value() ? std::to_string(provider_id.value()) : "NULL") << ", ";
    ss << (care_site_id.has_value() ? std::to_string(care_site_id.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (person_source_value.has_value() ? escape_string_sql(person_source_value.value()) : "NULL") << ", ";
        ss << (gender_source_value.has_value() ? escape_string_sql(gender_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (person_source_value.has_value() ? "'" + person_source_value.value() + "'" : "NULL") << ", ";
        ss << (gender_source_value.has_value() ? "'" + gender_source_value.value() + "'" : "NULL") << ", ";
    }
    
    ss << (gender_source_concept_id.has_value() ? std::to_string(gender_source_concept_id.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (race_source_value.has_value() ? escape_string_sql(race_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (race_source_value.has_value() ? "'" + race_source_value.value() + "'" : "NULL") << ", ";
    }
    
    ss << (race_source_concept_id.has_value() ? std::to_string(race_source_concept_id.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (ethnicity_source_value.has_value() ? escape_string_sql(ethnicity_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (ethnicity_source_value.has_value() ? "'" + ethnicity_source_value.value() + "'" : "NULL") << ", ";
    }
    
    ss << (ethnicity_source_concept_id.has_value() ? std::to_string(ethnicity_source_concept_id.value()) : "NULL");
    
    ss << ")";
    return ss.str();
}

std::vector<std::string> Person::field_names() const {
    return {
        "person_id", "gender_concept_id", "year_of_birth", "month_of_birth",
        "day_of_birth", "birth_datetime", "race_concept_id", "ethnicity_concept_id",
        "location_id", "provider_id", "care_site_id", "person_source_value",
        "gender_source_value", "gender_source_concept_id", "race_source_value",
        "race_source_concept_id", "ethnicity_source_value", "ethnicity_source_concept_id"
    };
}

std::vector<std::any> Person::field_values() const {
    std::vector<std::any> values;
    values.reserve(18);
    
    values.push_back(person_id);
    values.push_back(gender_concept_id);
    values.push_back(year_of_birth);
    values.push_back(month_of_birth.value_or(0));
    values.push_back(day_of_birth.value_or(0));
    values.push_back(birth_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(race_concept_id);
    values.push_back(ethnicity_concept_id);
    values.push_back(location_id.value_or(0));
    values.push_back(provider_id.value_or(0));
    values.push_back(care_site_id.value_or(0));
    values.push_back(person_source_value.value_or(""));
    values.push_back(gender_source_value.value_or(""));
    values.push_back(gender_source_concept_id.value_or(0));
    values.push_back(race_source_value.value_or(""));
    values.push_back(race_source_concept_id.value_or(0));
    values.push_back(ethnicity_source_value.value_or(""));
    values.push_back(ethnicity_source_concept_id.value_or(0));
    
    return values;
}

void Person::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("person_id", person_id);
    visitor.visit("gender_concept_id", gender_concept_id);
    visitor.visit("year_of_birth", year_of_birth);
    visitor.visit("month_of_birth", month_of_birth);
    visitor.visit("day_of_birth", day_of_birth);
    visitor.visit("birth_datetime", birth_datetime);
    visitor.visit("race_concept_id", race_concept_id);
    visitor.visit("ethnicity_concept_id", ethnicity_concept_id);
    visitor.visit("location_id", location_id);
    visitor.visit("provider_id", provider_id);
    visitor.visit("care_site_id", care_site_id);
    visitor.visit("person_source_value", person_source_value);
    visitor.visit("gender_source_value", gender_source_value);
    visitor.visit("gender_source_concept_id", gender_source_concept_id);
    visitor.visit("race_source_value", race_source_value);
    visitor.visit("race_source_concept_id", race_source_concept_id);
    visitor.visit("ethnicity_source_value", ethnicity_source_value);
    visitor.visit("ethnicity_source_concept_id", ethnicity_source_concept_id);
}

bool Person::validate() const {
    return person_id > 0 && 
           gender_concept_id > 0 && 
           year_of_birth > 0 &&
           validate_year_of_birth() &&
           validate_month_of_birth() &&
           validate_day_of_birth();
}

std::vector<std::string> Person::validation_errors() const {
    std::vector<std::string> errors;
    
    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }
    
    if (gender_concept_id <= 0) {
        errors.push_back("gender_concept_id must be positive");
    }
    
    if (year_of_birth <= 0) {
        errors.push_back("year_of_birth must be positive");
    } else if (!validate_year_of_birth()) {
        auto current_year = std::chrono::year_month_day{
            std::chrono::floor<std::chrono::days>(std::chrono::system_clock::now())
        }.year();
        errors.push_back("year_of_birth must be between 1850 and " + 
                        std::to_string(static_cast<int>(current_year)));
    }
    
    if (!validate_month_of_birth()) {
        errors.push_back("month_of_birth must be between 1 and 12");
    }
    
    if (!validate_day_of_birth()) {
        errors.push_back("day_of_birth must be between 1 and 31");
    }
    
    return errors;
}

bool Person::validate_year_of_birth() const {
    auto current_year = std::chrono::year_month_day{
        std::chrono::floor<std::chrono::days>(std::chrono::system_clock::now())
    }.year();
    
    return year_of_birth >= 1850 && year_of_birth <= static_cast<int>(current_year);
}

bool Person::validate_month_of_birth() const {
    if (!month_of_birth.has_value()) {
        return true; // Optional field
    }
    return month_of_birth.value() >= 1 && month_of_birth.value() <= 12;
}

bool Person::validate_day_of_birth() const {
    if (!day_of_birth.has_value()) {
        return true; // Optional field
    }
    
    if (day_of_birth.value() < 1 || day_of_birth.value() > 31) {
        return false;
    }
    
    // More sophisticated validation considering month
    if (month_of_birth.has_value()) {
        int month = month_of_birth.value();
        int day = day_of_birth.value();
        
        // Days in month (non-leap year)
        static const int days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
        
        if (month >= 1 && month <= 12) {
            if (month == 2 && day == 29) {
                // Accept Feb 29 for simplicity (proper leap year check would need year)
                return true;
            }
            return day <= days_in_month[month - 1];
        }
    }
    
    return true;
}

// ObservationPeriod implementation
std::string ObservationPeriod::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";
    
    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }
    
    ss << ") VALUES (";
    ss << observation_period_id << ", ";
    ss << person_id << ", ";
    ss << format_datetime_sql(observation_period_start_date) << ", ";
    ss << format_datetime_sql(observation_period_end_date) << ", ";
    ss << period_type_concept_id;
    ss << ")";
    
    return ss.str();
}

std::vector<std::string> ObservationPeriod::field_names() const {
    return {
        "observation_period_id", "person_id", "observation_period_start_date",
        "observation_period_end_date", "period_type_concept_id"
    };
}

std::vector<std::any> ObservationPeriod::field_values() const {
    std::vector<std::any> values;
    values.reserve(5);
    
    values.push_back(observation_period_id);
    values.push_back(person_id);
    values.push_back(observation_period_start_date);
    values.push_back(observation_period_end_date);
    values.push_back(period_type_concept_id);
    
    return values;
}

void ObservationPeriod::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("observation_period_id", observation_period_id);
    visitor.visit("person_id", person_id);
    visitor.visit("observation_period_start_date", observation_period_start_date);
    visitor.visit("observation_period_end_date", observation_period_end_date);
    visitor.visit("period_type_concept_id", period_type_concept_id);
}

bool ObservationPeriod::validate() const {
    return observation_period_id > 0 && 
           person_id > 0 &&
           period_type_concept_id > 0 &&
           validate_date_range();
}

std::vector<std::string> ObservationPeriod::validation_errors() const {
    std::vector<std::string> errors;
    
    if (observation_period_id <= 0) {
        errors.push_back("observation_period_id must be positive");
    }
    
    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }
    
    if (!validate_date_range()) {
        errors.push_back("observation_period_start_date must be before observation_period_end_date");
    }
    
    if (period_type_concept_id <= 0) {
        errors.push_back("period_type_concept_id must be positive");
    }
    
    return errors;
}

bool ObservationPeriod::validate_date_range() const {
    return observation_period_start_date < observation_period_end_date;
}

// VisitOccurrence implementation
std::string VisitOccurrence::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";
    
    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }
    
    ss << ") VALUES (";
    ss << visit_occurrence_id << ", ";
    ss << person_id << ", ";
    ss << visit_concept_id << ", ";
    ss << format_datetime_sql(visit_start_date) << ", ";
    ss << format_datetime_sql(visit_end_date) << ", ";
    ss << visit_type_concept_id << ", ";
    ss << (visit_start_datetime.has_value() ? format_datetime_sql(visit_start_datetime.value()) : "NULL") << ", ";
    ss << (visit_end_datetime.has_value() ? format_datetime_sql(visit_end_datetime.value()) : "NULL") << ", ";
    ss << (provider_id.has_value() ? std::to_string(provider_id.value()) : "NULL") << ", ";
    ss << (care_site_id.has_value() ? std::to_string(care_site_id.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (visit_source_value.has_value() ? escape_string_sql(visit_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (visit_source_value.has_value() ? "'" + visit_source_value.value() + "'" : "NULL") << ", ";
    }
    
    ss << (visit_source_concept_id.has_value() ? std::to_string(visit_source_concept_id.value()) : "NULL") << ", ";
    ss << (admitted_from_concept_id.has_value() ? std::to_string(admitted_from_concept_id.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (admitted_from_source_value.has_value() ? escape_string_sql(admitted_from_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (admitted_from_source_value.has_value() ? "'" + admitted_from_source_value.value() + "'" : "NULL") << ", ";
    }
    
    ss << (discharged_to_concept_id.has_value() ? std::to_string(discharged_to_concept_id.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (discharged_to_source_value.has_value() ? escape_string_sql(discharged_to_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (discharged_to_source_value.has_value() ? "'" + discharged_to_source_value.value() + "'" : "NULL") << ", ";
    }
    
    ss << (preceding_visit_occurrence_id.has_value() ? std::to_string(preceding_visit_occurrence_id.value()) : "NULL");
    ss << ")";
    
    return ss.str();
}

std::vector<std::string> VisitOccurrence::field_names() const {
    return {
        "visit_occurrence_id", "person_id", "visit_concept_id", "visit_start_date",
        "visit_end_date", "visit_type_concept_id", "visit_start_datetime",
        "visit_end_datetime", "provider_id", "care_site_id", "visit_source_value",
        "visit_source_concept_id", "admitted_from_concept_id", "admitted_from_source_value",
        "discharged_to_concept_id", "discharged_to_source_value", "preceding_visit_occurrence_id"
    };
}

std::vector<std::any> VisitOccurrence::field_values() const {
    std::vector<std::any> values;
    values.reserve(17);
    
    values.push_back(visit_occurrence_id);
    values.push_back(person_id);
    values.push_back(visit_concept_id);
    values.push_back(visit_start_date);
    values.push_back(visit_end_date);
    values.push_back(visit_type_concept_id);
    values.push_back(visit_start_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(visit_end_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(provider_id.value_or(0));
    values.push_back(care_site_id.value_or(0));
    values.push_back(visit_source_value.value_or(""));
    values.push_back(visit_source_concept_id.value_or(0));
    values.push_back(admitted_from_concept_id.value_or(0));
    values.push_back(admitted_from_source_value.value_or(""));
    values.push_back(discharged_to_concept_id.value_or(0));
    values.push_back(discharged_to_source_value.value_or(""));
    values.push_back(preceding_visit_occurrence_id.value_or(0));
    
    return values;
}

void VisitOccurrence::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("visit_occurrence_id", visit_occurrence_id);
    visitor.visit("person_id", person_id);
    visitor.visit("visit_concept_id", visit_concept_id);
    visitor.visit("visit_start_date", visit_start_date);
    visitor.visit("visit_end_date", visit_end_date);
    visitor.visit("visit_type_concept_id", visit_type_concept_id);
    visitor.visit("visit_start_datetime", visit_start_datetime);
    visitor.visit("visit_end_datetime", visit_end_datetime);
    visitor.visit("provider_id", provider_id);
    visitor.visit("care_site_id", care_site_id);
    visitor.visit("visit_source_value", visit_source_value);
    visitor.visit("visit_source_concept_id", visit_source_concept_id);
    visitor.visit("admitted_from_concept_id", admitted_from_concept_id);
    visitor.visit("admitted_from_source_value", admitted_from_source_value);
    visitor.visit("discharged_to_concept_id", discharged_to_concept_id);
    visitor.visit("discharged_to_source_value", discharged_to_source_value);
    visitor.visit("preceding_visit_occurrence_id", preceding_visit_occurrence_id);
}

bool VisitOccurrence::validate() const {
    return visit_occurrence_id > 0 && 
           person_id > 0 && 
           visit_concept_id > 0 &&
           visit_start_date < visit_end_date && 
           visit_type_concept_id > 0;
}

std::vector<std::string> VisitOccurrence::validation_errors() const {
    std::vector<std::string> errors;
    
    if (visit_occurrence_id <= 0) {
        errors.push_back("visit_occurrence_id must be positive");
    }
    
    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }
    
    if (visit_concept_id <= 0) {
        errors.push_back("visit_concept_id must be positive");
    }
    
    if (visit_start_date >= visit_end_date) {
        errors.push_back("visit_start_date must be before visit_end_date");
    }
    
    if (visit_type_concept_id <= 0) {
        errors.push_back("visit_type_concept_id must be positive");
    }
    
    return errors;
}

// ConditionOccurrence implementation
std::string ConditionOccurrence::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";
    
    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }
    
    ss << ") VALUES (";
    ss << condition_occurrence_id << ", ";
    ss << person_id << ", ";
    ss << condition_concept_id << ", ";
    ss << format_datetime_sql(condition_start_date) << ", ";
    ss << (condition_start_datetime.has_value() ? format_datetime_sql(condition_start_datetime.value()) : "NULL") << ", ";
    ss << (condition_end_date.has_value() ? format_datetime_sql(condition_end_date.value()) : "NULL") << ", ";
    ss << (condition_end_datetime.has_value() ? format_datetime_sql(condition_end_datetime.value()) : "NULL") << ", ";
    ss << condition_type_concept_id << ", ";
    ss << (condition_status_concept_id.has_value() ? std::to_string(condition_status_concept_id.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (stop_reason.has_value() ? escape_string_sql(stop_reason.value()) : "NULL") << ", ";
    } else {
        ss << (stop_reason.has_value() ? "'" + stop_reason.value() + "'" : "NULL") << ", ";
    }
    
    ss << (provider_id.has_value() ? std::to_string(provider_id.value()) : "NULL") << ", ";
    ss << (visit_occurrence_id.has_value() ? std::to_string(visit_occurrence_id.value()) : "NULL") << ", ";
    ss << (visit_detail_id.has_value() ? std::to_string(visit_detail_id.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (condition_source_value.has_value() ? escape_string_sql(condition_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (condition_source_value.has_value() ? "'" + condition_source_value.value() + "'" : "NULL") << ", ";
    }
    
    ss << (condition_source_concept_id.has_value() ? std::to_string(condition_source_concept_id.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (condition_status_source_value.has_value() ? escape_string_sql(condition_status_source_value.value()) : "NULL");
    } else {
        ss << (condition_status_source_value.has_value() ? "'" + condition_status_source_value.value() + "'" : "NULL");
    }
    
    ss << ")";
    return ss.str();
}

std::vector<std::string> ConditionOccurrence::field_names() const {
    return {
        "condition_occurrence_id", "person_id", "condition_concept_id",
        "condition_start_date", "condition_start_datetime", "condition_end_date",
        "condition_end_datetime", "condition_type_concept_id", "condition_status_concept_id",
        "stop_reason", "provider_id", "visit_occurrence_id", "visit_detail_id",
        "condition_source_value", "condition_source_concept_id", "condition_status_source_value"
    };
}

std::vector<std::any> ConditionOccurrence::field_values() const {
    std::vector<std::any> values;
    values.reserve(16);
    
    values.push_back(condition_occurrence_id);
    values.push_back(person_id);
    values.push_back(condition_concept_id);
    values.push_back(condition_start_date);
    values.push_back(condition_start_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(condition_end_date.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(condition_end_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(condition_type_concept_id);
    values.push_back(condition_status_concept_id.value_or(0));
    values.push_back(stop_reason.value_or(""));
    values.push_back(provider_id.value_or(0));
    values.push_back(visit_occurrence_id.value_or(0));
    values.push_back(visit_detail_id.value_or(0));
    values.push_back(condition_source_value.value_or(""));
    values.push_back(condition_source_concept_id.value_or(0));
    values.push_back(condition_status_source_value.value_or(""));
    
    return values;
}

void ConditionOccurrence::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("condition_occurrence_id", condition_occurrence_id);
    visitor.visit("person_id", person_id);
    visitor.visit("condition_concept_id", condition_concept_id);
    visitor.visit("condition_start_date", condition_start_date);
    visitor.visit("condition_start_datetime", condition_start_datetime);
    visitor.visit("condition_end_date", condition_end_date);
    visitor.visit("condition_end_datetime", condition_end_datetime);
    visitor.visit("condition_type_concept_id", condition_type_concept_id);
    visitor.visit("condition_status_concept_id", condition_status_concept_id);
    visitor.visit("stop_reason", stop_reason);
    visitor.visit("provider_id", provider_id);
    visitor.visit("visit_occurrence_id", visit_occurrence_id);
    visitor.visit("visit_detail_id", visit_detail_id);
    visitor.visit("condition_source_value", condition_source_value);
    visitor.visit("condition_source_concept_id", condition_source_concept_id);
    visitor.visit("condition_status_source_value", condition_status_source_value);
}

bool ConditionOccurrence::validate() const {
    return condition_occurrence_id > 0 && 
           person_id > 0 && 
           condition_concept_id > 0 &&
           condition_type_concept_id > 0;
}

std::vector<std::string> ConditionOccurrence::validation_errors() const {
    std::vector<std::string> errors;
    
    if (condition_occurrence_id <= 0) {
        errors.push_back("condition_occurrence_id must be positive");
    }
    
    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }
    
    if (condition_concept_id <= 0) {
        errors.push_back("condition_concept_id must be positive");
    }
    
    if (condition_type_concept_id <= 0) {
        errors.push_back("condition_type_concept_id must be positive");
    }
    
    return errors;
}

// DrugExposure implementation
std::string DrugExposure::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";
    
    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }
    
    ss << ") VALUES (";
    ss << drug_exposure_id << ", ";
    ss << person_id << ", ";
    ss << drug_concept_id << ", ";
    ss << format_datetime_sql(drug_exposure_start_date) << ", ";
    ss << (drug_exposure_start_datetime.has_value() ? format_datetime_sql(drug_exposure_start_datetime.value()) : "NULL") << ", ";
    ss << format_datetime_sql(drug_exposure_end_date) << ", ";
    ss << (drug_exposure_end_datetime.has_value() ? format_datetime_sql(drug_exposure_end_datetime.value()) : "NULL") << ", ";
    ss << (verbatim_end_date.has_value() ? format_datetime_sql(verbatim_end_date.value()) : "NULL") << ", ";
    ss << drug_type_concept_id << ", ";
    
    if (escape_values) {
        ss << (stop_reason.has_value() ? escape_string_sql(stop_reason.value()) : "NULL") << ", ";
    } else {
        ss << (stop_reason.has_value() ? "'" + stop_reason.value() + "'" : "NULL") << ", ";
    }
    
    ss << (refills.has_value() ? std::to_string(refills.value()) : "NULL") << ", ";
    ss << (quantity.has_value() ? std::to_string(quantity.value()) : "NULL") << ", ";
    ss << (days_supply.has_value() ? std::to_string(days_supply.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (sig.has_value() ? escape_string_sql(sig.value()) : "NULL") << ", ";
    } else {
        ss << (sig.has_value() ? "'" + sig.value() + "'" : "NULL") << ", ";
    }
    
    ss << (route_concept_id.has_value() ? std::to_string(route_concept_id.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (lot_number.has_value() ? escape_string_sql(lot_number.value()) : "NULL") << ", ";
    } else {
        ss << (lot_number.has_value() ? "'" + lot_number.value() + "'" : "NULL") << ", ";
    }
    
    ss << (provider_id.has_value() ? std::to_string(provider_id.value()) : "NULL") << ", ";
    ss << (visit_occurrence_id.has_value() ? std::to_string(visit_occurrence_id.value()) : "NULL") << ", ";
    ss << (visit_detail_id.has_value() ? std::to_string(visit_detail_id.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (drug_source_value.has_value() ? escape_string_sql(drug_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (drug_source_value.has_value() ? "'" + drug_source_value.value() + "'" : "NULL") << ", ";
    }
    
    ss << (drug_source_concept_id.has_value() ? std::to_string(drug_source_concept_id.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (route_source_value.has_value() ? escape_string_sql(route_source_value.value()) : "NULL") << ", ";
        ss << (dose_unit_source_value.has_value() ? escape_string_sql(dose_unit_source_value.value()) : "NULL");
    } else {
        ss << (route_source_value.has_value() ? "'" + route_source_value.value() + "'" : "NULL") << ", ";
        ss << (dose_unit_source_value.has_value() ? "'" + dose_unit_source_value.value() + "'" : "NULL");
    }
    
    ss << ")";
    return ss.str();
}

std::vector<std::string> DrugExposure::field_names() const {
    return {
        "drug_exposure_id", "person_id", "drug_concept_id", "drug_exposure_start_date",
        "drug_exposure_start_datetime", "drug_exposure_end_date", "drug_exposure_end_datetime",
        "verbatim_end_date", "drug_type_concept_id", "stop_reason", "refills",
        "quantity", "days_supply", "sig", "route_concept_id", "lot_number",
        "provider_id", "visit_occurrence_id", "visit_detail_id", "drug_source_value",
        "drug_source_concept_id", "route_source_value", "dose_unit_source_value"
    };
}

std::vector<std::any> DrugExposure::field_values() const {
    std::vector<std::any> values;
    values.reserve(23);
    
    values.push_back(drug_exposure_id);
    values.push_back(person_id);
    values.push_back(drug_concept_id);
    values.push_back(drug_exposure_start_date);
    values.push_back(drug_exposure_start_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(drug_exposure_end_date);
    values.push_back(drug_exposure_end_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(verbatim_end_date.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(drug_type_concept_id);
    values.push_back(stop_reason.value_or(""));
    values.push_back(refills.value_or(0));
    values.push_back(quantity.value_or(0.0));
    values.push_back(days_supply.value_or(0));
    values.push_back(sig.value_or(""));
    values.push_back(route_concept_id.value_or(0));
    values.push_back(lot_number.value_or(""));
    values.push_back(provider_id.value_or(0));
    values.push_back(visit_occurrence_id.value_or(0));
    values.push_back(visit_detail_id.value_or(0));
    values.push_back(drug_source_value.value_or(""));
    values.push_back(drug_source_concept_id.value_or(0));
    values.push_back(route_source_value.value_or(""));
    values.push_back(dose_unit_source_value.value_or(""));
    
    return values;
}

void DrugExposure::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("drug_exposure_id", drug_exposure_id);
    visitor.visit("person_id", person_id);
    visitor.visit("drug_concept_id", drug_concept_id);
    visitor.visit("drug_exposure_start_date", drug_exposure_start_date);
    visitor.visit("drug_exposure_start_datetime", drug_exposure_start_datetime);
    visitor.visit("drug_exposure_end_date", drug_exposure_end_date);
    visitor.visit("drug_exposure_end_datetime", drug_exposure_end_datetime);
    visitor.visit("verbatim_end_date", verbatim_end_date);
    visitor.visit("drug_type_concept_id", drug_type_concept_id);
    visitor.visit("stop_reason", stop_reason);
    visitor.visit("refills", refills);
    visitor.visit("quantity", quantity);
    visitor.visit("days_supply", days_supply);
    visitor.visit("sig", sig);
    visitor.visit("route_concept_id", route_concept_id);
    visitor.visit("lot_number", lot_number);
    visitor.visit("provider_id", provider_id);
    visitor.visit("visit_occurrence_id", visit_occurrence_id);
    visitor.visit("visit_detail_id", visit_detail_id);
    visitor.visit("drug_source_value", drug_source_value);
    visitor.visit("drug_source_concept_id", drug_source_concept_id);
    visitor.visit("route_source_value", route_source_value);
    visitor.visit("dose_unit_source_value", dose_unit_source_value);
}

bool DrugExposure::validate() const {
    return drug_exposure_id > 0 && 
           person_id > 0 && 
           drug_concept_id > 0 &&
           drug_type_concept_id > 0 && 
           drug_exposure_start_date < drug_exposure_end_date &&
           validate_quantity() &&
           validate_days_supply();
}

std::vector<std::string> DrugExposure::validation_errors() const {
    std::vector<std::string> errors;
    
    if (drug_exposure_id <= 0) {
        errors.push_back("drug_exposure_id must be positive");
    }
    
    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }
    
    if (drug_concept_id <= 0) {
        errors.push_back("drug_concept_id must be positive");
    }
    
    if (drug_type_concept_id <= 0) {
        errors.push_back("drug_type_concept_id must be positive");
    }
    
    if (drug_exposure_start_date >= drug_exposure_end_date) {
        errors.push_back("drug_exposure_start_date must be before drug_exposure_end_date");
    }
    
    if (!validate_quantity()) {
        errors.push_back("quantity must be positive");
    }
    
    if (!validate_days_supply()) {
        errors.push_back("days_supply must be positive");
    }
    
    return errors;
}

bool DrugExposure::validate_quantity() const {
    if (!quantity.has_value()) {
        return true; // Optional field
    }
    return quantity.value() > 0;
}

bool DrugExposure::validate_days_supply() const {
    if (!days_supply.has_value()) {
        return true; // Optional field
    }
    return days_supply.value() > 0;
}

// ProcedureOccurrence implementation
std::string ProcedureOccurrence::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";
    
    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }
    
    ss << ") VALUES (";
    ss << procedure_occurrence_id << ", ";
    ss << person_id << ", ";
    ss << procedure_concept_id << ", ";
    ss << format_datetime_sql(procedure_date) << ", ";
    ss << (procedure_datetime.has_value() ? format_datetime_sql(procedure_datetime.value()) : "NULL") << ", ";
    ss << (procedure_end_date.has_value() ? format_datetime_sql(procedure_end_date.value()) : "NULL") << ", ";
    ss << (procedure_end_datetime.has_value() ? format_datetime_sql(procedure_end_datetime.value()) : "NULL") << ", ";
    ss << procedure_type_concept_id << ", ";
    ss << (modifier_concept_id.has_value() ? std::to_string(modifier_concept_id.value()) : "NULL") << ", ";
    ss << (quantity.has_value() ? std::to_string(quantity.value()) : "NULL") << ", ";
    ss << (provider_id.has_value() ? std::to_string(provider_id.value()) : "NULL") << ", ";
    ss << (visit_occurrence_id.has_value() ? std::to_string(visit_occurrence_id.value()) : "NULL") << ", ";
    ss << (visit_detail_id.has_value() ? std::to_string(visit_detail_id.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (procedure_source_value.has_value() ? escape_string_sql(procedure_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (procedure_source_value.has_value() ? "'" + procedure_source_value.value() + "'" : "NULL") << ", ";
    }
    
    ss << (procedure_source_concept_id.has_value() ? std::to_string(procedure_source_concept_id.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (modifier_source_value.has_value() ? escape_string_sql(modifier_source_value.value()) : "NULL");
    } else {
        ss << (modifier_source_value.has_value() ? "'" + modifier_source_value.value() + "'" : "NULL");
    }
    
    ss << ")";
    return ss.str();
}

std::vector<std::string> ProcedureOccurrence::field_names() const {
    return {
        "procedure_occurrence_id", "person_id", "procedure_concept_id", "procedure_date",
        "procedure_datetime", "procedure_end_date", "procedure_end_datetime",
        "procedure_type_concept_id", "modifier_concept_id", "quantity", "provider_id",
        "visit_occurrence_id", "visit_detail_id", "procedure_source_value",
        "procedure_source_concept_id", "modifier_source_value"
    };
}

std::vector<std::any> ProcedureOccurrence::field_values() const {
    std::vector<std::any> values;
    values.reserve(16);
    
    values.push_back(procedure_occurrence_id);
    values.push_back(person_id);
    values.push_back(procedure_concept_id);
    values.push_back(procedure_date);
    values.push_back(procedure_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(procedure_end_date.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(procedure_end_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(procedure_type_concept_id);
    values.push_back(modifier_concept_id.value_or(0));
    values.push_back(quantity.value_or(0));
    values.push_back(provider_id.value_or(0));
    values.push_back(visit_occurrence_id.value_or(0));
    values.push_back(visit_detail_id.value_or(0));
    values.push_back(procedure_source_value.value_or(""));
    values.push_back(procedure_source_concept_id.value_or(0));
    values.push_back(modifier_source_value.value_or(""));
    
    return values;
}

void ProcedureOccurrence::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("procedure_occurrence_id", procedure_occurrence_id);
    visitor.visit("person_id", person_id);
    visitor.visit("procedure_concept_id", procedure_concept_id);
    visitor.visit("procedure_date", procedure_date);
    visitor.visit("procedure_datetime", procedure_datetime);
    visitor.visit("procedure_end_date", procedure_end_date);
    visitor.visit("procedure_end_datetime", procedure_end_datetime);
    visitor.visit("procedure_type_concept_id", procedure_type_concept_id);
    visitor.visit("modifier_concept_id", modifier_concept_id);
    visitor.visit("quantity", quantity);
    visitor.visit("provider_id", provider_id);
    visitor.visit("visit_occurrence_id", visit_occurrence_id);
    visitor.visit("visit_detail_id", visit_detail_id);
    visitor.visit("procedure_source_value", procedure_source_value);
    visitor.visit("procedure_source_concept_id", procedure_source_concept_id);
    visitor.visit("modifier_source_value", modifier_source_value);
}

bool ProcedureOccurrence::validate() const {
    return procedure_occurrence_id > 0 && 
           person_id > 0 && 
           procedure_concept_id > 0 &&
           procedure_type_concept_id > 0;
}

std::vector<std::string> ProcedureOccurrence::validation_errors() const {
    std::vector<std::string> errors;
    
    if (procedure_occurrence_id <= 0) {
        errors.push_back("procedure_occurrence_id must be positive");
    }
    
    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }
    
    if (procedure_concept_id <= 0) {
        errors.push_back("procedure_concept_id must be positive");
    }
    
    if (procedure_type_concept_id <= 0) {
        errors.push_back("procedure_type_concept_id must be positive");
    }
    
    return errors;
}

// Measurement implementation
std::string Measurement::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";
    
    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }
    
    ss << ") VALUES (";
    ss << measurement_id << ", ";
    ss << person_id << ", ";
    ss << measurement_concept_id << ", ";
    ss << format_datetime_sql(measurement_date) << ", ";
    ss << (measurement_datetime.has_value() ? format_datetime_sql(measurement_datetime.value()) : "NULL") << ", ";
    ss << (measurement_time.has_value() ? format_datetime_sql(measurement_time.value()) : "NULL") << ", ";
    ss << measurement_type_concept_id << ", ";
    ss << (operator_concept_id.has_value() ? std::to_string(operator_concept_id.value()) : "NULL") << ", ";
    ss << (value_as_number.has_value() ? std::to_string(value_as_number.value()) : "NULL") << ", ";
    ss << (value_as_concept_id.has_value() ? std::to_string(value_as_concept_id.value()) : "NULL") << ", ";
    ss << (unit_concept_id.has_value() ? std::to_string(unit_concept_id.value()) : "NULL") << ", ";
    ss << (range_low.has_value() ? std::to_string(range_low.value()) : "NULL") << ", ";
    ss << (range_high.has_value() ? std::to_string(range_high.value()) : "NULL") << ", ";
    ss << (provider_id.has_value() ? std::to_string(provider_id.value()) : "NULL") << ", ";
    ss << (visit_occurrence_id.has_value() ? std::to_string(visit_occurrence_id.value()) : "NULL") << ", ";
    ss << (visit_detail_id.has_value() ? std::to_string(visit_detail_id.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (measurement_source_value.has_value() ? escape_string_sql(measurement_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (measurement_source_value.has_value() ? "'" + measurement_source_value.value() + "'" : "NULL") << ", ";
    }
    
    ss << (measurement_source_concept_id.has_value() ? std::to_string(measurement_source_concept_id.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (unit_source_value.has_value() ? escape_string_sql(unit_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (unit_source_value.has_value() ? "'" + unit_source_value.value() + "'" : "NULL") << ", ";
    }
    
    ss << (unit_source_concept_id.has_value() ? std::to_string(unit_source_concept_id.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (value_source_value.has_value() ? escape_string_sql(value_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (value_source_value.has_value() ? "'" + value_source_value.value() + "'" : "NULL") << ", ";
    }
    
    ss << (measurement_event_id.has_value() ? std::to_string(measurement_event_id.value()) : "NULL") << ", ";
    ss << (meas_event_field_concept_id.has_value() ? std::to_string(meas_event_field_concept_id.value()) : "NULL");
    
    ss << ")";
    return ss.str();
}

std::vector<std::string> Measurement::field_names() const {
    return {
        "measurement_id", "person_id", "measurement_concept_id", "measurement_date",
        "measurement_datetime", "measurement_time", "measurement_type_concept_id",
        "operator_concept_id", "value_as_number", "value_as_concept_id", "unit_concept_id",
        "range_low", "range_high", "provider_id", "visit_occurrence_id", "visit_detail_id",
        "measurement_source_value", "measurement_source_concept_id", "unit_source_value",
        "unit_source_concept_id", "value_source_value", "measurement_event_id",
        "meas_event_field_concept_id"
    };
}

std::vector<std::any> Measurement::field_values() const {
    std::vector<std::any> values;
    values.reserve(23);
    
    values.push_back(measurement_id);
    values.push_back(person_id);
    values.push_back(measurement_concept_id);
    values.push_back(measurement_date);
    values.push_back(measurement_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(measurement_time.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(measurement_type_concept_id);
    values.push_back(operator_concept_id.value_or(0));
    values.push_back(value_as_number.value_or(0.0));
    values.push_back(value_as_concept_id.value_or(0));
    values.push_back(unit_concept_id.value_or(0));
    values.push_back(range_low.value_or(0.0));
    values.push_back(range_high.value_or(0.0));
    values.push_back(provider_id.value_or(0));
    values.push_back(visit_occurrence_id.value_or(0));
    values.push_back(visit_detail_id.value_or(0));
    values.push_back(measurement_source_value.value_or(""));
    values.push_back(measurement_source_concept_id.value_or(0));
    values.push_back(unit_source_value.value_or(""));
    values.push_back(unit_source_concept_id.value_or(0));
    values.push_back(value_source_value.value_or(""));
    values.push_back(measurement_event_id.value_or(0));
    values.push_back(meas_event_field_concept_id.value_or(0));
    
    return values;
}

void Measurement::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("measurement_id", measurement_id);
    visitor.visit("person_id", person_id);
    visitor.visit("measurement_concept_id", measurement_concept_id);
    visitor.visit("measurement_date", measurement_date);
    visitor.visit("measurement_datetime", measurement_datetime);
    visitor.visit("measurement_time", measurement_time);
    visitor.visit("measurement_type_concept_id", measurement_type_concept_id);
    visitor.visit("operator_concept_id", operator_concept_id);
    visitor.visit("value_as_number", value_as_number);
    visitor.visit("value_as_concept_id", value_as_concept_id);
    visitor.visit("unit_concept_id", unit_concept_id);
    visitor.visit("range_low", range_low);
    visitor.visit("range_high", range_high);
    visitor.visit("provider_id", provider_id);
    visitor.visit("visit_occurrence_id", visit_occurrence_id);
    visitor.visit("visit_detail_id", visit_detail_id);
    visitor.visit("measurement_source_value", measurement_source_value);
    visitor.visit("measurement_source_concept_id", measurement_source_concept_id);
    visitor.visit("unit_source_value", unit_source_value);
    visitor.visit("unit_source_concept_id", unit_source_concept_id);
    visitor.visit("value_source_value", value_source_value);
    visitor.visit("measurement_event_id", measurement_event_id);
    visitor.visit("meas_event_field_concept_id", meas_event_field_concept_id);
}

bool Measurement::validate() const {
    return measurement_id > 0 && 
           person_id > 0 && 
           measurement_concept_id > 0 &&
           measurement_type_concept_id > 0 &&
           validate_ranges();
}

std::vector<std::string> Measurement::validation_errors() const {
    std::vector<std::string> errors;
    
    if (measurement_id <= 0) {
        errors.push_back("measurement_id must be positive");
    }
    
    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }
    
    if (measurement_concept_id <= 0) {
        errors.push_back("measurement_concept_id must be positive");
    }
    
    if (measurement_type_concept_id <= 0) {
        errors.push_back("measurement_type_concept_id must be positive");
    }
    
    if (!validate_ranges()) {
        errors.push_back("range_low must be less than or equal to range_high");
    }
    
    return errors;
}

bool Measurement::validate_ranges() const {
    if (!range_low.has_value() || !range_high.has_value()) {
        return true; // Both are optional
    }
    return range_low.value() <= range_high.value();
}

// Observation implementation
std::string Observation::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";
    
    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }
    
    ss << ") VALUES (";
    ss << observation_id << ", ";
    ss << person_id << ", ";
    ss << observation_concept_id << ", ";
    ss << format_datetime_sql(observation_date) << ", ";
    ss << (observation_datetime.has_value() ? format_datetime_sql(observation_datetime.value()) : "NULL") << ", ";
    ss << observation_type_concept_id << ", ";
    ss << (value_as_number.has_value() ? std::to_string(value_as_number.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (value_as_string.has_value() ? escape_string_sql(value_as_string.value()) : "NULL") << ", ";
    } else {
        ss << (value_as_string.has_value() ? "'" + value_as_string.value() + "'" : "NULL") << ", ";
    }
    
    ss << (value_as_concept_id.has_value() ? std::to_string(value_as_concept_id.value()) : "NULL") << ", ";
    ss << (qualifier_concept_id.has_value() ? std::to_string(qualifier_concept_id.value()) : "NULL") << ", ";
    ss << (unit_concept_id.has_value() ? std::to_string(unit_concept_id.value()) : "NULL") << ", ";
    ss << (provider_id.has_value() ? std::to_string(provider_id.value()) : "NULL") << ", ";
    ss << (visit_occurrence_id.has_value() ? std::to_string(visit_occurrence_id.value()) : "NULL") << ", ";
    ss << (visit_detail_id.has_value() ? std::to_string(visit_detail_id.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (observation_source_value.has_value() ? escape_string_sql(observation_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (observation_source_value.has_value() ? "'" + observation_source_value.value() + "'" : "NULL") << ", ";
    }
    
    ss << (observation_source_concept_id.has_value() ? std::to_string(observation_source_concept_id.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (unit_source_value.has_value() ? escape_string_sql(unit_source_value.value()) : "NULL") << ", ";
        ss << (qualifier_source_value.has_value() ? escape_string_sql(qualifier_source_value.value()) : "NULL") << ", ";
        ss << (value_source_value.has_value() ? escape_string_sql(value_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (unit_source_value.has_value() ? "'" + unit_source_value.value() + "'" : "NULL") << ", ";
        ss << (qualifier_source_value.has_value() ? "'" + qualifier_source_value.value() + "'" : "NULL") << ", ";
        ss << (value_source_value.has_value() ? "'" + value_source_value.value() + "'" : "NULL") << ", ";
    }
    
    ss << (observation_event_id.has_value() ? std::to_string(observation_event_id.value()) : "NULL") << ", ";
    ss << (obs_event_field_concept_id.has_value() ? std::to_string(obs_event_field_concept_id.value()) : "NULL");
    
    ss << ")";
    return ss.str();
}

std::vector<std::string> Observation::field_names() const {
    return {
        "observation_id", "person_id", "observation_concept_id", "observation_date",
        "observation_datetime", "observation_type_concept_id", "value_as_number",
        "value_as_string", "value_as_concept_id", "qualifier_concept_id", "unit_concept_id",
        "provider_id", "visit_occurrence_id", "visit_detail_id", "observation_source_value",
        "observation_source_concept_id", "unit_source_value", "qualifier_source_value",
        "value_source_value", "observation_event_id", "obs_event_field_concept_id"
    };
}

std::vector<std::any> Observation::field_values() const {
    std::vector<std::any> values;
    values.reserve(21);
    
    values.push_back(observation_id);
    values.push_back(person_id);
    values.push_back(observation_concept_id);
    values.push_back(observation_date);
    values.push_back(observation_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(observation_type_concept_id);
    values.push_back(value_as_number.value_or(0.0));
    values.push_back(value_as_string.value_or(""));
    values.push_back(value_as_concept_id.value_or(0));
    values.push_back(qualifier_concept_id.value_or(0));
    values.push_back(unit_concept_id.value_or(0));
    values.push_back(provider_id.value_or(0));
    values.push_back(visit_occurrence_id.value_or(0));
    values.push_back(visit_detail_id.value_or(0));
    values.push_back(observation_source_value.value_or(""));
    values.push_back(observation_source_concept_id.value_or(0));
    values.push_back(unit_source_value.value_or(""));
    values.push_back(qualifier_source_value.value_or(""));
    values.push_back(value_source_value.value_or(""));
    values.push_back(observation_event_id.value_or(0));
    values.push_back(obs_event_field_concept_id.value_or(0));
    
    return values;
}

void Observation::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("observation_id", observation_id);
    visitor.visit("person_id", person_id);
    visitor.visit("observation_concept_id", observation_concept_id);
    visitor.visit("observation_date", observation_date);
    visitor.visit("observation_datetime", observation_datetime);
    visitor.visit("observation_type_concept_id", observation_type_concept_id);
    visitor.visit("value_as_number", value_as_number);
    visitor.visit("value_as_string", value_as_string);
    visitor.visit("value_as_concept_id", value_as_concept_id);
    visitor.visit("qualifier_concept_id", qualifier_concept_id);
    visitor.visit("unit_concept_id", unit_concept_id);
    visitor.visit("provider_id", provider_id);
    visitor.visit("visit_occurrence_id", visit_occurrence_id);
    visitor.visit("visit_detail_id", visit_detail_id);
    visitor.visit("observation_source_value", observation_source_value);
    visitor.visit("observation_source_concept_id", observation_source_concept_id);
    visitor.visit("unit_source_value", unit_source_value);
    visitor.visit("qualifier_source_value", qualifier_source_value);
    visitor.visit("value_source_value", value_source_value);
    visitor.visit("observation_event_id", observation_event_id);
    visitor.visit("obs_event_field_concept_id", obs_event_field_concept_id);
}

bool Observation::validate() const {
    return observation_id > 0 && 
           person_id > 0 && 
           observation_concept_id > 0 &&
           observation_type_concept_id > 0;
}

std::vector<std::string> Observation::validation_errors() const {
    std::vector<std::string> errors;
    
    if (observation_id <= 0) {
        errors.push_back("observation_id must be positive");
    }
    
    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }
    
    if (observation_concept_id <= 0) {
        errors.push_back("observation_concept_id must be positive");
    }
    
    if (observation_type_concept_id <= 0) {
        errors.push_back("observation_type_concept_id must be positive");
    }
    
    return errors;
}

// Death implementation
std::string Death::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";
    
    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }
    
    ss << ") VALUES (";
    ss << person_id << ", ";
    ss << format_datetime_sql(death_date) << ", ";
    ss << (death_datetime.has_value() ? format_datetime_sql(death_datetime.value()) : "NULL") << ", ";
    ss << (death_type_concept_id.has_value() ? std::to_string(death_type_concept_id.value()) : "NULL") << ", ";
    ss << (cause_concept_id.has_value() ? std::to_string(cause_concept_id.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (cause_source_value.has_value() ? escape_string_sql(cause_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (cause_source_value.has_value() ? "'" + cause_source_value.value() + "'" : "NULL") << ", ";
    }
    
    ss << (cause_source_concept_id.has_value() ? std::to_string(cause_source_concept_id.value()) : "NULL");
    
    ss << ")";
    return ss.str();
}

std::vector<std::string> Death::field_names() const {
    return {
        "person_id", "death_date", "death_datetime", "death_type_concept_id",
        "cause_concept_id", "cause_source_value", "cause_source_concept_id"
    };
}

std::vector<std::any> Death::field_values() const {
    std::vector<std::any> values;
    values.reserve(7);
    
    values.push_back(person_id);
    values.push_back(death_date);
    values.push_back(death_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(death_type_concept_id.value_or(0));
    values.push_back(cause_concept_id.value_or(0));
    values.push_back(cause_source_value.value_or(""));
    values.push_back(cause_source_concept_id.value_or(0));
    
    return values;
}

void Death::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("person_id", person_id);
    visitor.visit("death_date", death_date);
    visitor.visit("death_datetime", death_datetime);
    visitor.visit("death_type_concept_id", death_type_concept_id);
    visitor.visit("cause_concept_id", cause_concept_id);
    visitor.visit("cause_source_value", cause_source_value);
    visitor.visit("cause_source_concept_id", cause_source_concept_id);
}

bool Death::validate() const {
    return person_id > 0 && validate_death_date();
}

std::vector<std::string> Death::validation_errors() const {
    std::vector<std::string> errors;
    
    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }
    
    if (!validate_death_date()) {
        errors.push_back("death_date cannot be in the future");
    }
    
    return errors;
}

bool Death::validate_death_date() const {
    auto now = std::chrono::system_clock::now();
    return death_date <= now;
}

// Note implementation
std::string Note::to_insert_sql(bool escape_values) const {
    std::stringstream ss;
    ss << "INSERT INTO " << schema_name() << "." << table_name() << " (";
    
    auto names = field_names();
    for (size_t i = 0; i < names.size(); ++i) {
        if (i > 0) ss << ", ";
        ss << names[i];
    }
    
    ss << ") VALUES (";
    ss << note_id << ", ";
    ss << person_id << ", ";
    ss << format_datetime_sql(note_date) << ", ";
    ss << (note_datetime.has_value() ? format_datetime_sql(note_datetime.value()) : "NULL") << ", ";
    ss << note_type_concept_id << ", ";
    ss << note_class_concept_id << ", ";
    
    if (escape_values) {
        ss << escape_string_sql(note_title) << ", ";
        ss << escape_string_sql(note_text) << ", ";
    } else {
        ss << "'" << note_title << "', ";
        ss << "'" << note_text << "', ";
    }
    
    ss << encoding_concept_id << ", ";
    ss << language_concept_id << ", ";
    ss << (provider_id.has_value() ? std::to_string(provider_id.value()) : "NULL") << ", ";
    ss << (visit_occurrence_id.has_value() ? std::to_string(visit_occurrence_id.value()) : "NULL") << ", ";
    ss << (visit_detail_id.has_value() ? std::to_string(visit_detail_id.value()) : "NULL") << ", ";
    
    if (escape_values) {
        ss << (note_source_value.has_value() ? escape_string_sql(note_source_value.value()) : "NULL") << ", ";
    } else {
        ss << (note_source_value.has_value() ? "'" + note_source_value.value() + "'" : "NULL") << ", ";
    }
    
    ss << (note_event_id.has_value() ? std::to_string(note_event_id.value()) : "NULL") << ", ";
    ss << (note_event_field_concept_id.has_value() ? std::to_string(note_event_field_concept_id.value()) : "NULL");
    
    ss << ")";
    return ss.str();
}

std::vector<std::string> Note::field_names() const {
    return {
        "note_id", "person_id", "note_date", "note_datetime", "note_type_concept_id",
        "note_class_concept_id", "note_title", "note_text", "encoding_concept_id",
        "language_concept_id", "provider_id", "visit_occurrence_id", "visit_detail_id",
        "note_source_value", "note_event_id", "note_event_field_concept_id"
    };
}

std::vector<std::any> Note::field_values() const {
    std::vector<std::any> values;
    values.reserve(16);
    
    values.push_back(note_id);
    values.push_back(person_id);
    values.push_back(note_date);
    values.push_back(note_datetime.value_or(std::chrono::system_clock::time_point{}));
    values.push_back(note_type_concept_id);
    values.push_back(note_class_concept_id);
    values.push_back(note_title);
    values.push_back(note_text);
    values.push_back(encoding_concept_id);
    values.push_back(language_concept_id);
    values.push_back(provider_id.value_or(0));
    values.push_back(visit_occurrence_id.value_or(0));
    values.push_back(visit_detail_id.value_or(0));
    values.push_back(note_source_value.value_or(""));
    values.push_back(note_event_id.value_or(0));
    values.push_back(note_event_field_concept_id.value_or(0));
    
    return values;
}

void Note::visit_fields(FieldVisitor& visitor) const {
    visitor.visit("note_id", note_id);
    visitor.visit("person_id", person_id);
    visitor.visit("note_date", note_date);
    visitor.visit("note_datetime", note_datetime);
    visitor.visit("note_type_concept_id", note_type_concept_id);
    visitor.visit("note_class_concept_id", note_class_concept_id);
    visitor.visit("note_title", note_title);
    visitor.visit("note_text", note_text);
    visitor.visit("encoding_concept_id", encoding_concept_id);
    visitor.visit("language_concept_id", language_concept_id);
    visitor.visit("provider_id", provider_id);
    visitor.visit("visit_occurrence_id", visit_occurrence_id);
    visitor.visit("visit_detail_id", visit_detail_id);
    visitor.visit("note_source_value", note_source_value);
    visitor.visit("note_event_id", note_event_id);
    visitor.visit("note_event_field_concept_id", note_event_field_concept_id);
}

bool Note::validate() const {
    return note_id > 0 && 
           person_id > 0 && 
           note_type_concept_id > 0 &&
           note_class_concept_id > 0;
}

std::vector<std::string> Note::validation_errors() const {
    std::vector<std::string> errors;
    
    if (note_id <= 0) {
        errors.push_back("note_id must be positive");
    }
    
    if (person_id <= 0) {
        errors.push_back("person_id must be positive");
    }
    
    if (note_type_concept_id <= 0) {
        errors.push_back("note_type_concept_id must be positive");
    }
    
    if (note_class_concept_id <= 0) {
        errors.push_back("note_class_concept_id must be positive");
    }
    
    return errors;
}

// OmopTableFactory implementation
std::unordered_map<std::string, std::function<std::unique_ptr<OmopTable>()>>& 
OmopTableFactory::get_creators() {
    static std::unordered_map<std::string, std::function<std::unique_ptr<OmopTable>()>> creators = {
        {"person", []() { return std::make_unique<Person>(); }},
        {"observation_period", []() { return std::make_unique<ObservationPeriod>(); }},
        {"visit_occurrence", []() { return std::make_unique<VisitOccurrence>(); }},
        {"condition_occurrence", []() { return std::make_unique<ConditionOccurrence>(); }},
        {"drug_exposure", []() { return std::make_unique<DrugExposure>(); }},
        {"procedure_occurrence", []() { return std::make_unique<ProcedureOccurrence>(); }},
        {"measurement", []() { return std::make_unique<Measurement>(); }},
        {"observation", []() { return std::make_unique<Observation>(); }},
        {"death", []() { return std::make_unique<Death>(); }},
        {"note", []() { return std::make_unique<Note>(); }}
    };
    return creators;
}

std::unique_ptr<OmopTable> OmopTableFactory::create(const std::string& table_name) {
    auto& creators = get_creators();
    auto it = creators.find(table_name);
    if (it != creators.end()) {
        return it->second();
    }
    return nullptr;
}

std::vector<std::string> OmopTableFactory::get_supported_tables() {
    auto& creators = get_creators();
    std::vector<std::string> tables;
    tables.reserve(creators.size());
    
    for (const auto& [name, creator] : creators) {
        tables.push_back(name);
    }
    
    return tables;
}

bool OmopTableFactory::is_supported(const std::string& table_name) {
    auto& creators = get_creators();
    return creators.find(table_name) != creators.end();
}

void OmopTableFactory::register_table(
    const std::string& table_name,
    std::function<std::unique_ptr<OmopTable>()> creator) {
    
    auto& creators = get_creators();
    creators[table_name] = creator;
}

// OmopSchema implementation
std::string OmopSchema::get_create_table_sql(
    const std::string& table_name,
    const std::string& schema_name) {

    auto& schema_def = SchemaDefinitions::instance();
    auto table = schema_def.get_table(table_name);

    if (!table) {
        throw std::invalid_argument("Unknown table: " + table_name);
    }

    return table->generate_create_table_sql(schema_name);
}

std::vector<std::string> OmopSchema::get_all_create_table_sql(
    const std::string& schema_name) {

    auto& schema_def = SchemaDefinitions::instance();
    auto table_order = schema_def.get_creation_order();
    std::vector<std::string> sql_statements;
    sql_statements.reserve(table_order.size());

    for (const auto& table_name : table_order) {
        try {
            sql_statements.push_back(get_create_table_sql(table_name, schema_name));
        } catch (const std::exception& e) {
            // Log error but continue with other tables
            // In production, this should use proper logging
            continue;
        }
    }

    return sql_statements;
}

std::vector<std::string> OmopSchema::get_table_indexes(
    const std::string& table_name,
    const std::string& schema_name) {

    auto& schema_def = SchemaDefinitions::instance();
    auto table = schema_def.get_table(table_name);

    if (!table) {
        throw std::invalid_argument("Unknown table: " + table_name);
    }

    return table->generate_create_index_sql(schema_name);
}

std::vector<std::string> OmopSchema::get_foreign_keys(
    const std::string& table_name,
    const std::string& schema_name) {

    auto& schema_def = SchemaDefinitions::instance();
    auto table = schema_def.get_table(table_name);

    if (!table) {
        throw std::invalid_argument("Unknown table: " + table_name);
    }

    return table->generate_foreign_key_sql(schema_name);
}

} // namespace omop::cdm