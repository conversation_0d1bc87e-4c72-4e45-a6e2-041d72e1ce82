# Service library
set(SERVICE_SOURCES
    service.cpp
    etl_service.cpp
)

set(SERVICE_HEADERS
    etl_service.h
)

add_library(omop_service STATIC ${SERVICE_SOURCES})

target_link_libraries(omop_service
    PUBLIC
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
)

target_include_directories(omop_service
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/..>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
)

# Set compile features
target_compile_features(omop_service PUBLIC cxx_std_20)

# Install rules
install(TARGETS omop_service
    EXPORT omop-etl-targets
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES ${SERVICE_HEADERS}
    DESTINATION include/omop/service
)