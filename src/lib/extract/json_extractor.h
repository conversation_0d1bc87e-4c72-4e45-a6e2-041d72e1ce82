#pragma once

#include "extract/database_connector.h"
#include "core/interfaces.h"
#include <nlohmann/json.hpp>
#include <fstream>
#include <filesystem>
#include <queue>
#include <stack>
#include <thread>
#include <condition_variable>

namespace omop::extract {

using json = nlohmann::json;

/**
 * @brief JSON parsing options
 */
struct JsonOptions {
    std::string root_path;              ///< JSON path to data array (e.g., "data.patients")
    bool flatten_nested{true};          ///< Flatten nested objects
    std::string array_delimiter{"_"};   ///< Delimiter for flattened array indices
    bool parse_dates{true};             ///< Automatically parse date strings
    std::vector<std::string> date_formats{
        "%Y-%m-%d",
        "%Y-%m-%dT%H:%M:%S",
        "%Y-%m-%d %H:%M:%S"
    };
    bool ignore_null{true};             ///< Skip null values
    size_t max_depth{10};               ///< Maximum nesting depth
};

/**
 * @brief JSON extractor for single JSON files
 *
 * Extracts data from JSON files, supporting both array and object formats,
 * with automatic flattening of nested structures.
 */
class JsonExtractor : public core::IExtractor {
public:
    /**
     * @brief Constructor
     */
    JsonExtractor() = default;

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return core::RecordBatch Extracted records
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                   core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "json"; }

    /**
     * @brief Finalize extraction
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

protected:
    /**
     * @brief Load JSON file
     * @param filepath File path
     */
    void load_file(const std::string& filepath);

    /**
     * @brief Navigate to data array using JSON path
     * @param root Root JSON object
     * @param path Dot-separated path (e.g., "data.patients")
     * @return json::const_iterator Iterator to array
     */
    json::const_iterator navigate_to_data(const json& root, const std::string& path);

public:
    /**
     * @brief Flatten JSON object to record
     * @param obj JSON object
     * @param prefix Field name prefix
     * @param depth Current nesting depth
     * @return core::Record Flattened record
     */
    core::Record flatten_json_object(const json& obj,
                                    const std::string& prefix = "",
                                    size_t depth = 0);

protected:

    /**
     * @brief Convert JSON value to std::any
     * @param value JSON value
     * @return std::any Converted value
     */
    std::any json_to_any(const json& value);

    /**
     * @brief Parse date string
     * @param date_str Date string
     * @return std::optional<std::chrono::system_clock::time_point> Parsed date
     */
    std::optional<std::chrono::system_clock::time_point> parse_date(const std::string& date_str);

public:
    JsonOptions options_;

protected:
    std::string filepath_;
    json json_data_;
    json::const_iterator current_iterator_;
    json::const_iterator end_iterator_;
    size_t total_records_{0};
    size_t extracted_count_{0};
    bool data_loaded_{false};
    std::chrono::steady_clock::time_point start_time_;
};

/**
 * @brief JSON Lines (JSONL) extractor
 *
 * Extracts data from JSON Lines files where each line is a separate JSON object.
 * More memory-efficient for large files.
 */
class JsonLinesExtractor : public core::IExtractor {
public:
    /**
     * @brief Constructor
     */
    JsonLinesExtractor() = default;

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return core::RecordBatch Extracted records
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                   core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "jsonl"; }

    /**
     * @brief Finalize extraction
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

protected:
    /**
     * @brief Open JSONL file
     * @param filepath File path
     */
    void open_file(const std::string& filepath);

    /**
     * @brief Read next line and parse JSON
     * @return std::optional<json> Parsed JSON object
     */
    std::optional<json> read_next_line();

    /**
     * @brief Convert JSON object to record
     * @param obj JSON object
     * @return core::Record Converted record
     */
    core::Record json_to_record(const json& obj);

private:
    std::ifstream file_stream_;
    std::string filepath_;
    JsonOptions options_;
    size_t current_line_{0};
    size_t extracted_count_{0};
    size_t error_count_{0};
    bool has_more_{true};
    std::chrono::steady_clock::time_point start_time_;
};

/**
 * @brief Streaming JSON extractor for very large files
 *
 * Uses a SAX-style parser to handle JSON files that don't fit in memory.
 */
class StreamingJsonExtractor : public core::IExtractor {
public:
    /**
     * @brief SAX event handler for JSON parsing
     */
    class JsonHandler : public nlohmann::json_sax<json> {
    public:
        JsonHandler(std::queue<core::Record>& record_queue,
                   const JsonOptions& options)
            : record_queue_(record_queue), options_(options) {}

        // SAX interface implementation
        bool null() override;
        bool boolean(bool val) override;
        bool number_integer(number_integer_t val) override;
        bool number_unsigned(number_unsigned_t val) override;
        bool number_float(number_float_t val, const string_t& s) override;
        bool string(string_t& val) override;
        bool binary(binary_t& val) override;
        bool start_object(std::size_t elements) override;
        bool end_object() override;
        bool start_array(std::size_t elements) override;
        bool end_array() override;
        bool key(string_t& val) override;
        bool parse_error(std::size_t position, const std::string& last_token,
                        const nlohmann::detail::exception& ex) override;

    private:
        std::queue<core::Record>& record_queue_;
        const JsonOptions& options_;
        std::stack<std::string> path_stack_;
        std::stack<json> object_stack_;
        std::string current_key_;
        bool in_data_array_{false};
    };

    /**
     * @brief Constructor
     */
    StreamingJsonExtractor() = default;

    // IExtractor interface implementation
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;
    core::RecordBatch extract_batch(size_t batch_size,
                                   core::ProcessingContext& context) override;
    bool has_more_data() const override;
    std::string get_type() const override { return "streaming_json"; }
    void finalize(core::ProcessingContext& context) override;
    std::unordered_map<std::string, std::any> get_statistics() const override;

private:
    std::ifstream file_stream_;
    std::string filepath_;
    JsonOptions options_;
    std::queue<core::Record> record_queue_;
    std::unique_ptr<std::thread> parser_thread_;
    mutable std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    std::atomic<bool> parsing_complete_{false};
    std::atomic<bool> has_error_{false};
    std::string error_message_;
    size_t extracted_count_{0};
    std::chrono::steady_clock::time_point start_time_;

    void parse_file_async();
};

/**
 * @brief Factory for JSON extractors
 */
class JsonExtractorFactory {
public:
    /**
     * @brief Create JSON extractor
     * @param type Extractor type (json, jsonl, streaming_json)
     * @return std::unique_ptr<core::IExtractor> Extractor instance
     */
    static std::unique_ptr<core::IExtractor> create(const std::string& type);

    /**
     * @brief Register JSON extractors with the main factory
     */
    static void register_extractors();
};

// Implementation helpers

inline void JsonExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                     core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-json-extractor");
    logger->info("Initializing JSON extractor");

    // Get filepath
    if (config.find("filepath") == config.end()) {
        throw common::ExtractionException("JSON extractor requires 'filepath' parameter", "json");
    }
    filepath_ = std::any_cast<std::string>(config.at("filepath"));

    // Configure options
    if (config.find("root_path") != config.end()) {
        options_.root_path = std::any_cast<std::string>(config.at("root_path"));
    }
    if (config.find("flatten_nested") != config.end()) {
        options_.flatten_nested = std::any_cast<bool>(config.at("flatten_nested"));
    }
    if (config.find("array_delimiter") != config.end()) {
        options_.array_delimiter = std::any_cast<std::string>(config.at("array_delimiter"));
    }

    start_time_ = std::chrono::steady_clock::now();

    // Load JSON file
    load_file(filepath_);
}

inline void JsonExtractor::load_file(const std::string& filepath) {
    auto logger = common::Logger::get("omop-json-extractor");

    if (!std::filesystem::exists(filepath)) {
        throw common::ExtractionException(
            std::format("JSON file not found: '{}'", filepath), "json");
    }

    try {
        std::ifstream file(filepath);
        if (!file.is_open()) {
            throw common::ExtractionException(
                std::format("Failed to open JSON file: '{}'", filepath), "json");
        }

        // Parse JSON
        file >> json_data_;
        data_loaded_ = true;

        // Navigate to data array
        if (!options_.root_path.empty()) {
            current_iterator_ = navigate_to_data(json_data_, options_.root_path);

            // Find the parent array
            std::vector<std::string> path_parts;
            std::stringstream ss(options_.root_path);
            std::string part;
            while (std::getline(ss, part, '.')) {
                path_parts.push_back(part);
            }

            json* current = &json_data_;
            for (const auto& p : path_parts) {
                if (current->contains(p)) {
                    current = &(*current)[p];
                }
            }

            if (current->is_array()) {
                current_iterator_ = current->begin();
                end_iterator_ = current->end();
                total_records_ = current->size();
            } else {
                throw common::ExtractionException(
                    std::format("Path '{}' does not point to an array", options_.root_path), "json");
            }
        } else if (json_data_.is_array()) {
            current_iterator_ = json_data_.begin();
            end_iterator_ = json_data_.end();
            total_records_ = json_data_.size();
        } else {
            // Single object
            total_records_ = 1;
        }

        logger->info("Loaded JSON file with {} records", total_records_);

    } catch (const json::exception& e) {
        throw common::ExtractionException(
            std::format("Failed to parse JSON file: {}", e.what()), "json");
    }
}

inline core::RecordBatch JsonExtractor::extract_batch(size_t batch_size,
                                                     core::ProcessingContext& context) {
    core::RecordBatch batch;
    batch.reserve(batch_size);

    if (!data_loaded_) {
        return batch;
    }

    size_t count = 0;

    // Handle array of objects
    if (json_data_.is_array() || !options_.root_path.empty()) {
        while (current_iterator_ != end_iterator_ && count < batch_size) {
            try {
                if (current_iterator_->is_object()) {
                    auto record = flatten_json_object(*current_iterator_);
                    batch.addRecord(std::move(record));
                    count++;
                    extracted_count_++;
                }
            } catch (const std::exception& e) {
                context.log("warning",
                    std::format("Failed to extract record: {}", e.what()));
                context.increment_errors();
            }

            ++current_iterator_;
        }
    } else if (json_data_.is_object() && extracted_count_ == 0) {
        // Single object
        try {
            auto record = flatten_json_object(json_data_);
            batch.addRecord(std::move(record));
            extracted_count_++;
        } catch (const std::exception& e) {
            context.log("error",
                std::format("Failed to extract single object: {}", e.what()));
            context.increment_errors();
        }
    }

    return batch;
}

inline core::Record JsonExtractor::flatten_json_object(const json& obj,
                                                      const std::string& prefix,
                                                      size_t depth) {
    core::Record record;

    if (depth > options_.max_depth) {
        return record;
    }

    for (auto& [key, value] : obj.items()) {
        std::string field_name = prefix.empty() ? key : prefix + options_.array_delimiter + key;

        if (value.is_null() && options_.ignore_null) {
            continue;
        }

        if (value.is_object() && options_.flatten_nested) {
            // Recursively flatten nested object
            auto nested_record = flatten_json_object(value, field_name, depth + 1);
            for (const auto& nested_field : nested_record.getFieldNames()) {
                try {
                    auto nested_value = nested_record.getField(nested_field);
                    record.setField(nested_field, nested_value);
                } catch (const std::exception&) {
                    // Field doesn't exist, skip it
                }
            }
        } else if (value.is_array() && options_.flatten_nested) {
            // Flatten array
            for (size_t i = 0; i < value.size(); ++i) {
                std::string array_field = field_name + options_.array_delimiter + std::to_string(i);
                if (value[i].is_object()) {
                    auto nested_record = flatten_json_object(value[i], array_field, depth + 1);
                    for (const auto& nested_field : nested_record.getFieldNames()) {
                        try {
                            auto nested_value = nested_record.getField(nested_field);
                            record.setField(nested_field, nested_value);
                        } catch (const std::exception&) {
                            // Field doesn't exist, skip it
                        }
                    }
                } else {
                    record.setField(array_field, json_to_any(value[i]));
                }
            }
        } else {
            // Direct value
            record.setField(field_name, json_to_any(value));
        }
    }

    return record;
}

inline std::any JsonExtractor::json_to_any(const json& value) {
    if (value.is_null()) {
        return std::any{};
    } else if (value.is_boolean()) {
        return value.get<bool>();
    } else if (value.is_number_integer()) {
        return value.get<int64_t>();
    } else if (value.is_number_float()) {
        return value.get<double>();
    } else if (value.is_string()) {
        std::string str_val = value.get<std::string>();

        // Try to parse as date if enabled
        if (options_.parse_dates) {
            auto date = parse_date(str_val);
            if (date) {
                return *date;
            }
        }

        return str_val;
    } else {
        // Complex type, convert to string representation
        return value.dump();
    }
}

inline bool JsonExtractor::has_more_data() const {
    if (!data_loaded_) return false;

    if (json_data_.is_array() || !options_.root_path.empty()) {
        return current_iterator_ != end_iterator_;
    } else {
        return extracted_count_ == 0;
    }
}

} // namespace omop::extract