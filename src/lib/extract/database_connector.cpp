/**
 * @file database_connector.cpp
 * @brief Implementation of database connection interfaces
 * <AUTHOR> ETL Team
 * @date 2024
 */

#include "database_connector.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include <chrono>
#include <thread>
#include <queue>
#include <condition_variable>

namespace omop::extract {

// DatabaseExtractor Implementation

void DatabaseExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                 core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-database-extractor");
    logger->info("Initializing database extractor");
    
    start_time_ = std::chrono::steady_clock::now();
    
    // Extract configuration parameters
    if (config.find("table") == config.end()) {
        throw common::ConfigurationException("Database extractor requires 'table' parameter");
    }
    table_name_ = std::any_cast<std::string>(config.at("table"));
    
    // Optional parameters
    if (config.find("schema") != config.end()) {
        schema_name_ = std::any_cast<std::string>(config.at("schema"));
    }
    
    if (config.find("columns") != config.end()) {
        columns_ = std::any_cast<std::vector<std::string>>(config.at("columns"));
    }
    
    if (config.find("filter") != config.end()) {
        filter_condition_ = std::any_cast<std::string>(config.at("filter"));
    }
    
    if (config.find("order_by") != config.end()) {
        order_by_ = std::any_cast<std::string>(config.at("order_by"));
    }
    
    // Verify connection
    if (!connection_->is_connected()) {
        throw common::DatabaseException("Database connection not established", 
                                      connection_->get_database_type(), 0);
    }
    
    // Verify table exists
    if (!connection_->table_exists(table_name_, schema_name_)) {
        throw common::DatabaseException(
            std::format("Table '{}' does not exist in schema '{}'", 
                       table_name_, 
                       schema_name_.empty() ? "default" : schema_name_),
            connection_->get_database_type(), 0);
    }
    
    // Execute initial query to get result set
    std::string query = build_query();
    logger->info("Executing extraction query: {}", query);
    
    try {
        current_result_set_ = connection_->execute_query(query);
        has_more_data_ = current_result_set_ != nullptr;
    } catch (const std::exception& e) {
        throw common::ExtractionException(
            std::format("Failed to execute extraction query: {}", e.what()),
            connection_->get_database_type());
    }
}

core::RecordBatch DatabaseExtractor::extract_batch(size_t batch_size,
                                                 core::ProcessingContext& context) {
    core::RecordBatch batch;
    batch.reserve(batch_size);
    
    if (!current_result_set_ || !has_more_data_) {
        return batch;
    }
    
    size_t count = 0;
    auto logger = common::Logger::get("omop-database-extractor");
    
    try {
        while (current_result_set_->next() && count < batch_size) {
            try {
                core::Record record = current_result_set_->to_record();
                
                // Add metadata
                core::Record::RecordMetadata metadata;
                metadata.source_table = table_name_;
                if (!schema_name_.empty()) {
                    metadata.custom["source_schema"] = schema_name_;
                }
                metadata.extraction_time = std::chrono::system_clock::now();
                record.setMetadata(metadata);

                batch.addRecord(std::move(record));
                count++;
                total_extracted_++;
                
            } catch (const std::exception& e) {
                logger->warn("Failed to extract record: {}", e.what());
                context.increment_errors();
                
                // Continue processing on error by default
                // Context doesn't have should_continue_on_error method
            }
        }
        
        // Check if we've reached the end
        if (count < batch_size) {
            has_more_data_ = false;
        }
        
        batch_count_++;
        
    } catch (const std::exception& e) {
        throw common::ExtractionException(
            std::format("Batch extraction failed: {}", e.what()),
            connection_->get_database_type());
    }
    
    return batch;
}

void DatabaseExtractor::finalize(core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-database-extractor");
    
    // Clean up result set
    current_result_set_.reset();
    
    // Log final statistics
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        end_time - start_time_).count();
    
    logger->info("Database extraction completed: {} records in {} seconds ({:.2f} records/sec)",
                total_extracted_, duration, 
                duration > 0 ? static_cast<double>(total_extracted_) / duration : 0.0);
}

std::unordered_map<std::string, std::any> DatabaseExtractor::get_statistics() const {
    std::unordered_map<std::string, std::any> stats;
    
    stats["total_records"] = total_extracted_;
    stats["batch_count"] = batch_count_;
    stats["table_name"] = table_name_;
    stats["schema_name"] = schema_name_;
    stats["database_type"] = connection_->get_database_type();
    
    auto current_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        current_time - start_time_).count();
    stats["extraction_time_seconds"] = duration;
    
    if (duration > 0) {
        stats["records_per_second"] = static_cast<double>(total_extracted_) / duration;
    }
    
    return stats;
}

std::string DatabaseExtractor::build_query() const {
    std::stringstream query;
    
    // SELECT clause
    query << "SELECT ";
    if (columns_.empty()) {
        query << "*";
    } else {
        for (size_t i = 0; i < columns_.size(); ++i) {
            if (i > 0) query << ", ";
            query << columns_[i];
        }
    }
    
    // FROM clause
    query << " FROM ";
    if (!schema_name_.empty()) {
        query << schema_name_ << ".";
    }
    query << table_name_;
    
    // WHERE clause
    if (!filter_condition_.empty()) {
        query << " WHERE " << filter_condition_;
    }
    
    // ORDER BY clause
    if (!order_by_.empty()) {
        query << " ORDER BY " << order_by_;
    }
    
    return query.str();
}

std::string DatabaseExtractor::apply_filters(const std::string& base_query) const {
    if (filter_condition_.empty()) {
        return base_query;
    }
    
    // Check if query already has WHERE clause
    std::string upper_query = base_query;
    std::transform(upper_query.begin(), upper_query.end(), upper_query.begin(), ::toupper);
    
    size_t where_pos = upper_query.find(" WHERE ");
    if (where_pos != std::string::npos) {
        // Append to existing WHERE clause
        return base_query + " AND " + filter_condition_;
    } else {
        // Add new WHERE clause
        return base_query + " WHERE " + filter_condition_;
    }
}

// ConnectionPool Implementation

class ConnectionPool::Impl {
public:
    Impl(size_t min_connections, size_t max_connections,
         std::function<std::unique_ptr<IDatabaseConnection>()> factory)
        : min_connections_(min_connections),
          max_connections_(max_connections),
          connection_factory_(factory),
          total_acquisitions_(0),
          total_releases_(0),
          wait_count_(0),
          total_wait_time_(0) {
        
        // Create initial connections
        for (size_t i = 0; i < min_connections_; ++i) {
            idle_connections_.push(connection_factory_());
        }
    }
    
    ~Impl() {
        std::unique_lock<std::mutex> lock(mutex_);
        shutdown_ = true;
        cv_.notify_all();
        
        // Clear all connections
        while (!idle_connections_.empty()) {
            idle_connections_.pop();
        }
    }
    
    std::unique_ptr<IDatabaseConnection> acquire(int timeout_ms) {
        std::unique_lock<std::mutex> lock(mutex_);
        
        auto start_time = std::chrono::steady_clock::now();
        
        while (idle_connections_.empty() && 
               idle_connections_.size() + active_connections_ >= max_connections_) {
            
            if (shutdown_) {
                throw common::DatabaseException("Connection pool is shutting down", "", 0);
            }
            
            if (timeout_ms >= 0) {
                if (cv_.wait_for(lock, std::chrono::milliseconds(timeout_ms)) 
                    == std::cv_status::timeout) {
                    throw common::DatabaseException("Connection pool timeout", "", 0);
                }
            } else {
                cv_.wait(lock);
            }
            
            wait_count_++;
        }
        
        std::unique_ptr<IDatabaseConnection> connection;
        
        if (!idle_connections_.empty()) {
            connection = std::move(idle_connections_.front());
            idle_connections_.pop();
        } else if (idle_connections_.size() + active_connections_ < max_connections_) {
            connection = connection_factory_();
        }
        
        if (connection) {
            // Validate connection
            if (!connection->is_connected()) {
                connection = connection_factory_();
            }
            
            active_connections_++;
            total_acquisitions_++;
            
            auto wait_time = std::chrono::steady_clock::now() - start_time;
            total_wait_time_ += std::chrono::duration_cast<std::chrono::milliseconds>(wait_time);
        }
        
        return connection;
    }
    
    void release(std::unique_ptr<IDatabaseConnection> connection) {
        if (!connection) return;
        
        std::unique_lock<std::mutex> lock(mutex_);
        
        active_connections_--;
        total_releases_++;
        
        if (connection->is_connected() && !shutdown_) {
            idle_connections_.push(std::move(connection));
        }
        
        cv_.notify_one();
    }
    
    ConnectionPool::PoolStats get_statistics() const {
        std::unique_lock<std::mutex> lock(mutex_);
        
        PoolStats stats;
        stats.total_connections = idle_connections_.size() + active_connections_;
        stats.active_connections = active_connections_;
        stats.idle_connections = idle_connections_.size();
        stats.total_acquisitions = total_acquisitions_;
        stats.total_releases = total_releases_;
        stats.wait_count = wait_count_;
        
        if (wait_count_ > 0) {
            stats.avg_wait_time = total_wait_time_ / wait_count_.load();
        } else {
            stats.avg_wait_time = std::chrono::milliseconds(0);
        }
        
        return stats;
    }
    
    void clear_idle_connections() {
        std::unique_lock<std::mutex> lock(mutex_);
        
        while (!idle_connections_.empty()) {
            idle_connections_.pop();
        }
    }
    
    size_t validate_connections() {
        std::unique_lock<std::mutex> lock(mutex_);
        
        size_t invalid_count = 0;
        std::queue<std::unique_ptr<IDatabaseConnection>> valid_connections;
        
        while (!idle_connections_.empty()) {
            auto conn = std::move(idle_connections_.front());
            idle_connections_.pop();
            
            if (conn->is_connected()) {
                valid_connections.push(std::move(conn));
            } else {
                invalid_count++;
            }
        }
        
        idle_connections_ = std::move(valid_connections);
        
        // Ensure minimum connections
        while (idle_connections_.size() < min_connections_) {
            try {
                idle_connections_.push(connection_factory_());
            } catch (const std::exception& e) {
                // Log error but continue
                auto logger = common::Logger::get("omop-connection-pool");
                logger->error("Failed to create connection: {}", e.what());
                break;
            }
        }
        
        return invalid_count;
    }
    
private:
    size_t min_connections_;
    size_t max_connections_;
    std::function<std::unique_ptr<IDatabaseConnection>()> connection_factory_;
    
    mutable std::mutex mutex_;
    std::condition_variable cv_;
    std::queue<std::unique_ptr<IDatabaseConnection>> idle_connections_;
    size_t active_connections_{0};
    bool shutdown_{false};
    
    // Statistics
    std::atomic<size_t> total_acquisitions_;
    std::atomic<size_t> total_releases_;
    std::atomic<size_t> wait_count_;
    std::chrono::milliseconds total_wait_time_;
};

// ConnectionPool public methods

ConnectionPool::ConnectionPool(size_t min_connections,
                             size_t max_connections,
                             std::function<std::unique_ptr<IDatabaseConnection>()> connection_factory)
    : impl_(std::make_unique<Impl>(min_connections, max_connections, connection_factory)) {
}

ConnectionPool::~ConnectionPool() = default;

std::unique_ptr<IDatabaseConnection> ConnectionPool::acquire(int timeout_ms) {
    return impl_->acquire(timeout_ms);
}

void ConnectionPool::release(std::unique_ptr<IDatabaseConnection> connection) {
    impl_->release(std::move(connection));
}

ConnectionPool::PoolStats ConnectionPool::get_statistics() const {
    return impl_->get_statistics();
}

void ConnectionPool::clear_idle_connections() {
    impl_->clear_idle_connections();
}

size_t ConnectionPool::validate_connections() {
    return impl_->validate_connections();
}

// DatabaseConnectionFactory implementation

std::unique_ptr<IDatabaseConnection> DatabaseConnectionFactory::create_from_config(
    const std::unordered_map<std::string, std::any>& config) {

    // Extract type from config
    auto type_it = config.find("type");
    if (type_it == config.end()) {
        throw std::runtime_error("Database type not specified in config");
    }
    std::string type = std::any_cast<std::string>(type_it->second);

    auto connection = create(type);

    IDatabaseConnection::ConnectionParams params;

    // Extract connection parameters from config
    auto host_it = config.find("host");
    if (host_it != config.end()) {
        params.host = std::any_cast<std::string>(host_it->second);
    }

    auto port_it = config.find("port");
    if (port_it != config.end()) {
        params.port = std::any_cast<int>(port_it->second);
    }

    auto database_it = config.find("database");
    if (database_it != config.end()) {
        params.database = std::any_cast<std::string>(database_it->second);
    }

    auto username_it = config.find("username");
    if (username_it != config.end()) {
        params.username = std::any_cast<std::string>(username_it->second);
    }

    auto password_it = config.find("password");
    if (password_it != config.end()) {
        params.password = std::any_cast<std::string>(password_it->second);
    }

    auto options_it = config.find("options");
    if (options_it != config.end()) {
        params.options = std::any_cast<std::unordered_map<std::string, std::string>>(options_it->second);
    }

    connection->connect(params);

    return connection;
}

} // namespace omop::extract