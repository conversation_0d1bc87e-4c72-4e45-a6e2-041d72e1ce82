/**
 * @file odbc_connector.cpp
 * @brief ODBC database connector implementation
 * <AUTHOR> ETL Team
 * @date 2024
 */

#include "odbc_connector.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include <format>
#include <chrono>
#include <ctime>
#include <sstream>
#include <iomanip>
#include <cstring>

namespace omop::extract {

// ODBC type mapping
static const std::unordered_map<SQLSMALLINT, std::string> SQL_TYPE_NAMES = {
    {SQL_CHAR, "CHAR"},
    {SQL_VARCHAR, "VARCHAR"},
    {SQL_LONGVARCHAR, "LONGVARCHAR"},
    {SQL_WCHAR, "WCHA<PERSON>"},
    {SQL_WVARCHAR, "WVARCHAR"},
    {SQL_WLONGVARCHAR, "WLON<PERSON>VARCHAR"},
    {SQL_DECIMAL, "DECIMAL"},
    {SQL_NUMERIC, "NUMER<PERSON>"},
    {<PERSON><PERSON>_SMALLINT, "SMALLINT"},
    {SQL_INTEGER, "INTEGER"},
    {SQL_REAL, "REAL"},
    {SQL_FLOAT, "FLOAT"},
    {SQL_DOUBLE, "DOUBLE"},
    {SQL_BIT, "BIT"},
    {SQL_TINYINT, "TINYINT"},
    {SQL_BIGINT, "BIGINT"},
    {SQL_BINARY, "BINARY"},
    {SQL_VARBINARY, "VARBINARY"},
    {SQL_LONGVARBINARY, "LONGVARBINARY"},
    {SQL_TYPE_DATE, "DATE"},
    {SQL_TYPE_TIME, "TIME"},
    {SQL_TYPE_TIMESTAMP, "TIMESTAMP"}
};

// OdbcResultSet implementation

OdbcResultSet::OdbcResultSet(std::shared_ptr<OdbcStatement> statement)
    : statement_(statement) {
    
    if (!statement_) {
        throw common::DatabaseException("Invalid ODBC statement", "ODBC", 0);
    }
    
    load_metadata();
    indicators_.resize(columns_.size(), SQL_NULL_DATA);
}

OdbcResultSet::~OdbcResultSet() {
    // Statement will be freed by shared_ptr
}

void OdbcResultSet::load_metadata() {
    SQLSMALLINT column_count;
    SQLRETURN ret = SQLNumResultCols(statement_->get(), &column_count);
    
    if (!SQL_SUCCEEDED(ret)) {
        throw common::DatabaseException("Failed to get column count", "ODBC", ret);
    }
    
    columns_.reserve(column_count);
    
    for (SQLSMALLINT i = 1; i <= column_count; ++i) {
        ColumnInfo info;
        SQLCHAR column_name[256];
        SQLSMALLINT name_length;
        
        ret = SQLDescribeCol(statement_->get(), i,
                            column_name, sizeof(column_name), &name_length,
                            &info.sql_type, &info.size,
                            &info.decimal_digits, &info.nullable);
        
        if (!SQL_SUCCEEDED(ret)) {
            throw common::DatabaseException(
                std::format("Failed to describe column {}", i), "ODBC", ret);
        }
        
        info.name = std::string(reinterpret_cast<char*>(column_name), name_length);
        columns_.push_back(info);
    }
    
    metadata_loaded_ = true;
}

bool OdbcResultSet::next() {
    SQLRETURN ret = SQLFetch(statement_->get());
    
    if (ret == SQL_NO_DATA) {
        return false;
    }
    
    if (!SQL_SUCCEEDED(ret)) {
        auto errors = OdbcDatabaseConnection::get_odbc_errors(SQL_HANDLE_STMT, statement_->get());
        std::string error_msg = "Failed to fetch row";
        if (!errors.empty()) {
            error_msg += ": " + errors[0].message;
        }
        throw common::DatabaseException(error_msg, "ODBC", ret);
    }
    
    return true;
}

std::any OdbcResultSet::get_value(size_t index) const {
    if (index >= columns_.size()) {
        throw common::DatabaseException(
            std::format("Column index {} out of range (0-{})", index, columns_.size() - 1),
            "ODBC", 0);
    }
    
    return convert_value(index);
}

std::any OdbcResultSet::get_value(const std::string& column_name) const {
    size_t index = get_column_index(column_name);
    return get_value(index);
}

bool OdbcResultSet::is_null(size_t index) const {
    if (index >= columns_.size()) {
        throw common::DatabaseException(
            std::format("Column index {} out of range", index),
            "ODBC", 0);
    }
    
    SQLLEN indicator;
    char dummy[1];
    SQLRETURN ret = SQLGetData(statement_->get(), 
                              static_cast<SQLSMALLINT>(index + 1),
                              SQL_C_CHAR, dummy, 0, &indicator);
    
    if (!SQL_SUCCEEDED(ret) && ret != SQL_SUCCESS_WITH_INFO) {
        throw common::DatabaseException(
            std::format("Failed to check NULL status for column {}", index),
            "ODBC", ret);
    }
    
    return indicator == SQL_NULL_DATA;
}

bool OdbcResultSet::is_null(const std::string& column_name) const {
    size_t index = get_column_index(column_name);
    return is_null(index);
}

size_t OdbcResultSet::column_count() const {
    return columns_.size();
}

std::string OdbcResultSet::column_name(size_t index) const {
    if (index >= columns_.size()) {
        throw common::DatabaseException(
            std::format("Column index {} out of range", index),
            "ODBC", 0);
    }
    
    return columns_[index].name;
}

std::string OdbcResultSet::column_type(size_t index) const {
    if (index >= columns_.size()) {
        throw common::DatabaseException(
            std::format("Column index {} out of range", index),
            "ODBC", 0);
    }
    
    return OdbcDatabaseConnection::get_sql_type_name(columns_[index].sql_type);
}

size_t OdbcResultSet::get_column_index(const std::string& column_name) const {
    // Check cache first
    auto it = column_index_cache_.find(column_name);
    if (it != column_index_cache_.end()) {
        return it->second;
    }
    
    // Search for column
    for (size_t i = 0; i < columns_.size(); ++i) {
        if (columns_[i].name == column_name) {
            column_index_cache_[column_name] = i;
            return i;
        }
    }
    
    throw common::DatabaseException(
        std::format("Column '{}' not found", column_name),
        "ODBC", 0);
}

std::any OdbcResultSet::convert_value(size_t index) const {
    const ColumnInfo& col = columns_[index];
    SQLLEN indicator;
    
    // Check for NULL first
    if (is_null(index)) {
        return std::any{};
    }
    
    switch (col.sql_type) {
        case SQL_BIT:
        case SQL_TINYINT:
        case SQL_SMALLINT:
        case SQL_INTEGER: {
            SQLINTEGER value;
            SQLGetData(statement_->get(), static_cast<SQLSMALLINT>(index + 1),
                      SQL_C_SLONG, &value, sizeof(value), &indicator);
            return static_cast<int>(value);
        }
        
        case SQL_BIGINT: {
            SQLBIGINT value;
            SQLGetData(statement_->get(), static_cast<SQLSMALLINT>(index + 1),
                      SQL_C_SBIGINT, &value, sizeof(value), &indicator);
            return static_cast<long long>(value);
        }
        
        case SQL_REAL:
        case SQL_FLOAT:
        case SQL_DOUBLE: {
            SQLDOUBLE value;
            SQLGetData(statement_->get(), static_cast<SQLSMALLINT>(index + 1),
                      SQL_C_DOUBLE, &value, sizeof(value), &indicator);
            return static_cast<double>(value);
        }
        
        case SQL_TYPE_DATE: {
            SQL_DATE_STRUCT date;
            SQLGetData(statement_->get(), static_cast<SQLSMALLINT>(index + 1),
                      SQL_C_TYPE_DATE, &date, sizeof(date), &indicator);
            
            std::tm tm = {};
            tm.tm_year = date.year - 1900;
            tm.tm_mon = date.month - 1;
            tm.tm_mday = date.day;
            
            return std::chrono::system_clock::from_time_t(std::mktime(&tm));
        }
        
        case SQL_TYPE_TIME: {
            SQL_TIME_STRUCT time;
            SQLGetData(statement_->get(), static_cast<SQLSMALLINT>(index + 1),
                      SQL_C_TYPE_TIME, &time, sizeof(time), &indicator);
            
            std::tm tm = {};
            tm.tm_hour = time.hour;
            tm.tm_min = time.minute;
            tm.tm_sec = time.second;
            
            return std::chrono::system_clock::from_time_t(std::mktime(&tm));
        }
        
        case SQL_TYPE_TIMESTAMP: {
            SQL_TIMESTAMP_STRUCT timestamp;
            SQLGetData(statement_->get(), static_cast<SQLSMALLINT>(index + 1),
                      SQL_C_TYPE_TIMESTAMP, &timestamp, sizeof(timestamp), &indicator);
            
            std::tm tm = {};
            tm.tm_year = timestamp.year - 1900;
            tm.tm_mon = timestamp.month - 1;
            tm.tm_mday = timestamp.day;
            tm.tm_hour = timestamp.hour;
            tm.tm_min = timestamp.minute;
            tm.tm_sec = timestamp.second;
            
            auto time_point = std::chrono::system_clock::from_time_t(std::mktime(&tm));
            
            // Add fractional seconds
            if (timestamp.fraction > 0) {
                auto nanos = std::chrono::nanoseconds(timestamp.fraction);
                time_point += nanos;
            }
            
            return time_point;
        }
        
        default: {
            // Default to string for all other types
            std::vector<char> buffer(col.size + 1);
            SQLGetData(statement_->get(), static_cast<SQLSMALLINT>(index + 1),
                      SQL_C_CHAR, buffer.data(), buffer.size(), &indicator);
            return std::string(buffer.data());
        }
    }
}

// OdbcPreparedStatement implementation

OdbcPreparedStatement::OdbcPreparedStatement(std::shared_ptr<OdbcConnection> connection,
                                           const std::string& sql)
    : connection_(connection), sql_(sql) {
    
    if (!connection_) {
        throw common::DatabaseException("Invalid connection", "ODBC", 0);
    }
    
    // Allocate statement handle
    statement_ = std::make_shared<OdbcStatement>(SQL_HANDLE_STMT, connection_->get());
    
    // Prepare statement
    SQLRETURN ret = SQLPrepare(statement_->get(), 
                              reinterpret_cast<SQLCHAR*>(const_cast<char*>(sql_.c_str())),
                              SQL_NTS);
    
    if (!SQL_SUCCEEDED(ret)) {
        auto errors = OdbcDatabaseConnection::get_odbc_errors(SQL_HANDLE_STMT, statement_->get());
        std::string error_msg = "Failed to prepare statement";
        if (!errors.empty()) {
            error_msg += ": " + errors[0].message;
        }
        throw common::DatabaseException(error_msg, "ODBC", ret);
    }
}

OdbcPreparedStatement::~OdbcPreparedStatement() {
    // Statement will be freed by shared_ptr
}

void OdbcPreparedStatement::bind(size_t index, const std::any& value) {
    if (index == 0) {
        throw common::DatabaseException("Parameter index must be 1-based", "ODBC", 0);
    }
    
    ParameterBinding binding;
    binding.value = value;
    
    // Determine C and SQL types based on value type
    if (!value.has_value()) {
        binding.c_type = SQL_C_CHAR;
        binding.sql_type = SQL_VARCHAR;
        binding.indicator = SQL_NULL_DATA;
    }
    else if (value.type() == typeid(bool)) {
        binding.c_type = SQL_C_BIT;
        binding.sql_type = SQL_BIT;
        binding.buffer.resize(sizeof(unsigned char));
        *reinterpret_cast<unsigned char*>(binding.buffer.data()) = 
            std::any_cast<bool>(value) ? 1 : 0;
        binding.indicator = sizeof(unsigned char);
    }
    else if (value.type() == typeid(int)) {
        binding.c_type = SQL_C_SLONG;
        binding.sql_type = SQL_INTEGER;
        binding.buffer.resize(sizeof(SQLINTEGER));
        *reinterpret_cast<SQLINTEGER*>(binding.buffer.data()) = 
            std::any_cast<int>(value);
        binding.indicator = sizeof(SQLINTEGER);
    }
    else if (value.type() == typeid(long long)) {
        binding.c_type = SQL_C_SBIGINT;
        binding.sql_type = SQL_BIGINT;
        binding.buffer.resize(sizeof(SQLBIGINT));
        *reinterpret_cast<SQLBIGINT*>(binding.buffer.data()) = 
            std::any_cast<long long>(value);
        binding.indicator = sizeof(SQLBIGINT);
    }
    else if (value.type() == typeid(double)) {
        binding.c_type = SQL_C_DOUBLE;
        binding.sql_type = SQL_DOUBLE;
        binding.buffer.resize(sizeof(SQLDOUBLE));
        *reinterpret_cast<SQLDOUBLE*>(binding.buffer.data()) = 
            std::any_cast<double>(value);
        binding.indicator = sizeof(SQLDOUBLE);
    }
    else if (value.type() == typeid(std::string)) {
        binding.c_type = SQL_C_CHAR;
        binding.sql_type = SQL_VARCHAR;
        std::string str_val = std::any_cast<std::string>(value);
        binding.buffer.resize(str_val.length() + 1);
        std::strcpy(binding.buffer.data(), str_val.c_str());
        binding.indicator = SQL_NTS;
    }
    else if (value.type() == typeid(std::chrono::system_clock::time_point)) {
        binding.c_type = SQL_C_TYPE_TIMESTAMP;
        binding.sql_type = SQL_TYPE_TIMESTAMP;
        binding.buffer.resize(sizeof(SQL_TIMESTAMP_STRUCT));
        
        auto tp = std::any_cast<std::chrono::system_clock::time_point>(value);
        auto time_t_val = std::chrono::system_clock::to_time_t(tp);
        std::tm tm = *std::localtime(&time_t_val);
        
        auto timestamp = reinterpret_cast<SQL_TIMESTAMP_STRUCT*>(binding.buffer.data());
        timestamp->year = tm.tm_year + 1900;
        timestamp->month = tm.tm_mon + 1;
        timestamp->day = tm.tm_mday;
        timestamp->hour = tm.tm_hour;
        timestamp->minute = tm.tm_min;
        timestamp->second = tm.tm_sec;
        timestamp->fraction = 0;
        
        binding.indicator = sizeof(SQL_TIMESTAMP_STRUCT);
    }
    else {
        throw common::DatabaseException(
            "Unsupported parameter type for ODBC", "ODBC", 0);
    }
    
    parameters_[index] = std::move(binding);
    bind_parameter(index, parameters_[index]);
}

void OdbcPreparedStatement::bind_parameter(size_t index, ParameterBinding& binding) {
    SQLRETURN ret = SQLBindParameter(
        statement_->get(),
        static_cast<SQLSMALLINT>(index),
        SQL_PARAM_INPUT,
        binding.c_type,
        binding.sql_type,
        0,  // Column size - use default
        0,  // Decimal digits
        binding.indicator == SQL_NULL_DATA ? nullptr : binding.buffer.data(),
        binding.buffer.size(),
        &binding.indicator
    );
    
    if (!SQL_SUCCEEDED(ret)) {
        auto errors = OdbcDatabaseConnection::get_odbc_errors(SQL_HANDLE_STMT, statement_->get());
        std::string error_msg = std::format("Failed to bind parameter {}", index);
        if (!errors.empty()) {
            error_msg += ": " + errors[0].message;
        }
        throw common::DatabaseException(error_msg, "ODBC", ret);
    }
}

std::unique_ptr<IResultSet> OdbcPreparedStatement::execute_query() {
    // Bind all parameters
    for (auto& [index, binding] : parameters_) {
        bind_parameter(index, binding);
    }
    
    // Execute statement
    SQLRETURN ret = SQLExecute(statement_->get());
    
    if (!SQL_SUCCEEDED(ret)) {
        auto errors = OdbcDatabaseConnection::get_odbc_errors(SQL_HANDLE_STMT, statement_->get());
        std::string error_msg = "Query execution failed";
        if (!errors.empty()) {
            error_msg += ": " + errors[0].message;
        }
        throw common::DatabaseException(error_msg, "ODBC", ret);
    }
    
    return std::make_unique<OdbcResultSet>(statement_);
}

size_t OdbcPreparedStatement::execute_update() {
    // Bind all parameters
    for (auto& [index, binding] : parameters_) {
        bind_parameter(index, binding);
    }
    
    // Execute statement
    SQLRETURN ret = SQLExecute(statement_->get());
    
    if (!SQL_SUCCEEDED(ret)) {
        auto errors = OdbcDatabaseConnection::get_odbc_errors(SQL_HANDLE_STMT, statement_->get());
        std::string error_msg = "Update execution failed";
        if (!errors.empty()) {
            error_msg += ": " + errors[0].message;
        }
        throw common::DatabaseException(error_msg, "ODBC", ret);
    }
    
    // Get row count
    SQLLEN row_count;
    ret = SQLRowCount(statement_->get(), &row_count);
    
    if (!SQL_SUCCEEDED(ret)) {
        return 0;
    }
    
    return static_cast<size_t>(row_count);
}

void OdbcPreparedStatement::clear_parameters() {
    parameters_.clear();
    
    // Reset all parameter bindings
    SQLRETURN ret = SQLFreeStmt(statement_->get(), SQL_RESET_PARAMS);
    
    if (!SQL_SUCCEEDED(ret)) {
        auto logger = common::Logger::get("omop-odbc");
        logger->warn("Failed to reset parameters");
    }
}

// OdbcDatabaseConnection implementation

OdbcDatabaseConnection::OdbcDatabaseConnection() {
    // Allocate environment handle
    environment_ = std::make_shared<OdbcEnvironment>(SQL_HANDLE_ENV, SQL_NULL_HANDLE);
    
    // Set ODBC version
    SQLRETURN ret = SQLSetEnvAttr(environment_->get(), SQL_ATTR_ODBC_VERSION,
                                  reinterpret_cast<SQLPOINTER>(SQL_OV_ODBC3), 0);
    
    if (!SQL_SUCCEEDED(ret)) {
        throw common::DatabaseException("Failed to set ODBC version", "ODBC", ret);
    }
}

OdbcDatabaseConnection::~OdbcDatabaseConnection() {
    disconnect();
}

void OdbcDatabaseConnection::connect(const ConnectionParams& params) {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (connected_) {
        disconnect();
    }
    
    // Allocate connection handle
    connection_ = std::make_shared<OdbcConnection>(SQL_HANDLE_DBC, environment_->get());
    
    // Build connection string
    std::string conn_str = build_connection_string(params);
    
    // Connect
    SQLCHAR out_conn_str[1024];
    SQLSMALLINT out_conn_str_len;
    
    SQLRETURN ret = SQLDriverConnect(
        connection_->get(),
        SQL_NULL_HANDLE,
        reinterpret_cast<SQLCHAR*>(const_cast<char*>(conn_str.c_str())),
        SQL_NTS,
        out_conn_str,
        sizeof(out_conn_str),
        &out_conn_str_len,
        SQL_DRIVER_NOPROMPT
    );
    
    if (!SQL_SUCCEEDED(ret)) {
        auto errors = get_odbc_errors(SQL_HANDLE_DBC, connection_->get());
        std::string error_msg = "Failed to connect to database";
        if (!errors.empty()) {
            error_msg += ": " + errors[0].message;
        }
        throw common::DatabaseException(error_msg, "ODBC", ret);
    }
    
    connected_ = true;
    database_name_ = params.database;
    
    // Get driver name
    SQLCHAR driver_name[256];
    SQLSMALLINT driver_name_len;
    SQLGetInfo(connection_->get(), SQL_DRIVER_NAME, driver_name, 
              sizeof(driver_name), &driver_name_len);
    driver_name_ = std::string(reinterpret_cast<char*>(driver_name));
    
    auto logger = common::Logger::get("omop-odbc");
    logger->info("Connected to database '{}' using driver '{}'", database_name_, driver_name_);
}

void OdbcDatabaseConnection::disconnect() {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (connected_) {
        // Rollback any pending transaction
        if (in_transaction_) {
            try {
                rollback();
            } catch (...) {
                // Ignore errors during disconnect
            }
        }
        
        SQLDisconnect(connection_->get());
        connection_.reset();
        connected_ = false;
        
        auto logger = common::Logger::get("omop-odbc");
        logger->info("Disconnected from database '{}'", database_name_);
    }
}

bool OdbcDatabaseConnection::is_connected() const {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (!connected_ || !connection_) {
        return false;
    }
    
    // Check connection status
    SQLINTEGER dead;
    SQLRETURN ret = SQLGetConnectAttr(connection_->get(), SQL_ATTR_CONNECTION_DEAD,
                                     &dead, sizeof(dead), nullptr);
    
    return SQL_SUCCEEDED(ret) && dead == SQL_CD_FALSE;
}

std::unique_ptr<IResultSet> OdbcDatabaseConnection::execute_query(const std::string& sql) {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (!connected_) {
        throw common::DatabaseException("Not connected to database", "ODBC", 0);
    }
    
    auto statement = std::make_shared<OdbcStatement>(SQL_HANDLE_STMT, connection_->get());
    
    SQLRETURN ret = SQLExecDirect(statement->get(),
                                 reinterpret_cast<SQLCHAR*>(const_cast<char*>(sql.c_str())),
                                 SQL_NTS);
    
    check_error(ret, "execute_query", SQL_HANDLE_STMT, statement->get());
    
    return std::make_unique<OdbcResultSet>(statement);
}

size_t OdbcDatabaseConnection::execute_update(const std::string& sql) {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (!connected_) {
        throw common::DatabaseException("Not connected to database", "ODBC", 0);
    }
    
    auto statement = std::make_shared<OdbcStatement>(SQL_HANDLE_STMT, connection_->get());
    
    SQLRETURN ret = SQLExecDirect(statement->get(),
                                 reinterpret_cast<SQLCHAR*>(const_cast<char*>(sql.c_str())),
                                 SQL_NTS);
    
    check_error(ret, "execute_update", SQL_HANDLE_STMT, statement->get());
    
    // Get row count
    SQLLEN row_count;
    ret = SQLRowCount(statement->get(), &row_count);
    
    if (!SQL_SUCCEEDED(ret)) {
        return 0;
    }
    
    return static_cast<size_t>(row_count);
}

std::unique_ptr<IPreparedStatement> OdbcDatabaseConnection::prepare_statement(const std::string& sql) {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (!connected_) {
        throw common::DatabaseException("Not connected to database", "ODBC", 0);
    }
    
    return std::make_unique<OdbcPreparedStatement>(connection_, sql);
}

void OdbcDatabaseConnection::begin_transaction() {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (in_transaction_) {
        throw common::DatabaseException("Transaction already in progress", "ODBC", 0);
    }
    
    // Disable auto-commit
    SQLRETURN ret = SQLSetConnectAttr(connection_->get(), SQL_ATTR_AUTOCOMMIT,
                                     reinterpret_cast<SQLPOINTER>(SQL_AUTOCOMMIT_OFF), 0);
    
    check_error(ret, "begin_transaction", SQL_HANDLE_DBC, connection_->get());
    
    in_transaction_ = true;
}

void OdbcDatabaseConnection::commit() {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (!in_transaction_) {
        throw common::DatabaseException("No transaction in progress", "ODBC", 0);
    }
    
    SQLRETURN ret = SQLEndTran(SQL_HANDLE_DBC, connection_->get(), SQL_COMMIT);
    
    check_error(ret, "commit", SQL_HANDLE_DBC, connection_->get());
    
    // Re-enable auto-commit
    SQLSetConnectAttr(connection_->get(), SQL_ATTR_AUTOCOMMIT,
                     reinterpret_cast<SQLPOINTER>(SQL_AUTOCOMMIT_ON), 0);
    
    in_transaction_ = false;
}

void OdbcDatabaseConnection::rollback() {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (!in_transaction_) {
        throw common::DatabaseException("No transaction in progress", "ODBC", 0);
    }
    
    SQLRETURN ret = SQLEndTran(SQL_HANDLE_DBC, connection_->get(), SQL_ROLLBACK);
    
    check_error(ret, "rollback", SQL_HANDLE_DBC, connection_->get());
    
    // Re-enable auto-commit
    SQLSetConnectAttr(connection_->get(), SQL_ATTR_AUTOCOMMIT,
                     reinterpret_cast<SQLPOINTER>(SQL_AUTOCOMMIT_ON), 0);
    
    in_transaction_ = false;
}

std::string OdbcDatabaseConnection::get_database_type() const {
    if (!driver_name_.empty()) {
        // Identify common database types from driver name
        std::string lower_driver = driver_name_;
        std::transform(lower_driver.begin(), lower_driver.end(), 
                      lower_driver.begin(), ::tolower);
        
        if (lower_driver.find("mysql") != std::string::npos) return "MySQL";
        if (lower_driver.find("postgres") != std::string::npos) return "PostgreSQL";
        if (lower_driver.find("sqlserver") != std::string::npos) return "SQL Server";
        if (lower_driver.find("oracle") != std::string::npos) return "Oracle";
        if (lower_driver.find("sqlite") != std::string::npos) return "SQLite";
    }
    
    return "ODBC";
}

std::string OdbcDatabaseConnection::get_version() const {
    if (!connected_) {
        return "Not connected";
    }
    
    SQLCHAR version[256];
    SQLSMALLINT version_len;
    
    SQLRETURN ret = SQLGetInfo(connection_->get(), SQL_DBMS_VER, version,
                              sizeof(version), &version_len);
    
    if (SQL_SUCCEEDED(ret)) {
        return std::string(reinterpret_cast<char*>(version));
    }
    
    return "Unknown";
}

void OdbcDatabaseConnection::set_query_timeout(int seconds) {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (connected_) {
        SQLRETURN ret = SQLSetConnectAttr(connection_->get(), SQL_ATTR_QUERY_TIMEOUT,
                                         reinterpret_cast<SQLPOINTER>(static_cast<SQLULEN>(seconds)), 0);
        
        if (!SQL_SUCCEEDED(ret)) {
            auto logger = common::Logger::get("omop-odbc");
            logger->warn("Failed to set query timeout");
        }
    }
}

bool OdbcDatabaseConnection::table_exists(const std::string& table_name,
                                        const std::string& schema) const {
    if (!connected_) {
        return false;
    }
    
    auto statement = std::make_shared<OdbcStatement>(SQL_HANDLE_STMT, connection_->get());
    
    SQLRETURN ret = SQLTables(statement->get(),
                            nullptr, 0,  // Catalog
                            schema.empty() ? nullptr : reinterpret_cast<SQLCHAR*>(const_cast<char*>(schema.c_str())),
                            schema.empty() ? 0 : SQL_NTS,
                            reinterpret_cast<SQLCHAR*>(const_cast<char*>(table_name.c_str())), SQL_NTS,
                            reinterpret_cast<SQLCHAR*>(const_cast<char*>("TABLE")), SQL_NTS);
    
    if (!SQL_SUCCEEDED(ret)) {
        return false;
    }
    
    // Check if any rows returned
    ret = SQLFetch(statement->get());
    return ret == SQL_SUCCESS || ret == SQL_SUCCESS_WITH_INFO;
}

std::vector<OdbcError> OdbcDatabaseConnection::get_odbc_errors(SQLSMALLINT handle_type,
                                                              SQLHANDLE handle) {
    std::vector<OdbcError> errors;
    SQLSMALLINT rec_num = 1;
    
    while (true) {
        OdbcError error;
        SQLCHAR state[6];
        SQLCHAR message[1024];
        SQLSMALLINT message_len;
        
        SQLRETURN ret = SQLGetDiagRec(handle_type, handle, rec_num,
                                     state, &error.native_error,
                                     message, sizeof(message), &message_len);
        
        if (ret == SQL_NO_DATA) {
            break;
        }
        
        if (SQL_SUCCEEDED(ret)) {
            error.state = std::string(reinterpret_cast<char*>(state));
            error.message = std::string(reinterpret_cast<char*>(message));
            errors.push_back(error);
        }
        
        rec_num++;
    }
    
    return errors;
}

std::string OdbcDatabaseConnection::build_connection_string(const ConnectionParams& params) const {
    // Check if DSN is provided
    auto dsn_it = params.options.find("DSN");
    if (dsn_it != params.options.end()) {
        return build_dsn_string(params);
    }
    
    // Build driver-based connection string
    std::stringstream conn_str;
    
    // Driver
    auto driver_it = params.options.find("Driver");
    if (driver_it != params.options.end()) {
        conn_str << "Driver={" << driver_it->second << "};";
    } else {
        throw common::DatabaseException("No Driver or DSN specified", "ODBC", 0);
    }
    
    // Server/Host
    if (!params.host.empty()) {
        conn_str << "Server=" << params.host << ";";
    }
    
    // Port
    if (params.port > 0) {
        conn_str << "Port=" << params.port << ";";
    }
    
    // Database
    if (!params.database.empty()) {
        conn_str << "Database=" << params.database << ";";
    }
    
    // Username
    if (!params.username.empty()) {
        conn_str << "UID=" << params.username << ";";
    }
    
    // Password
    if (!params.password.empty()) {
        conn_str << "PWD=" << params.password << ";";
    }
    
    // Additional options
    for (const auto& [key, value] : params.options) {
        if (key != "Driver") {  // Skip Driver as it's already added
            conn_str << key << "=" << value << ";";
        }
    }
    
    return conn_str.str();
}

std::string OdbcDatabaseConnection::build_dsn_string(const ConnectionParams& params) const {
    std::stringstream conn_str;
    
    // DSN
    auto dsn_it = params.options.find("DSN");
    conn_str << "DSN=" << dsn_it->second << ";";
    
    // Username
    if (!params.username.empty()) {
        conn_str << "UID=" << params.username << ";";
    }
    
    // Password
    if (!params.password.empty()) {
        conn_str << "PWD=" << params.password << ";";
    }
    
    return conn_str.str();
}

void OdbcDatabaseConnection::check_error(SQLRETURN ret, const std::string& operation,
                                       SQLSMALLINT handle_type, SQLHANDLE handle) const {
    if (!SQL_SUCCEEDED(ret)) {
        auto errors = get_odbc_errors(handle_type, handle);
        std::string error_msg = std::format("{} failed", operation);
        
        if (!errors.empty()) {
            error_msg += ": " + errors[0].message;
            error_msg += " (SQLSTATE: " + errors[0].state + ")";
        }
        
        throw common::DatabaseException(error_msg, "ODBC", ret);
    }
}

std::string OdbcDatabaseConnection::get_sql_type_name(SQLSMALLINT sql_type) {
    auto it = SQL_TYPE_NAMES.find(sql_type);
    if (it != SQL_TYPE_NAMES.end()) {
        return it->second;
    }
    return std::format("SQL_TYPE_{}", sql_type);
}

// OdbcExtractor implementation

std::string OdbcExtractor::build_query() const {
    // Use parent implementation and add ODBC-specific optimizations
    std::string query = DatabaseExtractor::build_query();
    
    // Add ODBC-specific optimizations based on database type
    auto db_type = connection_->get_database_type();
    
    if (db_type == "SQL Server") {
        // Add NOLOCK hint for read-only queries
        size_t from_pos = query.find(" FROM ");
        if (from_pos != std::string::npos) {
            size_t table_end = query.find(" ", from_pos + 6);
            if (table_end != std::string::npos) {
                query.insert(table_end, " WITH (NOLOCK)");
            }
        }
    }
    
    return query;
}

// OdbcDriverManager implementation

std::vector<OdbcDriverManager::DriverInfo> OdbcDriverManager::get_available_drivers() {
    std::vector<DriverInfo> drivers;
    
    OdbcEnvironment env(SQL_HANDLE_ENV, SQL_NULL_HANDLE);
    SQLSetEnvAttr(env.get(), SQL_ATTR_ODBC_VERSION,
                 reinterpret_cast<SQLPOINTER>(SQL_OV_ODBC3), 0);
    
    SQLCHAR driver[256];
    SQLCHAR attrs[256];
    SQLSMALLINT driver_len, attrs_len;
    SQLUSMALLINT direction = SQL_FETCH_FIRST;
    
    while (SQL_SUCCEEDED(SQLDrivers(env.get(), direction, driver, sizeof(driver),
                                   &driver_len, attrs, sizeof(attrs), &attrs_len))) {
        DriverInfo info;
        info.name = std::string(reinterpret_cast<char*>(driver));
        
        // Parse attributes (key=value pairs separated by null)
        std::string attr_str(reinterpret_cast<char*>(attrs), attrs_len);
        size_t pos = 0;
        while (pos < attr_str.length()) {
            size_t end = attr_str.find('\0', pos);
            if (end == std::string::npos) end = attr_str.length();
            
            std::string pair = attr_str.substr(pos, end - pos);
            size_t eq_pos = pair.find('=');
            if (eq_pos != std::string::npos) {
                info.attributes[pair.substr(0, eq_pos)] = pair.substr(eq_pos + 1);
            }
            
            pos = end + 1;
        }
        
        drivers.push_back(info);
        direction = SQL_FETCH_NEXT;
    }
    
    return drivers;
}

std::vector<OdbcDriverManager::DataSourceInfo> OdbcDriverManager::get_data_sources() {
    std::vector<DataSourceInfo> sources;
    
    OdbcEnvironment env(SQL_HANDLE_ENV, SQL_NULL_HANDLE);
    SQLSetEnvAttr(env.get(), SQL_ATTR_ODBC_VERSION,
                 reinterpret_cast<SQLPOINTER>(SQL_OV_ODBC3), 0);
    
    SQLCHAR dsn[256];
    SQLCHAR desc[256];
    SQLSMALLINT dsn_len, desc_len;
    SQLUSMALLINT direction = SQL_FETCH_FIRST;
    
    while (SQL_SUCCEEDED(SQLDataSources(env.get(), direction, dsn, sizeof(dsn),
                                       &dsn_len, desc, sizeof(desc), &desc_len))) {
        DataSourceInfo info;
        info.name = std::string(reinterpret_cast<char*>(dsn));
        info.description = std::string(reinterpret_cast<char*>(desc));
        
        sources.push_back(info);
        direction = SQL_FETCH_NEXT;
    }
    
    return sources;
}

std::pair<bool, std::string> OdbcDriverManager::test_connection(const std::string& connection_string) {
    try {
        OdbcEnvironment env(SQL_HANDLE_ENV, SQL_NULL_HANDLE);
        SQLSetEnvAttr(env.get(), SQL_ATTR_ODBC_VERSION,
                     reinterpret_cast<SQLPOINTER>(SQL_OV_ODBC3), 0);
        
        OdbcConnection conn(SQL_HANDLE_DBC, env.get());
        
        SQLCHAR out_conn_str[1024];
        SQLSMALLINT out_conn_str_len;
        
        SQLRETURN ret = SQLDriverConnect(
            conn.get(),
            SQL_NULL_HANDLE,
            reinterpret_cast<SQLCHAR*>(const_cast<char*>(connection_string.c_str())),
            SQL_NTS,
            out_conn_str,
            sizeof(out_conn_str),
            &out_conn_str_len,
            SQL_DRIVER_NOPROMPT
        );
        
        if (SQL_SUCCEEDED(ret)) {
            SQLDisconnect(conn.get());
            return {true, "Connection successful"};
        } else {
            auto errors = OdbcDatabaseConnection::get_odbc_errors(SQL_HANDLE_DBC, conn.get());
            std::string error_msg = "Connection failed";
            if (!errors.empty()) {
                error_msg += ": " + errors[0].message;
            }
            return {false, error_msg};
        }
        
    } catch (const std::exception& e) {
        return {false, std::string("Exception: ") + e.what()};
    }
}

} // namespace omop::extract