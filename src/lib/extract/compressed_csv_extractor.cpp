/**
 * @file compressed_csv_extractor.cpp
 * @brief Implementation of compressed CSV extractor
 * <AUTHOR> ETL Team
 * @date 2024
 */

#include "extract/csv_extractor.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include "omop/config.h"
#include <zlib.h>
#if OMOP_HAVE_LIBARCHIVE
#include <archive.h>
#include <archive_entry.h>
#endif
#include <fstream>
#include <filesystem>
#include <cstring>

namespace omop::extract {

namespace {
    /**
     * @brief Check file signature for compression type
     * @param filepath File path
     * @return CompressionFormat Detected format
     */
    CompressedCsvExtractor::CompressionFormat detect_format_by_signature(const std::string& filepath) {
        std::ifstream file(filepath, std::ios::binary);
        if (!file.is_open()) {
            return CompressedCsvExtractor::CompressionFormat::None;
        }
        
        // Read first few bytes for magic numbers
        unsigned char header[8] = {0};
        file.read(reinterpret_cast<char*>(header), sizeof(header));
        file.close();
        
        // Check magic numbers
        if (header[0] == 0x1f && header[1] == 0x8b) {
            // Gzip magic number
            return CompressedCsvExtractor::CompressionFormat::Gzip;
        }
        else if (header[0] == 'P' && header[1] == 'K' && 
                 header[2] == 0x03 && header[3] == 0x04) {
            // ZIP magic number
            return CompressedCsvExtractor::CompressionFormat::Zip;
        }
        else if (header[0] == 'B' && header[1] == 'Z' && header[2] == 'h') {
            // Bzip2 magic number
            return CompressedCsvExtractor::CompressionFormat::Bzip2;
        }
        else if (header[0] == 0xfd && header[1] == '7' && header[2] == 'z' &&
                 header[3] == 'X' && header[4] == 'Z' && header[5] == 0x00) {
            // XZ magic number
            return CompressedCsvExtractor::CompressionFormat::Xz;
        }
        
        return CompressedCsvExtractor::CompressionFormat::None;
    }
    
    /**
     * @brief Decompress gzip file
     * @param input_path Input file path
     * @param output_path Output file path
     * @return bool Success
     */
    bool decompress_gzip(const std::string& input_path, const std::string& output_path) {
        gzFile gz_file = gzopen(input_path.c_str(), "rb");
        if (!gz_file) {
            return false;
        }
        
        std::ofstream output(output_path, std::ios::binary);
        if (!output.is_open()) {
            gzclose(gz_file);
            return false;
        }
        
        const size_t buffer_size = 8192;
        char buffer[buffer_size];
        int bytes_read;
        
        while ((bytes_read = gzread(gz_file, buffer, buffer_size)) > 0) {
            output.write(buffer, bytes_read);
        }
        
        output.close();
        gzclose(gz_file);
        
        return bytes_read >= 0;
    }
    
    /**
     * @brief Decompress file using libarchive
     * @param input_path Input file path
     * @param output_path Output file path
     * @param format Expected format
     * @return bool Success
     */
    bool decompress_with_libarchive(const std::string& input_path,
                                   const std::string& output_path,
                                   CompressedCsvExtractor::CompressionFormat format) {
#if OMOP_HAVE_LIBARCHIVE
        struct archive* a = archive_read_new();
        struct archive* ext = archive_write_disk_new();
        
        // Enable appropriate format support
        switch (format) {
            case CompressedCsvExtractor::CompressionFormat::Zip:
                archive_read_support_format_zip(a);
                break;
            case CompressedCsvExtractor::CompressionFormat::Bzip2:
                archive_read_support_filter_bzip2(a);
                archive_read_support_format_raw(a);
                break;
            case CompressedCsvExtractor::CompressionFormat::Xz:
                archive_read_support_filter_xz(a);
                archive_read_support_format_raw(a);
                break;
            default:
                archive_read_support_filter_all(a);
                archive_read_support_format_all(a);
                break;
        }
        
        archive_write_disk_set_options(ext, ARCHIVE_EXTRACT_TIME);
        
        if (archive_read_open_filename(a, input_path.c_str(), 10240) != ARCHIVE_OK) {
            archive_read_free(a);
            archive_write_free(ext);
            return false;
        }
        
        struct archive_entry* entry;
        bool found_csv = false;
        
        while (archive_read_next_header(a, &entry) == ARCHIVE_OK) {
            std::string filename = archive_entry_pathname(entry);
            
            // Look for CSV files
            if (filename.find(".csv") != std::string::npos ||
                filename.find(".CSV") != std::string::npos) {
                
                // Extract to temporary location
                archive_entry_set_pathname(entry, output_path.c_str());
                
                if (archive_write_header(ext, entry) == ARCHIVE_OK) {
                    const void* buff;
                    size_t size;
                    int64_t offset;
                    
                    while (archive_read_data_block(a, &buff, &size, &offset) == ARCHIVE_OK) {
                        archive_write_data_block(ext, buff, size, offset);
                    }
                    
                    found_csv = true;
                    break;  // Extract only the first CSV file
                }
            }
        }
        
        archive_read_close(a);
        archive_read_free(a);
        archive_write_close(ext);
        archive_write_free(ext);

        return found_csv;
#else
        // LibArchive not available
        return false;
#endif
    }
}

// CompressedCsvExtractor implementation

void CompressedCsvExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                       core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-compressed-csv");
    
    // Get filepath
    if (config.find("filepath") == config.end()) {
        throw common::ConfigurationException("Compressed CSV extractor requires 'filepath' parameter");
    }
    std::string compressed_filepath = std::any_cast<std::string>(config.at("filepath"));
    
    // Detect compression format
    if (config.find("compression_format") != config.end()) {
        std::string format_str = std::any_cast<std::string>(config.at("compression_format"));
        compression_format_ = string_to_format(format_str);
    } else {
        compression_format_ = detect_compression(compressed_filepath);
    }
    
    if (compression_format_ == CompressionFormat::None) {
        logger->warn("No compression detected, treating as regular CSV");
        CsvExtractor::initialize(config, context);
        return;
    }
    
    logger->info("Detected {} compression format", format_to_string(compression_format_));
    
    // Check cleanup option
    if (config.find("cleanup_temp_file") != config.end()) {
        cleanup_temp_file_ = std::any_cast<bool>(config.at("cleanup_temp_file"));
    }
    
    // Create temporary file path
    std::filesystem::path temp_dir = std::filesystem::temp_directory_path();
    std::filesystem::path temp_file = temp_dir / 
        (std::filesystem::path(compressed_filepath).stem().string() + "_decompressed.csv");
    temp_file_path_ = temp_file.string();
    
    // Decompress file
    logger->info("Decompressing file to: {}", temp_file_path_);
    
    std::string decompressed_path = decompress_file(compressed_filepath, compression_format_);
    if (decompressed_path.empty()) {
        throw common::ExtractionException(
            std::format("Failed to decompress file: '{}'", compressed_filepath), "compressed_csv");
    }
    
    // Update config with decompressed file path
    auto modified_config = config;
    modified_config["filepath"] = decompressed_path;
    
    // Initialize parent CSV extractor with decompressed file
    CsvExtractor::initialize(modified_config, context);
}

CompressedCsvExtractor::CompressionFormat CompressedCsvExtractor::detect_compression(
    const std::string& filepath) {
    
    // First try detection by file extension
    std::filesystem::path path(filepath);
    std::string extension = path.extension().string();
    std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);
    
    if (extension == ".gz" || filepath.find(".csv.gz") != std::string::npos) {
        return CompressionFormat::Gzip;
    }
    else if (extension == ".zip") {
        return CompressionFormat::Zip;
    }
    else if (extension == ".bz2" || filepath.find(".csv.bz2") != std::string::npos) {
        return CompressionFormat::Bzip2;
    }
    else if (extension == ".xz" || filepath.find(".csv.xz") != std::string::npos) {
        return CompressionFormat::Xz;
    }
    
    // If extension is not conclusive, check file signature
    return detect_format_by_signature(filepath);
}

std::string CompressedCsvExtractor::decompress_file(const std::string& filepath,
                                                   CompressionFormat format) {
    auto logger = common::Logger::get("omop-compressed-csv");
    
    if (format == CompressionFormat::None) {
        return filepath;  // No decompression needed
    }
    
    // Create output path
    std::filesystem::path temp_dir = std::filesystem::temp_directory_path();
    std::filesystem::path input_path(filepath);
    std::string base_name = input_path.stem().string();
    
    // Remove compression extension if present
    if (base_name.find(".csv") == std::string::npos) {
        base_name += ".csv";
    }
    
    std::filesystem::path output_path = temp_dir / base_name;
    temp_file_path_ = output_path.string();
    
    bool success = false;
    
    switch (format) {
        case CompressionFormat::Gzip:
            success = decompress_gzip(filepath, temp_file_path_);
            break;
            
        case CompressionFormat::Zip:
        case CompressionFormat::Bzip2:
        case CompressionFormat::Xz:
            success = decompress_with_libarchive(filepath, temp_file_path_, format);
            break;
            
        default:
            throw common::ExtractionException(
                std::format("Unsupported compression format: {}", 
                           static_cast<int>(format)), "compressed_csv");
    }
    
    if (!success) {
        throw common::ExtractionException(
            std::format("Failed to decompress file: '{}'", filepath), "compressed_csv");
    }
    
    logger->info("Successfully decompressed to: {}", temp_file_path_);
    return temp_file_path_;
}

void CompressedCsvExtractor::finalize(core::ProcessingContext& context) {
    // First finalize parent
    CsvExtractor::finalize(context);
    
    // Clean up temporary file if requested
    if (cleanup_temp_file_ && !temp_file_path_.empty()) {
        auto logger = common::Logger::get("omop-compressed-csv");
        
        try {
            if (std::filesystem::exists(temp_file_path_)) {
                std::filesystem::remove(temp_file_path_);
                logger->info("Cleaned up temporary file: {}", temp_file_path_);
            }
        } catch (const std::exception& e) {
            logger->warn("Failed to clean up temporary file: {}", e.what());
        }
    }
}

std::string CompressedCsvExtractor::format_to_string(CompressionFormat format) {
    switch (format) {
        case CompressionFormat::None: return "None";
        case CompressionFormat::Gzip: return "Gzip";
        case CompressionFormat::Zip: return "Zip";
        case CompressionFormat::Bzip2: return "Bzip2";
        case CompressionFormat::Xz: return "Xz";
        default: return "Unknown";
    }
}

CompressedCsvExtractor::CompressionFormat CompressedCsvExtractor::string_to_format(
    const std::string& format_str) {
    
    std::string lower_format = format_str;
    std::transform(lower_format.begin(), lower_format.end(), lower_format.begin(), ::tolower);
    
    if (lower_format == "gzip" || lower_format == "gz") {
        return CompressionFormat::Gzip;
    }
    else if (lower_format == "zip") {
        return CompressionFormat::Zip;
    }
    else if (lower_format == "bzip2" || lower_format == "bz2") {
        return CompressionFormat::Bzip2;
    }
    else if (lower_format == "xz") {
        return CompressionFormat::Xz;
    }
    else {
        return CompressionFormat::None;
    }
}

} // namespace omop::extract