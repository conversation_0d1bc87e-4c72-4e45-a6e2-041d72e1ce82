/**
 * @file csv_extractor.cpp
 * @brief Implementation of CSV data extractor
 * <AUTHOR> ETL Team
 * @date 2024
 */

#include "csv_extractor.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include <algorithm>
#include <cctype>
#include <iomanip>
#include <sstream>

namespace omop::extract {

// CsvFieldParser implementation

std::any CsvFieldParser::convert_field(const std::string& field, const std::string& type_hint) {
    // Handle null values
    if (field.empty() || field == options_.null_string) {
        return std::any{};
    }
    
    // If no type hint, try to infer type
    if (type_hint.empty()) {
        // Try boolean
        std::string upper_field = field;
        std::transform(upper_field.begin(), upper_field.end(), upper_field.begin(), ::toupper);
        
        if (upper_field == options_.true_string || upper_field == "1" || 
            upper_field == "YES" || upper_field == "Y" || upper_field == "T") {
            return true;
        }
        if (upper_field == options_.false_string || upper_field == "0" || 
            upper_field == "NO" || upper_field == "N" || upper_field == "F") {
            return false;
        }
        
        // Try integer
        try {
            size_t pos;
            long long int_val = std::stoll(field, &pos);
            if (pos == field.length()) {
                return int_val;
            }
        } catch (...) {}
        
        // Try double
        try {
            size_t pos;
            double double_val = std::stod(field, &pos);
            if (pos == field.length()) {
                return double_val;
            }
        } catch (...) {}
        
        // Try date/datetime
        auto datetime = parse_datetime(field, options_.datetime_format);
        if (datetime.time_since_epoch().count() > 0) {
            return datetime;
        }
        
        auto date = parse_datetime(field, options_.date_format);
        if (date.time_since_epoch().count() > 0) {
            return date;
        }
        
        // Default to string
        return field;
    }
    
    // Convert based on type hint
    if (type_hint == "boolean" || type_hint == "bool") {
        std::string upper_field = field;
        std::transform(upper_field.begin(), upper_field.end(), upper_field.begin(), ::toupper);
        return upper_field == options_.true_string || upper_field == "1" || 
               upper_field == "TRUE" || upper_field == "YES";
    }
    else if (type_hint == "integer" || type_hint == "int" || type_hint == "bigint") {
        return std::stoll(field);
    }
    else if (type_hint == "double" || type_hint == "float" || type_hint == "decimal") {
        return std::stod(field);
    }
    else if (type_hint == "date") {
        return parse_datetime(field, options_.date_format);
    }
    else if (type_hint == "datetime" || type_hint == "timestamp") {
        return parse_datetime(field, options_.datetime_format);
    }
    else {
        return field;
    }
}

std::chrono::system_clock::time_point CsvFieldParser::parse_datetime(
    const std::string& value, const std::string& format) const {
    
    std::tm tm = {};
    std::stringstream ss(value);
    ss >> std::get_time(&tm, format.c_str());
    
    if (ss.fail()) {
        return std::chrono::system_clock::time_point{};
    }
    
    return std::chrono::system_clock::from_time_t(std::mktime(&tm));
}

// CsvExtractor implementation

void CsvExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                            core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-csv-extractor");
    logger->info("Initializing CSV extractor");
    
    // Get filepath
    if (config.find("filepath") == config.end()) {
        throw common::ConfigurationException("CSV extractor requires 'filepath' parameter");
    }
    filepath_ = std::any_cast<std::string>(config.at("filepath"));
    
    // Configure options
    if (config.find("delimiter") != config.end()) {
        options_.delimiter = std::any_cast<char>(config.at("delimiter"));
    }
    if (config.find("quote_char") != config.end()) {
        options_.quote_char = std::any_cast<char>(config.at("quote_char"));
    }
    if (config.find("escape_char") != config.end()) {
        options_.escape_char = std::any_cast<char>(config.at("escape_char"));
    }
    if (config.find("has_header") != config.end()) {
        options_.has_header = std::any_cast<bool>(config.at("has_header"));
    }
    if (config.find("encoding") != config.end()) {
        options_.encoding = std::any_cast<std::string>(config.at("encoding"));
    }
    if (config.find("skip_lines") != config.end()) {
        options_.skip_lines = std::any_cast<size_t>(config.at("skip_lines"));
    }
    if (config.find("max_lines") != config.end()) {
        options_.max_lines = std::any_cast<size_t>(config.at("max_lines"));
    }
    
    // Initialize parser
    parser_ = CsvFieldParser(options_);
    
    // Open file
    open_file(filepath_);
    
    // Skip initial lines if requested
    for (size_t i = 0; i < options_.skip_lines && file_stream_.good(); ++i) {
        std::string line;
        std::getline(file_stream_, line);
        current_line_++;
    }
    
    // Read header if present
    if (options_.has_header) {
        read_header();
    } else if (config.find("column_names") != config.end()) {
        column_names_ = std::any_cast<std::vector<std::string>>(config.at("column_names"));
    }
    
    // Get column types if provided
    if (config.find("column_types") != config.end()) {
        column_types_ = std::any_cast<std::vector<std::string>>(config.at("column_types"));
    } else {
        // Infer types from data
        infer_column_types();
    }
    
    start_time_ = std::chrono::steady_clock::now();
    logger->info("CSV extractor initialized for file: {}", filepath_);
}

core::RecordBatch CsvExtractor::extract_batch(size_t batch_size,
                                            core::ProcessingContext& context) {
    core::RecordBatch batch;
    batch.reserve(batch_size);
    
    auto logger = common::Logger::get("omop-csv-extractor");
    size_t count = 0;
    
    while (file_stream_.good() && count < batch_size && has_more_) {
        std::string line;
        std::getline(file_stream_, line);
        
        if (line.empty() && options_.skip_empty_lines) {
            continue;
        }
        
        if (options_.max_lines > 0 && current_line_ >= options_.max_lines) {
            has_more_ = false;
            break;
        }
        
        try {
            auto fields = parser_.parse_line(line);
            
            if (!fields.empty()) {
                auto record = create_record(fields);
                batch.addRecord(std::move(record));
                count++;
                extracted_count_++;
            }
            
        } catch (const std::exception& e) {
            error_count_++;
            context.increment_errors();
            
            logger->warn("Error parsing line {}: {}", current_line_, e.what());
            
            // Continue processing on error by default
        }
        
        current_line_++;
    }
    
    // Check if we've reached end of file
    if (!file_stream_.good() || 
        (options_.max_lines > 0 && current_line_ >= options_.max_lines)) {
        has_more_ = false;
    }
    
    return batch;
}

void CsvExtractor::finalize(core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-csv-extractor");
    
    if (file_stream_.is_open()) {
        file_stream_.close();
    }
    
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        end_time - start_time_).count();
    
    logger->info("CSV extraction completed: {} records extracted, {} errors in {} seconds",
                extracted_count_, error_count_, duration);
}

std::unordered_map<std::string, std::any> CsvExtractor::get_statistics() const {
    std::unordered_map<std::string, std::any> stats;
    
    stats["filepath"] = filepath_;
    stats["total_lines"] = current_line_;
    stats["extracted_count"] = extracted_count_;
    stats["error_count"] = error_count_;
    stats["column_count"] = column_names_.size();
    
    auto current_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        current_time - start_time_).count();
    stats["extraction_time_seconds"] = duration;
    
    if (duration > 0) {
        stats["records_per_second"] = static_cast<double>(extracted_count_) / duration;
    }
    
    return stats;
}

void CsvExtractor::open_file(const std::string& filepath) {
    if (!std::filesystem::exists(filepath)) {
        throw common::ExtractionException(
            std::format("CSV file not found: '{}'", filepath), "csv");
    }
    
    file_stream_.open(filepath, std::ios::in);
    if (!file_stream_.is_open()) {
        throw common::ExtractionException(
            std::format("Failed to open CSV file: '{}'", filepath), "csv");
    }
    
    // Count total lines if needed
    if (options_.max_lines == 0) {
        std::ifstream count_stream(filepath);
        total_lines_ = std::count(std::istreambuf_iterator<char>(count_stream),
                                 std::istreambuf_iterator<char>(), '\n');
        count_stream.close();
    }
}

void CsvExtractor::read_header() {
    std::string header_line;
    std::getline(file_stream_, header_line);
    current_line_++;
    
    if (header_line.empty()) {
        throw common::ExtractionException("Empty header line in CSV file", "csv");
    }
    
    column_names_ = parser_.parse_line(header_line);
    
    // Trim column names
    for (auto& name : column_names_) {
        name.erase(0, name.find_first_not_of(" \t\r\n"));
        name.erase(name.find_last_not_of(" \t\r\n") + 1);
    }
}

void CsvExtractor::infer_column_types(size_t sample_size) {
    auto logger = common::Logger::get("omop-csv-extractor");
    logger->info("Inferring column types from {} sample rows", sample_size);
    
    // Save current position
    auto current_pos = file_stream_.tellg();
    
    // Read sample rows
    std::vector<std::vector<std::string>> sample_rows;
    size_t rows_read = 0;
    
    while (file_stream_.good() && rows_read < sample_size) {
        std::string line;
        std::getline(file_stream_, line);
        
        if (line.empty() && options_.skip_empty_lines) {
            continue;
        }
        
        auto fields = parser_.parse_line(line);
        if (!fields.empty()) {
            sample_rows.push_back(fields);
            rows_read++;
        }
    }
    
    // Infer types for each column
    column_types_.resize(column_names_.size(), "string");
    
    for (size_t col = 0; col < column_names_.size(); ++col) {
        bool all_integer = true;
        bool all_double = true;
        bool all_boolean = true;
        bool all_date = true;
        bool all_datetime = true;
        
        for (const auto& row : sample_rows) {
            if (col >= row.size() || row[col].empty() || 
                row[col] == options_.null_string) {
                continue;
            }
            
            const std::string& value = row[col];
            
            // Check integer
            if (all_integer) {
                try {
                    std::stoll(value);
                } catch (...) {
                    all_integer = false;
                }
            }
            
            // Check double
            if (all_double && !all_integer) {
                try {
                    std::stod(value);
                } catch (...) {
                    all_double = false;
                }
            }
            
            // Check boolean
            if (all_boolean) {
                std::string upper = value;
                std::transform(upper.begin(), upper.end(), upper.begin(), ::toupper);
                if (upper != options_.true_string && upper != options_.false_string &&
                    upper != "1" && upper != "0" && upper != "TRUE" && upper != "FALSE" &&
                    upper != "YES" && upper != "NO" && upper != "Y" && upper != "N") {
                    all_boolean = false;
                }
            }
            
            // Check date/datetime
            if (all_datetime || all_date) {
                auto datetime = parser_.parse_datetime(value, options_.datetime_format);
                auto date = parser_.parse_datetime(value, options_.date_format);
                
                if (datetime.time_since_epoch().count() == 0) {
                    all_datetime = false;
                }
                if (date.time_since_epoch().count() == 0) {
                    all_date = false;
                }
            }
        }
        
        // Assign inferred type
        if (all_integer) {
            column_types_[col] = "integer";
        } else if (all_double) {
            column_types_[col] = "double";
        } else if (all_boolean) {
            column_types_[col] = "boolean";
        } else if (all_datetime) {
            column_types_[col] = "datetime";
        } else if (all_date) {
            column_types_[col] = "date";
        } else {
            column_types_[col] = "string";
        }
    }
    
    // Restore file position
    file_stream_.clear();
    file_stream_.seekg(current_pos);
    
    logger->info("Column types inferred successfully");
}

core::Record CsvExtractor::create_record(const std::vector<std::string>& fields) {
    core::Record record;
    
    for (size_t i = 0; i < fields.size() && i < column_names_.size(); ++i) {
        const std::string& field_name = column_names_[i];
        const std::string& field_value = fields[i];
        
        // Get type hint
        std::string type_hint = i < column_types_.size() ? column_types_[i] : "";
        
        // Convert and set field
        try {
            auto converted_value = parser_.convert_field(field_value, type_hint);
            record.setField(field_name, converted_value);
        } catch (const std::exception& e) {
            // Log conversion error but continue
            auto logger = common::Logger::get("omop-csv-extractor");
            logger->debug("Failed to convert field '{}' value '{}': {}", 
                        field_name, field_value, e.what());
            
            // Store as string
            record.setField(field_name, field_value);
        }
    }
    
    // Add metadata
    // Set metadata using the Record metadata structure
    core::Record::RecordMetadata metadata;
    metadata.custom["source_file"] = filepath_;
    metadata.custom["line_number"] = current_line_;
    metadata.extraction_time = std::chrono::system_clock::now();
    record.setMetadata(metadata);
    
    return record;
}

// MultiFileCsvExtractor implementation

void MultiFileCsvExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                     core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-multi-csv-extractor");
    
    // Get file paths
    if (config.find("files") == config.end()) {
        throw common::ConfigurationException("Multi-file CSV extractor requires 'files' parameter");
    }
    file_paths_ = std::any_cast<std::vector<std::string>>(config.at("files"));
    
    if (file_paths_.empty()) {
        throw common::ConfigurationException("No files provided for multi-file CSV extractor");
    }
    
    // Get skip headers option
    if (config.find("skip_headers_after_first") != config.end()) {
        skip_headers_after_first_ = std::any_cast<bool>(config.at("skip_headers_after_first"));
    }
    
    logger->info("Initializing multi-file CSV extractor with {} files", file_paths_.size());
    
    // Create modified config for first file
    auto file_config = config;
    file_config["filepath"] = file_paths_[0];
    
    // Initialize with first file
    CsvExtractor::initialize(file_config, context);
}

bool MultiFileCsvExtractor::has_more_data() const {
    return CsvExtractor::has_more_data() || 
           (current_file_index_ + 1 < file_paths_.size());
}

bool MultiFileCsvExtractor::next_file() {
    auto logger = common::Logger::get("omop-multi-csv-extractor");
    
    current_file_index_++;
    if (current_file_index_ >= file_paths_.size()) {
        return false;
    }
    
    // Close current file
    if (file_stream_.is_open()) {
        file_stream_.close();
    }
    
    // Open next file
    filepath_ = file_paths_[current_file_index_];
    logger->info("Switching to file {}/{}: {}", 
                current_file_index_ + 1, file_paths_.size(), filepath_);
    
    open_file(filepath_);
    
    // Skip header if needed
    if (options_.has_header && skip_headers_after_first_) {
        std::string header_line;
        std::getline(file_stream_, header_line);
        current_line_++;
    }
    
    has_more_ = true;
    return true;
}

// CsvDirectoryExtractor implementation

void CsvDirectoryExtractor::initialize(const std::unordered_map<std::string, std::any>& config,
                                     core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-csv-directory-extractor");
    
    // Get directory path
    if (config.find("directory") == config.end()) {
        throw common::ConfigurationException("CSV directory extractor requires 'directory' parameter");
    }
    directory_path_ = std::any_cast<std::string>(config.at("directory"));
    
    // Get pattern
    std::string pattern = ".*\\.csv$";  // Default pattern
    if (config.find("pattern") != config.end()) {
        pattern = std::any_cast<std::string>(config.at("pattern"));
    }
    file_pattern_ = std::regex(pattern, std::regex_constants::icase);
    
    // Get recursive option
    if (config.find("recursive") != config.end()) {
        recursive_search_ = std::any_cast<bool>(config.at("recursive"));
    }
    
    // Find CSV files
    file_paths_ = find_csv_files(directory_path_, pattern, recursive_search_);
    
    if (file_paths_.empty()) {
        throw common::ExtractionException(
            std::format("No CSV files found in directory '{}' matching pattern '{}'", 
                       directory_path_, pattern),
            "csv_directory");
    }
    
    logger->info("Found {} CSV files in directory '{}'", 
                file_paths_.size(), directory_path_);
    
    // Initialize with multi-file config
    auto multi_config = config;
    multi_config["files"] = file_paths_;
    
    MultiFileCsvExtractor::initialize(multi_config, context);
}

std::vector<std::string> CsvDirectoryExtractor::find_csv_files(const std::string& directory,
                                                             const std::string& pattern,
                                                             bool recursive) {
    std::vector<std::string> files;
    std::regex file_regex(pattern, std::regex_constants::icase);
    
    try {
        if (recursive) {
            for (const auto& entry : std::filesystem::recursive_directory_iterator(directory)) {
                if (entry.is_regular_file() && 
                    std::regex_match(entry.path().filename().string(), file_regex)) {
                    files.push_back(entry.path().string());
                }
            }
        } else {
            for (const auto& entry : std::filesystem::directory_iterator(directory)) {
                if (entry.is_regular_file() && 
                    std::regex_match(entry.path().filename().string(), file_regex)) {
                    files.push_back(entry.path().string());
                }
            }
        }
    } catch (const std::filesystem::filesystem_error& e) {
        throw common::ExtractionException(
            std::format("Error accessing directory '{}': {}", directory, e.what()),
            "csv_directory");
    }
    
    // Sort files for consistent ordering
    std::sort(files.begin(), files.end());
    
    return files;
}

// CsvExtractorFactory implementation

std::unique_ptr<core::IExtractor> CsvExtractorFactory::create(const std::string& type) {
    if (type == "csv") {
        return std::make_unique<CsvExtractor>();
    } else if (type == "multi_csv") {
        return std::make_unique<MultiFileCsvExtractor>();
    } else if (type == "csv_directory") {
        return std::make_unique<CsvDirectoryExtractor>();
    } else if (type == "compressed_csv") {
        return std::make_unique<CompressedCsvExtractor>();
    } else {
        throw common::ConfigurationException(
            std::format("Unknown CSV extractor type: '{}'", type));
    }
}

void CsvExtractorFactory::register_extractors() {
    // Register with main factory if needed
    // This would typically be done during application initialization
}

} // namespace omop::extract