/**
 * @file extract.h
 * @brief Comprehensive header for the OMOP ETL extract module
 * <AUTHOR> ETL Team
 * @date 2024
 *
 * This header provides convenient access to all extraction functionality
 * within the OMOP ETL pipeline, including various data source extractors
 * and supporting utilities.
 */

#pragma once

// Core interfaces
#include "core/interfaces.h"

// Base extractor functionality
#include "extract/extractor_base.h"
#include "extract/extractor_factory.h"

// File-based extractors
#include "extract/csv_extractor.h"
#include "extract/json_extractor.h"

// Database extractors
#include "extract/database_connector.h"
#include "extract/postgresql_connector.h"
#ifdef OMOP_HAS_MYSQL
#include "extract/mysql_connector.h"
#endif
#ifdef OMOP_HAS_ODBC
#include "extract/odbc_connector.h"
#endif

// Utilities
#include "common/exceptions.h"
#include "common/logging.h"

/**
 * @namespace omop::extract
 * @brief Data extraction functionality for the OMOP ETL pipeline
 *
 * The extract namespace contains all components responsible for extracting
 * data from various sources including files (CSV, JSON) and databases
 * (PostgreSQL, MySQL, ODBC). The module provides a unified interface for
 * data extraction with support for batch processing, error handling, and
 * progress monitoring.
 */
namespace omop::extract {

/**
 * @brief Simplified extractor creation with automatic type detection
 * @param source_path Path to data source (file or connection string)
 * @param config Additional configuration parameters
 * @return std::unique_ptr<core::IExtractor> Configured extractor instance
 * @throws ConfigurationException if source type cannot be determined
 */
std::unique_ptr<core::IExtractor> create_extractor_auto(
    const std::string& source_path,
    const std::unordered_map<std::string, std::any>& config = {});

/**
 * @brief Extract data source type from path or configuration
 * @param source_path Path to data source
 * @return std::string Extractor type identifier
 */
std::string detect_source_type(const std::string& source_path);

/**
 * @brief Validate extractor configuration
 * @param type Extractor type
 * @param config Configuration parameters
 * @return std::pair<bool, std::string> Validation result and error message
 */
std::pair<bool, std::string> validate_extractor_config(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config);

/**
 * @brief Convenience class for batch extraction operations
 *
 * This class provides high-level functionality for extracting data
 * from various sources with automatic error handling and progress tracking.
 */
class BatchExtractor {
public:
    /**
     * @brief Configuration for batch extraction
     */
    struct Config {
        size_t batch_size = 10000;
        size_t max_records = 0;  // 0 = no limit
        bool continue_on_error = true;
        std::function<void(size_t, size_t)> progress_callback;
        std::function<void(const std::string&)> error_callback;
    };

    /**
     * @brief Constructor with default config
     * @param extractor Extractor instance
     */
    explicit BatchExtractor(std::unique_ptr<core::IExtractor> extractor);

    /**
     * @brief Constructor with custom config
     * @param extractor Extractor instance
     * @param config Batch extraction configuration
     */
    BatchExtractor(std::unique_ptr<core::IExtractor> extractor,
                  const Config& config);

    /**
     * @brief Extract all records
     * @return std::vector<core::Record> All extracted records
     */
    std::vector<core::Record> extract_all();

    /**
     * @brief Extract records with callback processing
     * @param processor Callback function for processing each batch
     * @return size_t Total number of records processed
     */
    size_t extract_with_callback(
        std::function<void(const core::RecordBatch&)> processor);

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics
     */
    std::unordered_map<std::string, std::any> get_statistics() const;

private:
    std::unique_ptr<core::IExtractor> extractor_;
    Config config_;
    core::ProcessingContext context_;
};

/**
 * @brief Parallel extraction coordinator
 *
 * This class manages parallel extraction from multiple sources,
 * coordinating thread pools and aggregating results.
 */
class ParallelExtractor {
public:
    /**
     * @brief Configuration for parallel extraction
     */
    struct Config {
        size_t num_threads = 4;
        size_t queue_size = 100;
        bool preserve_order = false;
    };

    /**
     * @brief Constructor with default config
     */
    ParallelExtractor();

    /**
     * @brief Constructor with custom config
     * @param config Parallel extraction configuration
     */
    explicit ParallelExtractor(const Config& config);

    /**
     * @brief Add extractor to parallel processing
     * @param extractor Extractor instance
     * @param name Optional name for identification
     */
    void add_extractor(std::unique_ptr<core::IExtractor> extractor,
                      const std::string& name = "");

    /**
     * @brief Execute parallel extraction
     * @return std::vector<core::Record> Aggregated records from all sources
     */
    std::vector<core::Record> extract_all();

    /**
     * @brief Execute parallel extraction with streaming
     * @param processor Callback for processing record batches
     */
    void extract_streaming(
        std::function<void(const core::RecordBatch&, const std::string&)> processor);

    /**
     * @brief Get statistics for all extractors
     * @return std::unordered_map<std::string, std::unordered_map<std::string, std::any>>
     *         Statistics per extractor
     */
    std::unordered_map<std::string, std::unordered_map<std::string, std::any>> 
    get_all_statistics() const;

private:
    Config config_;
    std::vector<std::pair<std::string, std::unique_ptr<core::IExtractor>>> extractors_;
    class Impl;
    std::unique_ptr<Impl> impl_;
};

/**
 * @brief Utility functions for common extraction patterns
 */
namespace utils {

/**
 * @brief Extract records from CSV file
 * @param filepath Path to CSV file
 * @param options Additional CSV options
 * @return std::vector<core::Record> Extracted records
 */
std::vector<core::Record> extract_csv(
    const std::string& filepath,
    const CsvOptions& options = {});

/**
 * @brief Extract records from JSON file
 * @param filepath Path to JSON file
 * @param options Additional JSON options
 * @return std::vector<core::Record> Extracted records
 */
std::vector<core::Record> extract_json(
    const std::string& filepath,
    const JsonOptions& options = {});

/**
 * @brief Extract records from database table
 * @param connection Database connection
 * @param table_name Table name
 * @param filter Optional WHERE clause
 * @return std::vector<core::Record> Extracted records
 */
std::vector<core::Record> extract_table(
    std::unique_ptr<IDatabaseConnection> connection,
    const std::string& table_name,
    const std::string& filter = "");

/**
 * @brief Create database connection from URL
 * @param url Database URL (e.g., "postgresql://user:pass@host:port/db")
 * @return std::unique_ptr<IDatabaseConnection> Database connection
 */
std::unique_ptr<IDatabaseConnection> create_connection_from_url(
    const std::string& url);

} // namespace utils

} // namespace omop::extract