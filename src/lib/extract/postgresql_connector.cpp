/**
 * @file postgresql_connector.cpp
 * @brief PostgreSQL database connector implementation
 * <AUTHOR> ETL Team
 * @date 2024
 */

#include "postgresql_connector.h"
#include "common/logging.h"
#include "common/exceptions.h"
#include <format>
#include <chrono>
#include <ctime>
#include <sstream>
#include <iomanip>

namespace omop::extract {

// PostgreSQL type names
static const std::unordered_map<Oid, std::string> PG_TYPE_NAMES = {
    {16, "boolean"},
    {20, "bigint"},
    {21, "smallint"},
    {23, "integer"},
    {25, "text"},
    {700, "real"},
    {701, "double precision"},
    {1043, "varchar"},
    {1082, "date"},
    {1114, "timestamp"},
    {1184, "timestamptz"}
};

// PostgreSQLResultSet implementation

PostgreSQLResultSet::PostgreSQLResultSet(PGresult* result)
    : result_(result),
      row_count_(PQntuples(result)),
      current_row_(-1),
      column_count_(PQnfields(result)) {
    
    if (!result_) {
        throw common::DatabaseException("Invalid PostgreSQL result set", "PostgreSQL", 0);
    }
}

PostgreSQLResultSet::~PostgreSQLResultSet() {
    if (result_) {
        PQclear(result_);
    }
}

bool PostgreSQLResultSet::next() {
    if (current_row_ + 1 < row_count_) {
        current_row_++;
        return true;
    }
    return false;
}

std::any PostgreSQLResultSet::get_value(size_t index) const {
    if (index >= static_cast<size_t>(column_count_)) {
        throw common::DatabaseException(
            std::format("Column index {} out of range (0-{})", index, column_count_ - 1),
            "PostgreSQL", 0);
    }
    
    if (current_row_ < 0 || current_row_ >= row_count_) {
        throw common::DatabaseException("No current row", "PostgreSQL", 0);
    }
    
    if (PQgetisnull(result_, current_row_, static_cast<int>(index))) {
        return std::any{};
    }
    
    const char* value = PQgetvalue(result_, current_row_, static_cast<int>(index));
    Oid type_oid = PQftype(result_, static_cast<int>(index));
    
    return convert_value(value, type_oid);
}

std::any PostgreSQLResultSet::get_value(const std::string& column_name) const {
    size_t index = get_column_index(column_name);
    return get_value(index);
}

bool PostgreSQLResultSet::is_null(size_t index) const {
    if (index >= static_cast<size_t>(column_count_)) {
        throw common::DatabaseException(
            std::format("Column index {} out of range", index),
            "PostgreSQL", 0);
    }
    
    if (current_row_ < 0 || current_row_ >= row_count_) {
        throw common::DatabaseException("No current row", "PostgreSQL", 0);
    }
    
    return PQgetisnull(result_, current_row_, static_cast<int>(index)) == 1;
}

bool PostgreSQLResultSet::is_null(const std::string& column_name) const {
    size_t index = get_column_index(column_name);
    return is_null(index);
}

size_t PostgreSQLResultSet::column_count() const {
    return static_cast<size_t>(column_count_);
}

std::string PostgreSQLResultSet::column_name(size_t index) const {
    if (index >= static_cast<size_t>(column_count_)) {
        throw common::DatabaseException(
            std::format("Column index {} out of range", index),
            "PostgreSQL", 0);
    }
    
    return PQfname(result_, static_cast<int>(index));
}

std::string PostgreSQLResultSet::column_type(size_t index) const {
    if (index >= static_cast<size_t>(column_count_)) {
        throw common::DatabaseException(
            std::format("Column index {} out of range", index),
            "PostgreSQL", 0);
    }
    
    Oid type_oid = PQftype(result_, static_cast<int>(index));
    auto it = PG_TYPE_NAMES.find(type_oid);
    
    if (it != PG_TYPE_NAMES.end()) {
        return it->second;
    }
    
    return std::format("oid:{}", type_oid);
}

size_t PostgreSQLResultSet::get_column_index(const std::string& column_name) const {
    // Check cache first
    auto it = column_index_cache_.find(column_name);
    if (it != column_index_cache_.end()) {
        return it->second;
    }
    
    // Search for column
    int index = PQfnumber(result_, column_name.c_str());
    if (index < 0) {
        throw common::DatabaseException(
            std::format("Column '{}' not found", column_name),
            "PostgreSQL", 0);
    }
    
    // Cache the result
    column_index_cache_[column_name] = static_cast<size_t>(index);
    return static_cast<size_t>(index);
}

std::any PostgreSQLResultSet::convert_value(const char* value, Oid oid) const {
    if (!value) {
        return std::any{};
    }
    
    std::string str_value(value);
    
    switch (oid) {
        case 16:  // boolean
            return str_value == "t" || str_value == "true" || str_value == "1";
            
        case 20:  // bigint
            return std::stoll(str_value);
            
        case 21:  // smallint
        case 23:  // integer
            return std::stoi(str_value);
            
        case 700: // real
        case 701: // double precision
            return std::stod(str_value);
            
        case 1082: // date
        case 1114: // timestamp
        case 1184: // timestamptz
        {
            // Parse PostgreSQL timestamp format
            std::tm tm = {};
            std::stringstream ss(str_value);
            ss >> std::get_time(&tm, "%Y-%m-%d %H:%M:%S");
            
            if (ss.fail()) {
                // Try date-only format
                ss.clear();
                ss.str(str_value);
                ss >> std::get_time(&tm, "%Y-%m-%d");
            }
            
            if (!ss.fail()) {
                return std::chrono::system_clock::from_time_t(std::mktime(&tm));
            }
            
            // Return as string if parsing fails
            return str_value;
        }
            
        default:
            // Default to string
            return str_value;
    }
}

// PostgreSQLPreparedStatement implementation

PostgreSQLPreparedStatement::PostgreSQLPreparedStatement(PGconn* connection,
                                                       const std::string& statement_name,
                                                       const std::string& sql)
    : connection_(connection),
      statement_name_(statement_name),
      sql_(sql) {
    
    if (!connection_) {
        throw common::DatabaseException("Invalid connection", "PostgreSQL", 0);
    }
    
    // Prepare the statement
    PGresult* result = PQprepare(connection_, statement_name_.c_str(), sql_.c_str(), 0, nullptr);
    
    if (PQresultStatus(result) != PGRES_COMMAND_OK) {
        std::string error = PQerrorMessage(connection_);
        PQclear(result);
        throw common::DatabaseException(
            std::format("Failed to prepare statement: {}", error),
            "PostgreSQL", 0);
    }
    
    PQclear(result);
}

PostgreSQLPreparedStatement::~PostgreSQLPreparedStatement() {
    // Deallocate prepared statement
    std::string dealloc_sql = std::format("DEALLOCATE {}", statement_name_);
    PGresult* result = PQexec(connection_, dealloc_sql.c_str());
    if (result) {
        PQclear(result);
    }
}

void PostgreSQLPreparedStatement::bind(size_t index, const std::any& value) {
    if (index == 0) {
        throw common::DatabaseException("Parameter index must be 1-based", "PostgreSQL", 0);
    }
    
    // Ensure parameters vector is large enough
    if (parameters_.size() < index) {
        parameters_.resize(index);
    }
    
    // Convert value to string
    parameters_[index - 1] = convert_parameter(value);
}

std::unique_ptr<IResultSet> PostgreSQLPreparedStatement::execute_query() {
    // Prepare parameter arrays
    param_values_.clear();
    param_lengths_.clear();
    param_formats_.clear();
    
    for (const auto& param : parameters_) {
        param_values_.push_back(param.c_str());
        param_lengths_.push_back(static_cast<int>(param.length()));
        param_formats_.push_back(0); // Text format
    }
    
    PGresult* result = PQexecPrepared(connection_,
                                     statement_name_.c_str(),
                                     static_cast<int>(parameters_.size()),
                                     param_values_.data(),
                                     param_lengths_.data(),
                                     param_formats_.data(),
                                     0); // Text result format
    
    if (PQresultStatus(result) != PGRES_TUPLES_OK) {
        std::string error = PQerrorMessage(connection_);
        PQclear(result);
        throw common::DatabaseException(
            std::format("Query execution failed: {}", error),
            "PostgreSQL", 0);
    }
    
    return std::make_unique<PostgreSQLResultSet>(result);
}

size_t PostgreSQLPreparedStatement::execute_update() {
    // Prepare parameter arrays
    param_values_.clear();
    param_lengths_.clear();
    param_formats_.clear();
    
    for (const auto& param : parameters_) {
        param_values_.push_back(param.c_str());
        param_lengths_.push_back(static_cast<int>(param.length()));
        param_formats_.push_back(0); // Text format
    }
    
    PGresult* result = PQexecPrepared(connection_,
                                     statement_name_.c_str(),
                                     static_cast<int>(parameters_.size()),
                                     param_values_.data(),
                                     param_lengths_.data(),
                                     param_formats_.data(),
                                     0); // Text result format
    
    if (PQresultStatus(result) != PGRES_COMMAND_OK) {
        std::string error = PQerrorMessage(connection_);
        PQclear(result);
        throw common::DatabaseException(
            std::format("Update execution failed: {}", error),
            "PostgreSQL", 0);
    }
    
    // Get affected rows
    const char* affected = PQcmdTuples(result);
    size_t rows_affected = affected ? std::stoull(affected) : 0;
    
    PQclear(result);
    return rows_affected;
}

void PostgreSQLPreparedStatement::clear_parameters() {
    parameters_.clear();
}

std::string PostgreSQLPreparedStatement::convert_parameter(const std::any& value) const {
    if (!value.has_value()) {
        return "NULL";
    }
    
    if (value.type() == typeid(bool)) {
        return std::any_cast<bool>(value) ? "true" : "false";
    }
    else if (value.type() == typeid(int)) {
        return std::to_string(std::any_cast<int>(value));
    }
    else if (value.type() == typeid(long)) {
        return std::to_string(std::any_cast<long>(value));
    }
    else if (value.type() == typeid(long long)) {
        return std::to_string(std::any_cast<long long>(value));
    }
    else if (value.type() == typeid(double)) {
        return std::to_string(std::any_cast<double>(value));
    }
    else if (value.type() == typeid(std::string)) {
        return std::any_cast<std::string>(value);
    }
    else if (value.type() == typeid(const char*)) {
        return std::string(std::any_cast<const char*>(value));
    }
    else if (value.type() == typeid(std::chrono::system_clock::time_point)) {
        auto tp = std::any_cast<std::chrono::system_clock::time_point>(value);
        auto time_t = std::chrono::system_clock::to_time_t(tp);
        std::tm tm = *std::localtime(&time_t);
        std::stringstream ss;
        ss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S");
        return ss.str();
    }
    else {
        throw common::DatabaseException(
            "Unsupported parameter type for PostgreSQL", "PostgreSQL", 0);
    }
}

// PostgreSQLConnection implementation

PostgreSQLConnection::PostgreSQLConnection()
    : connection_(nullptr),
      in_transaction_(false),
      query_timeout_(0),
      statement_counter_(0) {
}

PostgreSQLConnection::~PostgreSQLConnection() {
    disconnect();
}

void PostgreSQLConnection::connect(const ConnectionParams& params) {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (connection_) {
        disconnect();
    }
    
    std::string conn_string = build_connection_string(params);
    connection_ = PQconnectdb(conn_string.c_str());
    
    if (PQstatus(connection_) != CONNECTION_OK) {
        std::string error = PQerrorMessage(connection_);
        PQfinish(connection_);
        connection_ = nullptr;
        throw common::DatabaseException(
            std::format("Failed to connect to PostgreSQL: {}", error),
            "PostgreSQL", 0);
    }
    
    // Set client encoding to UTF8
    if (PQsetClientEncoding(connection_, "UTF8") != 0) {
        auto logger = common::Logger::get("omop-postgresql");
        logger->warn("Failed to set client encoding to UTF8");
    }
    
    // Apply query timeout if specified
    if (query_timeout_ > 0) {
        set_query_timeout(query_timeout_);
    }
}

void PostgreSQLConnection::disconnect() {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (connection_) {
        PQfinish(connection_);
        connection_ = nullptr;
        in_transaction_ = false;
    }
}

bool PostgreSQLConnection::is_connected() const {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (!connection_) {
        return false;
    }
    
    return PQstatus(connection_) == CONNECTION_OK;
}

std::unique_ptr<IResultSet> PostgreSQLConnection::execute_query(const std::string& sql) {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (!connection_) {
        throw common::DatabaseException("Not connected to database", "PostgreSQL", 0);
    }
    
    PGresult* result = PQexec(connection_, sql.c_str());
    check_error(result, "execute_query");
    
    if (PQresultStatus(result) != PGRES_TUPLES_OK) {
        std::string error = PQerrorMessage(connection_);
        PQclear(result);
        throw common::DatabaseException(
            std::format("Query execution failed: {}", error),
            "PostgreSQL", 0);
    }
    
    return std::make_unique<PostgreSQLResultSet>(result);
}

size_t PostgreSQLConnection::execute_update(const std::string& sql) {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (!connection_) {
        throw common::DatabaseException("Not connected to database", "PostgreSQL", 0);
    }
    
    PGresult* result = PQexec(connection_, sql.c_str());
    check_error(result, "execute_update");
    
    if (PQresultStatus(result) != PGRES_COMMAND_OK) {
        std::string error = PQerrorMessage(connection_);
        PQclear(result);
        throw common::DatabaseException(
            std::format("Update execution failed: {}", error),
            "PostgreSQL", 0);
    }
    
    const char* affected = PQcmdTuples(result);
    size_t rows_affected = affected ? std::stoull(affected) : 0;
    
    PQclear(result);
    return rows_affected;
}

std::unique_ptr<IPreparedStatement> PostgreSQLConnection::prepare_statement(const std::string& sql) {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (!connection_) {
        throw common::DatabaseException("Not connected to database", "PostgreSQL", 0);
    }
    
    std::string statement_name = generate_statement_name();
    return std::make_unique<PostgreSQLPreparedStatement>(connection_, statement_name, sql);
}

void PostgreSQLConnection::begin_transaction() {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (in_transaction_) {
        throw common::DatabaseException("Transaction already in progress", "PostgreSQL", 0);
    }
    
    execute_update("BEGIN");
    in_transaction_ = true;
}

void PostgreSQLConnection::commit() {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (!in_transaction_) {
        throw common::DatabaseException("No transaction in progress", "PostgreSQL", 0);
    }
    
    execute_update("COMMIT");
    in_transaction_ = false;
}

void PostgreSQLConnection::rollback() {
    std::lock_guard<std::mutex> lock(connection_mutex_);
    
    if (!in_transaction_) {
        throw common::DatabaseException("No transaction in progress", "PostgreSQL", 0);
    }
    
    execute_update("ROLLBACK");
    in_transaction_ = false;
}

std::string PostgreSQLConnection::get_version() const {
    auto result = const_cast<PostgreSQLConnection*>(this)->execute_query("SELECT version()");
    if (result->next()) {
        auto version_any = result->get_value(0);
        if (version_any.has_value() && version_any.type() == typeid(std::string)) {
            return std::any_cast<std::string>(version_any);
        }
    }
    return "Unknown";
}

void PostgreSQLConnection::set_query_timeout(int seconds) {
    query_timeout_ = seconds;
    
    if (connection_) {
        std::string timeout_sql = std::format("SET statement_timeout = {}", seconds * 1000);
        execute_update(timeout_sql);
    }
}

bool PostgreSQLConnection::table_exists(const std::string& table_name,
                                       const std::string& schema) const {
    std::string query;
    if (schema.empty()) {
        query = std::format(
            "SELECT EXISTS (SELECT 1 FROM information_schema.tables "
            "WHERE table_name = '{}' AND table_schema = current_schema())",
            table_name);
    } else {
        query = std::format(
            "SELECT EXISTS (SELECT 1 FROM information_schema.tables "
            "WHERE table_name = '{}' AND table_schema = '{}')",
            table_name, schema);
    }
    
    auto result = const_cast<PostgreSQLConnection*>(this)->execute_query(query);
    if (result->next()) {
        auto exists_any = result->get_value(0);
        if (exists_any.has_value() && exists_any.type() == typeid(bool)) {
            return std::any_cast<bool>(exists_any);
        }
    }
    
    return false;
}

std::string PostgreSQLConnection::build_connection_string(const ConnectionParams& params) const {
    std::stringstream conn_str;
    
    if (!params.host.empty()) {
        conn_str << "host=" << params.host << " ";
    }
    
    if (params.port > 0) {
        conn_str << "port=" << params.port << " ";
    }
    
    if (!params.database.empty()) {
        conn_str << "dbname=" << params.database << " ";
    }
    
    if (!params.username.empty()) {
        conn_str << "user=" << params.username << " ";
    }
    
    if (!params.password.empty()) {
        conn_str << "password=" << params.password << " ";
    }
    
    // Add custom options
    for (const auto& [key, value] : params.options) {
        conn_str << key << "=" << value << " ";
    }
    
    return conn_str.str();
}

void PostgreSQLConnection::check_error(PGresult* result, const std::string& operation) const {
    if (!result) {
        throw common::DatabaseException(
            std::format("{} failed: NULL result", operation),
            "PostgreSQL", 0);
    }
    
    ExecStatusType status = PQresultStatus(result);
    if (status == PGRES_BAD_RESPONSE || status == PGRES_FATAL_ERROR) {
        std::string error = PQerrorMessage(connection_);
        PQclear(result);
        throw common::DatabaseException(
            std::format("{} failed: {}", operation, error),
            "PostgreSQL", 0);
    }
}

std::string PostgreSQLConnection::generate_statement_name() {
    int counter = statement_counter_.fetch_add(1);
    return std::format("stmt_{}", counter);
}

// PostgreSQLExtractor implementation

std::string PostgreSQLExtractor::build_query() const {
    // Use parent implementation and add PostgreSQL-specific optimizations
    std::string query = DatabaseExtractor::build_query();
    return apply_query_hints(query);
}

std::string PostgreSQLExtractor::apply_query_hints(const std::string& query) const {
    // Add PostgreSQL-specific query hints for better performance
    // For large tables, use cursor-based fetching
    
    // This is a simplified example - in production, you might want to
    // analyze the query and table size to determine optimal hints
    return query;
}

} // namespace omop::extract