#pragma once

#include "extract/database_connector.h"
#include "core/interfaces.h"
#include <fstream>
#include <sstream>
#include <filesystem>
#include <regex>

namespace omop::extract {

/**
 * @brief CSV parsing options
 */
struct CsvOptions {
    char delimiter{','};
    char quote_char{'"'};
    char escape_char{'\\'};
    bool has_header{true};
    std::string encoding{"UTF-8"};
    std::vector<std::string> column_names;
    std::vector<std::string> column_types;
    bool skip_empty_lines{true};
    bool trim_fields{true};
    size_t skip_lines{0};
    std::optional<size_t> max_lines;
    std::string null_string{"NULL"};
    std::string true_string{"TRUE"};
    std::string false_string{"FALSE"};
    std::string date_format{"%Y-%m-%d"};
    std::string datetime_format{"%Y-%m-%d %H:%M:%S"};
};

/**
 * @brief CSV field parser
 *
 * Handles parsing of individual CSV fields with proper quote and escape handling.
 */
class CsvFieldParser {
public:
    /**
     * @brief Constructor
     * @param options CSV parsing options
     */
    explicit CsvFieldParser(const CsvOptions& options) : options_(options) {}

    /**
     * @brief Parse a single field
     * @param input Input string
     * @param pos Current position (updated)
     * @return std::string Parsed field value
     */
    std::string parse_field(const std::string& input, size_t& pos);

    /**
     * @brief Parse a complete line
     * @param line Input line
     * @return std::vector<std::string> Parsed fields
     */
    std::vector<std::string> parse_line(const std::string& line);

    /**
     * @brief Convert field to typed value
     * @param field Field value
     * @param type_hint Type hint (optional)
     * @return std::any Typed value
     */
    std::any convert_field(const std::string& field,
                          const std::string& type_hint = "");

    /**
     * @brief Parse date/time value
     * @param value String value
     * @param format Format string
     * @return std::chrono::system_clock::time_point Parsed time
     */
    std::chrono::system_clock::time_point parse_datetime(
        const std::string& value, const std::string& format) const;

private:
    CsvOptions options_;

    /**
     * @brief Trim whitespace from string
     * @param str String to trim
     * @return std::string Trimmed string
     */
    std::string trim(const std::string& str) const;
};

/**
 * @brief CSV file extractor
 *
 * Implements the IExtractor interface for CSV file sources,
 * providing efficient streaming extraction from CSV files.
 */
class CsvExtractor : public core::IExtractor {
public:
    /**
     * @brief Constructor
     */
    CsvExtractor() : parser_(options_) {}

    /**
     * @brief Initialize the extractor
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Extract next batch of records
     * @param batch_size Maximum number of records to extract
     * @param context Processing context
     * @return core::RecordBatch Extracted records
     */
    core::RecordBatch extract_batch(size_t batch_size,
                                   core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "csv"; }

    /**
     * @brief Finalize extraction
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

    /**
     * @brief Get extraction statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

protected:
    /**
     * @brief Open CSV file
     * @param filepath File path
     */
    void open_file(const std::string& filepath);

    /**
     * @brief Read and parse header
     */
    void read_header();

    /**
     * @brief Infer column types from data
     * @param sample_size Number of rows to sample
     */
    void infer_column_types(size_t sample_size = 100);

    /**
     * @brief Create record from parsed fields
     * @param fields Field values
     * @return core::Record Created record
     */
    core::Record create_record(const std::vector<std::string>& fields);

protected:
    std::ifstream file_stream_;
    std::string filepath_;
    CsvOptions options_;
    CsvFieldParser parser_;
    std::vector<std::string> column_names_;
    std::vector<std::string> column_types_;
    size_t current_line_{0};
    size_t total_lines_{0};
    size_t extracted_count_{0};
    size_t error_count_{0};
    bool has_more_{true};
    std::chrono::steady_clock::time_point start_time_;
};

/**
 * @brief Multi-file CSV extractor
 *
 * Extends CsvExtractor to handle multiple CSV files as a single data source.
 */
class MultiFileCsvExtractor : public CsvExtractor {
public:
    /**
     * @brief Constructor
     */
    MultiFileCsvExtractor() = default;

    /**
     * @brief Initialize with multiple files
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Check if more data is available
     * @return bool True if more data can be extracted
     */
    bool has_more_data() const override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "multi_csv"; }

protected:
    /**
     * @brief Move to next file
     * @return bool True if next file opened successfully
     */
    bool next_file();

    std::vector<std::string> file_paths_;
    size_t current_file_index_{0};
    bool skip_headers_after_first_{true};
};

/**
 * @brief CSV directory extractor
 *
 * Extracts data from all CSV files in a directory with pattern matching.
 */
class CsvDirectoryExtractor : public MultiFileCsvExtractor {
public:
    /**
     * @brief Constructor
     */
    CsvDirectoryExtractor() = default;

    /**
     * @brief Initialize with directory
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "csv_directory"; }

protected:
    /**
     * @brief Find CSV files in directory
     * @param directory Directory path
     * @param pattern File name pattern (regex)
     * @param recursive Whether to search recursively
     * @return std::vector<std::string> File paths
     */
    std::vector<std::string> find_csv_files(const std::string& directory,
                                           const std::string& pattern = ".*\\.csv$",
                                           bool recursive = false);

private:
    std::string directory_path_;
    std::regex file_pattern_;
    bool recursive_search_{false};
};

/**
 * @brief Compressed CSV extractor
 *
 * Handles extraction from compressed CSV files (gzip, zip, etc.).
 */
class CompressedCsvExtractor : public CsvExtractor {
public:
    /**
     * @brief Compression format
     */
    enum class CompressionFormat {
        None,
        Gzip,
        Zip,
        Bzip2,
        Xz
    };

    /**
     * @brief Constructor
     */
    CompressedCsvExtractor() = default;

    /**
     * @brief Initialize with compressed file
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Get extractor type name
     * @return std::string Extractor type identifier
     */
    std::string get_type() const override { return "compressed_csv"; }

    /**
     * @brief Finalize extraction and cleanup
     * @param context Processing context
     */
    void finalize(core::ProcessingContext& context) override;

protected:
    /**
     * @brief Detect compression format
     * @param filepath File path
     * @return CompressionFormat Detected format
     */
    CompressionFormat detect_compression(const std::string& filepath);

    /**
     * @brief Decompress file
     * @param filepath Compressed file path
     * @param format Compression format
     * @return std::string Path to decompressed file
     */
    std::string decompress_file(const std::string& filepath,
                               CompressionFormat format);

    /**
     * @brief Convert compression format to string
     * @param format Compression format
     * @return std::string String representation
     */
    std::string format_to_string(CompressionFormat format);

    /**
     * @brief Convert string to compression format
     * @param format_str String representation
     * @return CompressionFormat Compression format
     */
    CompressionFormat string_to_format(const std::string& format_str);

private:
    CompressionFormat compression_format_{CompressionFormat::None};
    std::string temp_file_path_;
    bool cleanup_temp_file_{true};
};

/**
 * @brief CSV extractor factory
 */
class CsvExtractorFactory {
public:
    /**
     * @brief Create CSV extractor
     * @param type Extractor type (csv, multi_csv, csv_directory, compressed_csv)
     * @return std::unique_ptr<core::IExtractor> Extractor instance
     */
    static std::unique_ptr<core::IExtractor> create(const std::string& type);

    /**
     * @brief Register CSV extractors with the main factory
     */
    static void register_extractors();
};

// Implementation helpers

inline std::string CsvFieldParser::trim(const std::string& str) const {
    if (!options_.trim_fields) return str;

    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) return "";

    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

inline std::string CsvFieldParser::parse_field(const std::string& input, size_t& pos) {
    std::string field;
    bool in_quotes = false;
    bool escape_next = false;

    // Skip leading whitespace
    while (pos < input.length() && std::isspace(input[pos]) && input[pos] != '\n') {
        ++pos;
    }

    // Check if field starts with quote
    if (pos < input.length() && input[pos] == options_.quote_char) {
        in_quotes = true;
        ++pos;
    }

    while (pos < input.length()) {
        char c = input[pos];

        if (escape_next) {
            field += c;
            escape_next = false;
            ++pos;
            continue;
        }

        if (c == options_.escape_char) {
            escape_next = true;
            ++pos;
            continue;
        }

        if (in_quotes) {
            if (c == options_.quote_char) {
                // Check for escaped quote (double quote)
                if (pos + 1 < input.length() && input[pos + 1] == options_.quote_char) {
                    field += c;
                    pos += 2;
                    continue;
                }
                in_quotes = false;
                ++pos;
                // Skip to delimiter or end of line
                while (pos < input.length() &&
                       input[pos] != options_.delimiter &&
                       input[pos] != '\n') {
                    ++pos;
                }
                break;
            }
            field += c;
            ++pos;
        } else {
            if (c == options_.delimiter || c == '\n') {
                break;
            }
            field += c;
            ++pos;
        }
    }

    // Skip delimiter if present
    if (pos < input.length() && input[pos] == options_.delimiter) {
        ++pos;
    }

    return trim(field);
}

inline std::vector<std::string> CsvFieldParser::parse_line(const std::string& line) {
    std::vector<std::string> fields;
    size_t pos = 0;

    while (pos < line.length()) {
        fields.push_back(parse_field(line, pos));
        if (pos >= line.length() || line[pos] == '\n') {
            break;
        }
    }

    return fields;
}

} // namespace omop::extract