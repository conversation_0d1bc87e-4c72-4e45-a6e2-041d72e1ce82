#pragma once

#include "core/interfaces.h"
#include "common/configuration.h"
#include "common/exceptions.h"
#include <memory>
#include <string>
#include <unordered_map>
#include <functional>
#include <regex>
#include <chrono>

namespace omop::transform {

/**
 * @brief Base class for field transformations
 *
 * This abstract class provides the interface for all field-level transformations
 * in the ETL pipeline. Concrete implementations handle specific transformation types.
 */
class FieldTransformation {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~FieldTransformation() = default;

    /**
     * @brief Apply transformation to a field value
     * @param input Input value
     * @param context Processing context
     * @return std::any Transformed value
     */
    virtual std::any transform(const std::any& input,
                              core::ProcessingContext& context) = 0;

    /**
     * @brief Validate input value before transformation
     * @param input Input value
     * @return bool True if valid
     */
    virtual bool validate_input(const std::any& input) const = 0;

    /**
     * @brief Get transformation type name
     * @return std::string Type identifier
     */
    virtual std::string get_type() const = 0;

    /**
     * @brief Configure transformation with parameters
     * @param params Configuration parameters
     */
    virtual void configure(const YAML::Node& params) = 0;
};

/**
 * @brief Direct field mapping (no transformation)
 */
class DirectTransformation : public FieldTransformation {
public:
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override {
        return input;
    }

    bool validate_input(const std::any& input) const override {
        return input.has_value();
    }

    std::string get_type() const override { return "direct"; }

    void configure(const YAML::Node& params) override {
        // No configuration needed for direct mapping
    }
};

/**
 * @brief Date format transformation
 *
 * Converts date/time values between different formats and handles
 * timezone conversions for OMOP CDM compliance.
 */
class DateTransformation : public FieldTransformation {
public:
    /**
     * @brief Constructor
     */
    DateTransformation() = default;

    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "date_transform"; }

    void configure(const YAML::Node& params) override;

private:
    std::string input_format_{"%Y-%m-%d"};
    std::string output_format_{"%Y-%m-%d %H:%M:%S"};
    std::string timezone_{"UTC"};
    bool add_time_{false};
    std::string default_time_{"00:00:00"};
};

/**
 * @brief Vocabulary mapping transformation
 *
 * Maps source values to OMOP concept IDs using vocabulary lookups.
 */
class VocabularyTransformation : public FieldTransformation {
public:
    /**
     * @brief Constructor
     * @param vocabulary_service Reference to vocabulary service
     */
    explicit VocabularyTransformation(class VocabularyService& vocabulary_service);

    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "vocabulary_mapping"; }

    void configure(const YAML::Node& params) override;

private:
    VocabularyService& vocabulary_service_;
    std::string vocabulary_name_;
    std::string source_vocabulary_;
    std::string target_vocabulary_{"OMOP"};
    int default_concept_id_{0};
    bool case_sensitive_{false};
};

/**
 * @brief Numeric value transformation
 *
 * Handles numeric conversions, unit conversions, and calculations.
 */
class NumericTransformation : public FieldTransformation {
public:
    enum class Operation {
        None,
        Multiply,
        Divide,
        Add,
        Subtract,
        Round,
        Floor,
        Ceiling,
        Absolute
    };

    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "numeric_transform"; }

    void configure(const YAML::Node& params) override;

private:
    Operation operation_{Operation::None};
    double operand_{1.0};
    int precision_{2};
    std::optional<double> min_value_;
    std::optional<double> max_value_;
    std::string unit_conversion_;
};

/**
 * @brief String concatenation transformation
 *
 * Combines multiple fields into a single string value.
 */
class StringConcatenationTransformation : public FieldTransformation {
public:
    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "string_concatenation"; }

    void configure(const YAML::Node& params) override;

    /**
     * @brief Set source fields for concatenation
     * @param fields Vector of field names
     */
    void set_source_fields(const std::vector<std::string>& fields) {
        source_fields_ = fields;
    }

    /**
     * @brief Transform multiple values
     * @param values Map of field names to values
     * @param context Processing context
     * @return std::any Concatenated string
     */
    std::any transform_multiple(const std::unordered_map<std::string, std::any>& values,
                               core::ProcessingContext& context);

private:
    std::vector<std::string> source_fields_;
    std::string separator_{" "};
    bool skip_empty_{true};
    std::string prefix_;
    std::string suffix_;
};

/**
 * @brief Conditional transformation based on rules
 *
 * Applies different transformations based on conditions.
 */
class ConditionalTransformation : public FieldTransformation {
public:
    struct Condition {
        std::string field;
        std::string operator_type;
        std::any value;
        std::string then_value;
        std::optional<std::string> else_value;
    };

    std::any transform(const std::any& input,
                      core::ProcessingContext& context) override;

    bool validate_input(const std::any& input) const override;

    std::string get_type() const override { return "conditional"; }

    void configure(const YAML::Node& params) override;

private:
    std::vector<Condition> conditions_;
    std::string default_value_;

    bool evaluate_condition(const Condition& condition,
                          const std::any& value) const;
};

/**
 * @brief Main transformation engine
 *
 * This class orchestrates the transformation process, managing field
 * transformations and applying business rules to convert source data
 * to OMOP CDM format.
 */
class TransformationEngine : public core::ITransformer {
public:
    /**
     * @brief Constructor
     */
    TransformationEngine();

    /**
     * @brief Initialize the transformer
     * @param config Configuration parameters
     * @param context Processing context
     */
    void initialize(const std::unordered_map<std::string, std::any>& config,
                   core::ProcessingContext& context) override;

    /**
     * @brief Transform a single record
     * @param record Record to transform
     * @param context Processing context
     * @return std::optional<core::Record> Transformed record
     */
    std::optional<core::Record> transform(const core::Record& record,
                                         core::ProcessingContext& context) override;

    /**
     * @brief Transform a batch of records
     * @param batch Batch to transform
     * @param context Processing context
     * @return core::RecordBatch Transformed batch
     */
    core::RecordBatch transform_batch(const core::RecordBatch& batch,
                                     core::ProcessingContext& context) override;

    /**
     * @brief Get transformer type name
     * @return std::string Transformer type identifier
     */
    std::string get_type() const override { return "omop_transformation_engine"; }

    /**
     * @brief Validate record
     * @param record Record to validate
     * @return core::ValidationResult Validation result
     */
    core::ValidationResult validate(const core::Record& record) const override;

    /**
     * @brief Get transformation statistics
     * @return std::unordered_map<std::string, std::any> Statistics map
     */
    std::unordered_map<std::string, std::any> get_statistics() const override;

    /**
     * @brief Register custom transformation
     * @param type Transformation type name
     * @param factory Factory function
     */
    void register_transformation(const std::string& type,
        std::function<std::unique_ptr<FieldTransformation>()> factory);

private:
    /**
     * @brief Apply transformation rules to a record
     * @param record Source record
     * @param rules Transformation rules
     * @param context Processing context
     * @return core::Record Transformed record
     */
    core::Record apply_transformations(const core::Record& record,
                                      const std::vector<common::TransformationRule>& rules,
                                      core::ProcessingContext& context);

    /**
     * @brief Create field transformation from rule
     * @param rule Transformation rule
     * @return std::unique_ptr<FieldTransformation> Field transformation
     */
    std::unique_ptr<FieldTransformation> create_transformation(
        const common::TransformationRule& rule);

    /**
     * @brief Apply filters to record
     * @param record Record to filter
     * @param filters Filter conditions
     * @return bool True if record passes filters
     */
    bool apply_filters(const core::Record& record,
                      const std::string& filters) const;

    /**
     * @brief Apply validation rules
     * @param record Record to validate
     * @param validations Validation rules
     * @return core::ValidationResult Validation result
     */
    core::ValidationResult apply_validations(const core::Record& record,
                                           const YAML::Node& validations) const;

    // Configuration
    common::TableMapping current_mapping_;
    std::unique_ptr<class VocabularyService> vocabulary_service_;

    // Transformation factories
    std::unordered_map<std::string,
        std::function<std::unique_ptr<FieldTransformation>()>> transformation_factories_;

    // Statistics
    mutable std::atomic<size_t> records_transformed_{0};
    mutable std::atomic<size_t> records_filtered_{0};
    mutable std::atomic<size_t> validation_errors_{0};
    mutable std::atomic<size_t> transformation_errors_{0};

    // Performance metrics
    mutable std::chrono::duration<double> total_transform_time_{0};
    mutable std::mutex stats_mutex_;
};

/**
 * @brief Factory for creating transformation engines
 */
class TransformationEngineFactory {
public:
    /**
     * @brief Create transformation engine for table
     * @param table_name OMOP table name
     * @param config Configuration manager
     * @return std::unique_ptr<TransformationEngine> Transformation engine
     */
    static std::unique_ptr<TransformationEngine> create_for_table(
        const std::string& table_name,
        const common::ConfigurationManager& config);

    /**
     * @brief Register custom transformation engine
     * @param table_name Table name
     * @param factory Factory function
     */
    static void register_engine(const std::string& table_name,
        std::function<std::unique_ptr<TransformationEngine>()> factory);

private:
    static std::unordered_map<std::string,
        std::function<std::unique_ptr<TransformationEngine>()>> engine_factories_;
};

} // namespace omop::transform