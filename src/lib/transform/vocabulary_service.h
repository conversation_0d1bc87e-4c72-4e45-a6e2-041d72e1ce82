#pragma once

#include <string>
#include <unordered_map>
#include <vector>
#include <optional>
#include <memory>
#include <shared_mutex>
#include "extract/database_connector.h"
#include "common/exceptions.h"

namespace omop::transform {

/**
 * @brief Represents an OMOP concept
 *
 * This class encapsulates the information about a single OMOP concept,
 * including its ID, name, domain, and vocabulary information.
 */
class Concept {
public:
    /**
     * @brief Default constructor
     */
    Concept() = default;

    /**
     * @brief Constructor with all fields
     */
    Concept(int concept_id,
            std::string concept_name,
            std::string domain_id,
            std::string vocabulary_id,
            std::string concept_class_id,
            std::string concept_code)
        : concept_id_(concept_id),
          concept_name_(std::move(concept_name)),
          domain_id_(std::move(domain_id)),
          vocabulary_id_(std::move(vocabulary_id)),
          concept_class_id_(std::move(concept_class_id)),
          concept_code_(std::move(concept_code)) {}

    // Getters
    [[nodiscard]] int concept_id() const noexcept { return concept_id_; }
    [[nodiscard]] const std::string& concept_name() const noexcept { return concept_name_; }
    [[nodiscard]] const std::string& domain_id() const noexcept { return domain_id_; }
    [[nodiscard]] const std::string& vocabulary_id() const noexcept { return vocabulary_id_; }
    [[nodiscard]] const std::string& concept_class_id() const noexcept { return concept_class_id_; }
    [[nodiscard]] const std::string& concept_code() const noexcept { return concept_code_; }
    [[nodiscard]] bool is_standard() const noexcept { return standard_concept_ == "S"; }
    [[nodiscard]] bool is_valid() const noexcept { return valid_end_date_.empty(); }

    // Setters
    void set_standard_concept(const std::string& standard) { standard_concept_ = standard; }
    void set_valid_dates(const std::string& start, const std::string& end) {
        valid_start_date_ = start;
        valid_end_date_ = end;
    }

private:
    int concept_id_{0};
    std::string concept_name_;
    std::string domain_id_;
    std::string vocabulary_id_;
    std::string concept_class_id_;
    std::string concept_code_;
    std::string standard_concept_;
    std::string valid_start_date_;
    std::string valid_end_date_;
};

/**
 * @brief Vocabulary mapping entry
 *
 * Represents a mapping from a source value to an OMOP concept.
 */
struct VocabularyMapping {
    std::string source_value;
    std::string source_vocabulary;
    int target_concept_id;
    std::string target_vocabulary;
    float mapping_confidence{1.0f};
    std::string mapping_type;
    std::optional<std::string> context;
};

/**
 * @brief Vocabulary service for concept lookups and mappings
 *
 * This service manages vocabulary data, provides concept lookups,
 * and handles source-to-concept mappings for the ETL pipeline.
 */
class VocabularyService {
public:
    /**
     * @brief Constructor
     * @param connection Database connection for vocabulary tables
     */
    explicit VocabularyService(std::unique_ptr<extract::IDatabaseConnection> connection);

    /**
     * @brief Initialize the service
     * @param cache_size Maximum number of cached concepts
     */
    void initialize(size_t cache_size = 10000);

    /**
     * @brief Load vocabulary mappings from configuration
     * @param mapping_config YAML configuration node
     */
    void load_mappings(const std::string& mapping_config);

    /**
     * @brief Load vocabulary mappings from database
     * @param mapping_table Name of the mapping table
     */
    void load_mappings_from_db(const std::string& mapping_table);

    /**
     * @brief Look up concept by ID
     * @param concept_id Concept ID
     * @return std::optional<Concept> Concept if found
     */
    [[nodiscard]] std::optional<Concept> get_concept(int concept_id);

    /**
     * @brief Look up concept by code and vocabulary
     * @param concept_code Concept code
     * @param vocabulary_id Vocabulary ID
     * @return std::optional<Concept> Concept if found
     */
    [[nodiscard]] std::optional<Concept> get_concept_by_code(
        const std::string& concept_code,
        const std::string& vocabulary_id);

    /**
     * @brief Map source value to concept ID
     * @param source_value Source value to map
     * @param vocabulary_name Vocabulary name for mapping
     * @param context Optional context for disambiguation
     * @return int Concept ID (0 if not found)
     */
    [[nodiscard]] int map_to_concept_id(
        const std::string& source_value,
        const std::string& vocabulary_name,
        const std::optional<std::string>& context = std::nullopt);

    /**
     * @brief Get all mappings for a source value
     * @param source_value Source value
     * @param vocabulary_name Vocabulary name
     * @return std::vector<VocabularyMapping> All matching mappings
     */
    [[nodiscard]] std::vector<VocabularyMapping> get_mappings(
        const std::string& source_value,
        const std::string& vocabulary_name);

    /**
     * @brief Find standard concept for a source concept
     * @param source_concept_id Source concept ID
     * @return int Standard concept ID (0 if not found)
     */
    [[nodiscard]] int get_standard_concept(int source_concept_id);

    /**
     * @brief Get descendant concepts
     * @param ancestor_concept_id Ancestor concept ID
     * @param max_levels Maximum levels of descendants (-1 for all)
     * @return std::vector<int> Descendant concept IDs
     */
    [[nodiscard]] std::vector<int> get_descendants(
        int ancestor_concept_id,
        int max_levels = -1);

    /**
     * @brief Get ancestor concepts
     * @param descendant_concept_id Descendant concept ID
     * @param max_levels Maximum levels of ancestors (-1 for all)
     * @return std::vector<int> Ancestor concept IDs
     */
    [[nodiscard]] std::vector<int> get_ancestors(
        int descendant_concept_id,
        int max_levels = -1);

    /**
     * @brief Check if concept is in domain
     * @param concept_id Concept ID
     * @param domain_id Domain ID
     * @return bool True if concept is in domain
     */
    [[nodiscard]] bool is_in_domain(int concept_id, const std::string& domain_id);

    /**
     * @brief Add custom mapping
     * @param mapping Vocabulary mapping to add
     */
    void add_mapping(const VocabularyMapping& mapping);

    /**
     * @brief Clear all cached data
     */
    void clear_cache();

    /**
     * @brief Get cache statistics
     * @return Cache hit rate, size, etc.
     */
    struct CacheStats {
        size_t cache_size;
        size_t max_cache_size;
        size_t hits;
        size_t misses;
        double hit_rate;
    };

    [[nodiscard]] CacheStats get_cache_stats() const;

    /**
     * @brief Validate vocabulary tables exist
     * @return bool True if all required tables exist
     */
    [[nodiscard]] bool validate_vocabulary_tables();

    /**
     * @brief Get vocabulary version information
     * @return std::string Version string
     */
    [[nodiscard]] std::string get_vocabulary_version();

private:
    /**
     * @brief Load concept from database
     * @param concept_id Concept ID
     * @return std::optional<Concept> Concept if found
     */
    std::optional<Concept> load_concept_from_db(int concept_id);

    /**
     * @brief Load concept by code from database
     * @param concept_code Concept code
     * @param vocabulary_id Vocabulary ID
     * @return std::optional<Concept> Concept if found
     */
    std::optional<Concept> load_concept_by_code_from_db(
        const std::string& concept_code,
        const std::string& vocabulary_id);

    /**
     * @brief Normalize source value for matching
     * @param value Source value
     * @param case_sensitive Whether to preserve case
     * @return std::string Normalized value
     */
    std::string normalize_value(const std::string& value, bool case_sensitive) const;

    /**
     * @brief Build mapping key
     * @param source_value Source value
     * @param vocabulary_name Vocabulary name
     * @param context Optional context
     * @return std::string Mapping key
     */
    std::string build_mapping_key(
        const std::string& source_value,
        const std::string& vocabulary_name,
        const std::optional<std::string>& context) const;

    // Database connection
    std::unique_ptr<extract::IDatabaseConnection> connection_;

    // Caches
    std::unordered_map<int, Concept> concept_cache_;
    std::unordered_map<std::string, int> code_to_concept_cache_;
    std::unordered_map<std::string, int> mapping_cache_;
    std::unordered_map<int, int> standard_concept_cache_;

    // Cache management
    mutable std::shared_mutex cache_mutex_;
    size_t max_cache_size_{10000};
    mutable size_t cache_hits_{0};
    mutable size_t cache_misses_{0};

    // Vocabulary mappings
    std::unordered_map<std::string, std::vector<VocabularyMapping>> vocabulary_mappings_;
    mutable std::shared_mutex mapping_mutex_;

    // Configuration
    bool case_sensitive_matching_{false};
    std::string vocabulary_schema_{"cdm"};
};

/**
 * @brief Vocabulary-based validator
 *
 * Validates that values can be mapped to valid OMOP concepts.
 */
class VocabularyValidator {
public:
    /**
     * @brief Constructor
     * @param vocabulary_service Reference to vocabulary service
     */
    explicit VocabularyValidator(VocabularyService& vocabulary_service)
        : vocabulary_service_(vocabulary_service) {}

    /**
     * @brief Validate concept ID
     * @param concept_id Concept ID to validate
     * @param expected_domain Expected domain (optional)
     * @return bool True if valid
     */
    [[nodiscard]] bool validate_concept_id(
        int concept_id,
        const std::optional<std::string>& expected_domain = std::nullopt);

    /**
     * @brief Validate source value can be mapped
     * @param source_value Source value
     * @param vocabulary_name Vocabulary name
     * @return bool True if can be mapped
     */
    [[nodiscard]] bool validate_mapping_exists(
        const std::string& source_value,
        const std::string& vocabulary_name);

    /**
     * @brief Validate concept is standard
     * @param concept_id Concept ID
     * @return bool True if standard concept
     */
    [[nodiscard]] bool validate_standard_concept(int concept_id);

    /**
     * @brief Get validation errors for a value
     * @param source_value Source value
     * @param vocabulary_name Vocabulary name
     * @param expected_domain Expected domain
     * @return std::vector<std::string> List of validation errors
     */
    [[nodiscard]] std::vector<std::string> get_validation_errors(
        const std::string& source_value,
        const std::string& vocabulary_name,
        const std::optional<std::string>& expected_domain = std::nullopt);

private:
    VocabularyService& vocabulary_service_;
};

/**
 * @brief Singleton accessor for global vocabulary service
 */
class VocabularyServiceManager {
public:
    /**
     * @brief Get the singleton instance
     * @return VocabularyService& Reference to the vocabulary service
     */
    [[nodiscard]] static VocabularyService& instance() {
        if (!instance_) {
            throw common::ConfigurationException(
                "VocabularyService not initialized. Call initialize() first.");
        }
        return *instance_;
    }

    /**
     * @brief Initialize the vocabulary service
     * @param connection Database connection
     * @param cache_size Cache size
     */
    static void initialize(
        std::unique_ptr<extract::IDatabaseConnection> connection,
        size_t cache_size = 10000) {
        instance_ = std::make_unique<VocabularyService>(std::move(connection));
        instance_->initialize(cache_size);
    }

    /**
     * @brief Check if initialized
     * @return bool True if initialized
     */
    [[nodiscard]] static bool is_initialized() {
        return instance_ != nullptr;
    }

    /**
     * @brief Reset the service
     */
    static void reset() {
        instance_.reset();
    }

private:
    static std::unique_ptr<VocabularyService> instance_;
};

} // namespace omop::transform