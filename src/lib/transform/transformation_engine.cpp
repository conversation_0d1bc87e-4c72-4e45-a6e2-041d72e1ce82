#include "transform/transformation_engine.h"
#include "transform/vocabulary_service.h"
#include "common/logging.h"
#include <regex>
#include <iomanip>
#include <sstream>

namespace omop::transform {

// Date Transformation Implementation
std::any DateTransformation::transform(const std::any& input,
                                      core::ProcessingContext& context) {
    if (!validate_input(input)) {
        throw common::TransformationException("Invalid input for date transformation",
                                            "date_field", "date_transform");
    }

    try {
        std::string date_str;

        // Handle different input types
        if (input.type() == typeid(std::string)) {
            date_str = std::any_cast<std::string>(input);
        } else if (input.type() == typeid(std::chrono::system_clock::time_point)) {
            // Already a time_point, convert to string first if format change needed
            auto tp = std::any_cast<std::chrono::system_clock::time_point>(input);
            auto time_t = std::chrono::system_clock::to_time_t(tp);
            std::tm tm = *std::localtime(&time_t);

            std::ostringstream oss;
            oss << std::put_time(&tm, output_format_.c_str());
            return oss.str();
        } else {
            throw common::TransformationException("Unsupported input type for date transformation",
                                                "date_field", "date_transform");
        }

        // Parse input date
        std::tm tm = {};
        std::istringstream iss(date_str);
        iss >> std::get_time(&tm, input_format_.c_str());

        if (iss.fail()) {
            throw common::TransformationException(
                std::format("Failed to parse date '{}' with format '{}'", date_str, input_format_),
                "date_field", "date_transform");
        }

        // Add time if needed
        if (add_time_ && tm.tm_hour == 0 && tm.tm_min == 0 && tm.tm_sec == 0) {
            // Parse default time
            std::tm default_tm = {};
            std::istringstream time_iss(default_time_);
            time_iss >> std::get_time(&default_tm, "%H:%M:%S");

            tm.tm_hour = default_tm.tm_hour;
            tm.tm_min = default_tm.tm_min;
            tm.tm_sec = default_tm.tm_sec;
        }

        // Convert to time_point
        auto time_t = std::mktime(&tm);
        auto time_point = std::chrono::system_clock::from_time_t(time_t);

        // Return as formatted string or time_point based on configuration
        if (output_format_.find("%") != std::string::npos) {
            std::ostringstream oss;
            oss << std::put_time(&tm, output_format_.c_str());
            return oss.str();
        } else {
            return time_point;
        }

    } catch (const common::TransformationException&) {
        throw;
    } catch (const std::exception& e) {
        context.increment_errors();
        throw common::TransformationException(
            std::format("Date transformation failed: {}", e.what()),
            "date_field", "date_transform");
    }
}

bool DateTransformation::validate_input(const std::any& input) const {
    if (!input.has_value()) return false;

    return input.type() == typeid(std::string) ||
           input.type() == typeid(std::chrono::system_clock::time_point);
}

void DateTransformation::configure(const YAML::Node& params) {
    if (params["format"]) {
        input_format_ = params["format"].as<std::string>();
    }
    if (params["output_format"]) {
        output_format_ = params["output_format"].as<std::string>();
    }
    if (params["timezone"]) {
        timezone_ = params["timezone"].as<std::string>();
    }
    if (params["add_time"]) {
        add_time_ = params["add_time"].as<bool>();
    }
    if (params["default_time"]) {
        default_time_ = params["default_time"].as<std::string>();
    }
}

// Vocabulary Transformation Implementation
VocabularyTransformation::VocabularyTransformation(VocabularyService& vocabulary_service)
    : vocabulary_service_(vocabulary_service) {}

std::any VocabularyTransformation::transform(const std::any& input,
                                            core::ProcessingContext& context) {
    if (!validate_input(input)) {
        return default_concept_id_;
    }

    try {
        std::string value;

        if (input.type() == typeid(std::string)) {
            value = std::any_cast<std::string>(input);
        } else if (input.type() == typeid(const char*)) {
            value = std::any_cast<const char*>(input);
        } else {
            // Try to convert to string
            return default_concept_id_;
        }

        // Apply case sensitivity
        if (!case_sensitive_) {
            std::transform(value.begin(), value.end(), value.begin(), ::toupper);
        }

        // Look up concept ID
        int concept_id = vocabulary_service_.map_to_concept_id(
            value, vocabulary_name_);

        if (concept_id == 0) {
            context.log("warning", std::format("No mapping found for value '{}' in vocabulary '{}'",
                                             value, vocabulary_name_));
            return default_concept_id_;
        }

        return concept_id;

    } catch (const std::exception& e) {
        context.increment_errors();
        throw common::TransformationException(
            std::format("Vocabulary transformation failed: {}", e.what()),
            vocabulary_name_, "vocabulary_mapping");
    }
}

bool VocabularyTransformation::validate_input(const std::any& input) const {
    if (!input.has_value()) return false;

    return input.type() == typeid(std::string) ||
           input.type() == typeid(const char*);
}

void VocabularyTransformation::configure(const YAML::Node& params) {
    if (!params["vocabulary"]) {
        throw common::TransformationException(
            "Vocabulary transformation requires 'vocabulary' parameter",
            "", "vocabulary_mapping");
    }

    vocabulary_name_ = params["vocabulary"].as<std::string>();

    if (params["source_vocabulary"]) {
        source_vocabulary_ = params["source_vocabulary"].as<std::string>();
    }
    if (params["target_vocabulary"]) {
        target_vocabulary_ = params["target_vocabulary"].as<std::string>();
    }
    if (params["default_value"]) {
        default_concept_id_ = params["default_value"].as<int>();
    }
    if (params["case_sensitive"]) {
        case_sensitive_ = params["case_sensitive"].as<bool>();
    }
}

// Numeric Transformation Implementation
std::any NumericTransformation::transform(const std::any& input,
                                         core::ProcessingContext& context) {
    if (!validate_input(input)) {
        throw common::TransformationException("Invalid input for numeric transformation",
                                            "numeric_field", "numeric_transform");
    }

    try {
        double value = 0.0;

        // Convert input to double
        if (input.type() == typeid(double)) {
            value = std::any_cast<double>(input);
        } else if (input.type() == typeid(float)) {
            value = static_cast<double>(std::any_cast<float>(input));
        } else if (input.type() == typeid(int)) {
            value = static_cast<double>(std::any_cast<int>(input));
        } else if (input.type() == typeid(int64_t)) {
            value = static_cast<double>(std::any_cast<int64_t>(input));
        } else if (input.type() == typeid(std::string)) {
            value = std::stod(std::any_cast<std::string>(input));
        } else {
            throw common::TransformationException("Unsupported numeric type",
                                                "numeric_field", "numeric_transform");
        }

        // Apply operation
        switch (operation_) {
            case Operation::Multiply:
                value *= operand_;
                break;
            case Operation::Divide:
                if (operand_ == 0) {
                    throw common::TransformationException("Division by zero",
                                                        "numeric_field", "numeric_transform");
                }
                value /= operand_;
                break;
            case Operation::Add:
                value += operand_;
                break;
            case Operation::Subtract:
                value -= operand_;
                break;
            case Operation::Round:
                value = std::round(value * std::pow(10, precision_)) / std::pow(10, precision_);
                break;
            case Operation::Floor:
                value = std::floor(value);
                break;
            case Operation::Ceiling:
                value = std::ceil(value);
                break;
            case Operation::Absolute:
                value = std::abs(value);
                break;
            default:
                break;
        }

        // Apply constraints
        if (min_value_ && value < *min_value_) {
            value = *min_value_;
        }
        if (max_value_ && value > *max_value_) {
            value = *max_value_;
        }

        return value;

    } catch (const std::exception& e) {
        context.increment_errors();
        throw common::TransformationException(
            std::format("Numeric transformation failed: {}", e.what()),
            "numeric_field", "numeric_transform");
    }
}

bool NumericTransformation::validate_input(const std::any& input) const {
    if (!input.has_value()) return false;

    return input.type() == typeid(double) ||
           input.type() == typeid(float) ||
           input.type() == typeid(int) ||
           input.type() == typeid(int64_t) ||
           input.type() == typeid(std::string);
}

void NumericTransformation::configure(const YAML::Node& params) {
    if (params["operation"]) {
        static const std::unordered_map<std::string, Operation> op_map = {
            {"multiply", Operation::Multiply},
            {"divide", Operation::Divide},
            {"add", Operation::Add},
            {"subtract", Operation::Subtract},
            {"round", Operation::Round},
            {"floor", Operation::Floor},
            {"ceiling", Operation::Ceiling},
            {"absolute", Operation::Absolute}
        };

        std::string op_str = params["operation"].as<std::string>();
        auto it = op_map.find(op_str);
        if (it != op_map.end()) {
            operation_ = it->second;
        }
    }

    if (params["operand"]) {
        operand_ = params["operand"].as<double>();
    }
    if (params["precision"]) {
        precision_ = params["precision"].as<int>();
    }
    if (params["min_value"]) {
        min_value_ = params["min_value"].as<double>();
    }
    if (params["max_value"]) {
        max_value_ = params["max_value"].as<double>();
    }
    if (params["unit_conversion"]) {
        unit_conversion_ = params["unit_conversion"].as<std::string>();
    }
}

// String Concatenation Implementation
std::any StringConcatenationTransformation::transform(const std::any& input,
                                                     core::ProcessingContext& context) {
    // Single field version - just return as string
    if (!input.has_value()) {
        return std::string{};
    }

    if (input.type() == typeid(std::string)) {
        return input;
    }

    // Convert to string
    return std::string{"[converted]"};
}

std::any StringConcatenationTransformation::transform_multiple(
    const std::unordered_map<std::string, std::any>& values,
    core::ProcessingContext& context) {

    std::ostringstream result;
    bool first = true;

    for (const auto& field : source_fields_) {
        auto it = values.find(field);
        if (it == values.end() || !it->second.has_value()) {
            if (!skip_empty_) {
                if (!first) result << separator_;
                first = false;
            }
            continue;
        }

        std::string value;

        // Convert to string
        if (it->second.type() == typeid(std::string)) {
            value = std::any_cast<std::string>(it->second);
        } else if (it->second.type() == typeid(const char*)) {
            value = std::any_cast<const char*>(it->second);
        } else if (it->second.type() == typeid(int)) {
            value = std::to_string(std::any_cast<int>(it->second));
        } else if (it->second.type() == typeid(int64_t)) {
            value = std::to_string(std::any_cast<int64_t>(it->second));
        } else if (it->second.type() == typeid(double)) {
            value = std::to_string(std::any_cast<double>(it->second));
        } else {
            value = "[unknown]";
        }

        if (skip_empty_ && value.empty()) {
            continue;
        }

        if (!first) {
            result << separator_;
        }
        result << value;
        first = false;
    }

    return prefix_ + result.str() + suffix_;
}

bool StringConcatenationTransformation::validate_input(const std::any& input) const {
    return true; // String concatenation can handle any input
}

void StringConcatenationTransformation::configure(const YAML::Node& params) {
    if (params["separator"]) {
        separator_ = params["separator"].as<std::string>();
    }
    if (params["skip_empty"]) {
        skip_empty_ = params["skip_empty"].as<bool>();
    }
    if (params["prefix"]) {
        prefix_ = params["prefix"].as<std::string>();
    }
    if (params["suffix"]) {
        suffix_ = params["suffix"].as<std::string>();
    }
    if (params["source_fields"]) {
        source_fields_ = params["source_fields"].as<std::vector<std::string>>();
    }
}

// Conditional Transformation Implementation
std::any ConditionalTransformation::transform(const std::any& input,
                                             core::ProcessingContext& context) {
    for (const auto& condition : conditions_) {
        if (evaluate_condition(condition, input)) {
            return condition.then_value;
        } else if (condition.else_value) {
            return *condition.else_value;
        }
    }

    return default_value_;
}

bool ConditionalTransformation::evaluate_condition(const Condition& condition,
                                                  const std::any& value) const {
    if (!value.has_value()) {
        return condition.operator_type == "is_null";
    }

    if (condition.operator_type == "is_not_null") {
        return true;
    }

    // String comparisons
    if (value.type() == typeid(std::string) && condition.value.type() == typeid(std::string)) {
        std::string val = std::any_cast<std::string>(value);
        std::string cond_val = std::any_cast<std::string>(condition.value);

        if (condition.operator_type == "equals" || condition.operator_type == "==") {
            return val == cond_val;
        } else if (condition.operator_type == "not_equals" || condition.operator_type == "!=") {
            return val != cond_val;
        } else if (condition.operator_type == "contains") {
            return val.find(cond_val) != std::string::npos;
        } else if (condition.operator_type == "starts_with") {
            return val.starts_with(cond_val);
        } else if (condition.operator_type == "ends_with") {
            return val.ends_with(cond_val);
        } else if (condition.operator_type == "matches") {
            std::regex pattern(cond_val);
            return std::regex_match(val, pattern);
        }
    }

    // Numeric comparisons
    try {
        double val = 0.0;
        double cond_val = 0.0;

        // Convert value to double
        if (value.type() == typeid(double)) {
            val = std::any_cast<double>(value);
        } else if (value.type() == typeid(int)) {
            val = static_cast<double>(std::any_cast<int>(value));
        } else if (value.type() == typeid(int64_t)) {
            val = static_cast<double>(std::any_cast<int64_t>(value));
        }

        // Convert condition value to double
        if (condition.value.type() == typeid(double)) {
            cond_val = std::any_cast<double>(condition.value);
        } else if (condition.value.type() == typeid(int)) {
            cond_val = static_cast<double>(std::any_cast<int>(condition.value));
        }

        if (condition.operator_type == ">" || condition.operator_type == "greater_than") {
            return val > cond_val;
        } else if (condition.operator_type == ">=" || condition.operator_type == "greater_than_or_equal") {
            return val >= cond_val;
        } else if (condition.operator_type == "<" || condition.operator_type == "less_than") {
            return val < cond_val;
        } else if (condition.operator_type == "<=" || condition.operator_type == "less_than_or_equal") {
            return val <= cond_val;
        } else if (condition.operator_type == "==" || condition.operator_type == "equals") {
            return std::abs(val - cond_val) < 0.0001; // Floating point comparison
        } else if (condition.operator_type == "!=" || condition.operator_type == "not_equals") {
            return std::abs(val - cond_val) >= 0.0001;
        }
    } catch (...) {
        // Not numeric comparison
    }

    return false;
}

bool ConditionalTransformation::validate_input(const std::any& input) const {
    return true; // Conditional can handle any input
}

void ConditionalTransformation::configure(const YAML::Node& params) {
    if (params["conditions"]) {
        for (const auto& cond_node : params["conditions"]) {
            Condition condition;

            if (cond_node["field"]) {
                condition.field = cond_node["field"].as<std::string>();
            }

            if (cond_node["if"]) {
                // Parse condition from "if" string
                std::string if_expr = cond_node["if"].as<std::string>();
                // Simple parser for expressions like "field == value"
                // TODO: Implement proper expression parser
            }

            if (cond_node["operator"]) {
                condition.operator_type = cond_node["operator"].as<std::string>();
            }

            if (cond_node["value"]) {
                // Store as appropriate type
                condition.value = cond_node["value"];
            }

            if (cond_node["then"]) {
                condition.then_value = cond_node["then"].as<std::string>();
            }

            if (cond_node["else"]) {
                condition.else_value = cond_node["else"].as<std::string>();
            }

            conditions_.push_back(condition);
        }
    }

    if (params["default"]) {
        default_value_ = params["default"].as<std::string>();
    }
}

// Transformation Engine Implementation
TransformationEngine::TransformationEngine() {
    auto logger = common::Logger::get("omop-etl-transform");

    // Register default transformations
    register_transformation("direct",
        []() { return std::make_unique<DirectTransformation>(); });

    register_transformation("date_transform",
        []() { return std::make_unique<DateTransformation>(); });

    register_transformation("numeric_transform",
        []() { return std::make_unique<NumericTransformation>(); });

    register_transformation("string_concatenation",
        []() { return std::make_unique<StringConcatenationTransformation>(); });

    register_transformation("conditional",
        []() { return std::make_unique<ConditionalTransformation>(); });

    // Note: vocabulary_mapping requires special handling due to dependency
}

void TransformationEngine::initialize(
    const std::unordered_map<std::string, std::any>& config,
    core::ProcessingContext& context) {

    auto logger = common::Logger::get("omop-etl-transform");
    logger->info("Initializing transformation engine");

    // Get table name from config
    if (config.find("table_name") != config.end()) {
        std::string table_name = std::any_cast<std::string>(config.at("table_name"));

        // Load table mapping
        auto& config_mgr = common::Config::instance();
        auto mapping = config_mgr.get_table_mapping(table_name);

        if (!mapping) {
            throw common::ConfigurationException(
                std::format("No mapping found for table '{}'", table_name));
        }

        current_mapping_ = *mapping;
    }

    // Initialize vocabulary service if needed
    if (!VocabularyServiceManager::is_initialized()) {
        // This should be initialized by the pipeline
        logger->warn("Vocabulary service not initialized");
    } else {
        // Vocabulary service is available through VocabularyServiceManager

        // Register vocabulary transformation with service reference
        register_transformation("vocabulary_mapping",
            []() {
                auto& vocab_service = VocabularyServiceManager::instance();
                return std::make_unique<VocabularyTransformation>(vocab_service);
            });
    }

    logger->info("Transformation engine initialized for table: {}",
                current_mapping_.target_table());
}

std::optional<core::Record> TransformationEngine::transform(
    const core::Record& record,
    core::ProcessingContext& context) {

    try {
        // Apply filters first
        // Apply filters (simplified - convert YAML to string)
        std::string filters_str = current_mapping_.filters().as<std::string>();
        if (!apply_filters(record, filters_str)) {
            records_filtered_++;
            return std::nullopt;
        }

        // Apply transformations
        auto transformed = apply_transformations(record,
                                               current_mapping_.transformations(),
                                               context);

        // Validate if enabled
        // Simplified validation check - would need proper configuration access
        bool validate_records = true; // Default value
        if (validate_records) {
            auto validation_result = apply_validations(transformed,
                                                      current_mapping_.validations());
            if (!validation_result.is_valid()) {
                validation_errors_++;

                // Log validation errors
                for (const auto& error : validation_result.errors()) {
                    context.log("warning",
                        std::format("Validation error for field '{}': {}",
                                  error.field_name, error.error_message));
                }

                // Depending on configuration, may still return the record
                bool strict_validation = false; // Default value - would need proper configuration access
                if (strict_validation) {
                    return std::nullopt;
                }
            }
        }

        records_transformed_++;
        return transformed;

    } catch (const std::exception& e) {
        transformation_errors_++;
        throw std::runtime_error(
            std::format("Record transformation failed: {}", e.what()));
    }
}

core::RecordBatch TransformationEngine::transform_batch(
    const core::RecordBatch& batch,
    core::ProcessingContext& context) {

    auto start_time = std::chrono::steady_clock::now();
    core::RecordBatch transformed_batch;
    transformed_batch.reserve(batch.size());

    for (const auto& record : batch) {
        try {
            auto result = transform(record, context);
            if (result) {
                transformed_batch.addRecord(std::move(*result));
            }
        } catch (const std::exception& e) {
            // Log error but continue with batch
            context.log("error",
                std::format("Failed to transform record: {}", e.what()));
            context.increment_errors();
        }
    }

    auto duration = std::chrono::steady_clock::now() - start_time;
    {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        total_transform_time_ += duration;
    }

    return transformed_batch;
}

core::Record TransformationEngine::apply_transformations(
    const core::Record& record,
    const std::vector<common::TransformationRule>& rules,
    core::ProcessingContext& context) {

    core::Record transformed;

    for (const auto& rule : rules) {
        try {
            auto transformation = create_transformation(rule);

            if (rule.is_multi_column()) {
                // Multi-column transformation
                std::unordered_map<std::string, std::any> values;
                for (const auto& col : rule.source_columns()) {
                    try {
                        values[col] = record.getField(col);
                    } catch (...) {
                        values[col] = std::any{};
                    }
                }

                if (auto* concat = dynamic_cast<StringConcatenationTransformation*>(
                        transformation.get())) {
                    auto result = concat->transform_multiple(values, context);
                    transformed.setField(std::string(rule.target_column()), result);
                }
            } else {
                // Single column transformation
                try {
                    auto value = record.getField(std::string(rule.source_column()));
                    if (value.type() != typeid(void)) {
                        auto result = transformation->transform(value, context);
                        transformed.setField(std::string(rule.target_column()), result);

                        // Also store source value if different column
                        if (rule.source_column() != rule.target_column()) {
                            transformed.setField(std::string(rule.source_column()) + "_source", value);
                        }
                    }
                } catch (...) {
                    // Field doesn't exist, skip
                }
            }
        } catch (const std::exception& e) {
            throw std::runtime_error(
                std::format("Transformation failed for rule '{}': {}",
                          rule.target_column(), e.what()));
        }
    }

    // Copy any unmapped fields
    for (const auto& field : record.getFieldNames()) {
        if (!transformed.hasField(field)) {
            try {
                auto value = record.getField(field);
                if (value.type() != typeid(void)) {
                    transformed.setField(field, value);
                }
            } catch (...) {
                // Field doesn't exist, skip
            }
        }
    }

    return transformed;
}

std::unique_ptr<FieldTransformation> TransformationEngine::create_transformation(
    const common::TransformationRule& rule) {

    std::string type_name;
    switch (rule.type()) {
        case common::TransformationRule::Type::Direct:
            type_name = "direct";
            break;
        case common::TransformationRule::Type::DateTransform:
            type_name = "date_transform";
            break;
        case common::TransformationRule::Type::VocabularyMapping:
            type_name = "vocabulary_mapping";
            break;
        case common::TransformationRule::Type::DateCalculation:
            type_name = "date_calculation";
            break;
        case common::TransformationRule::Type::NumericTransform:
            type_name = "numeric_transform";
            break;
        case common::TransformationRule::Type::StringConcatenation:
            type_name = "string_concatenation";
            break;
        case common::TransformationRule::Type::Conditional:
            type_name = "conditional";
            break;
        case common::TransformationRule::Type::Custom:
            type_name = "custom";
            break;
    }

    auto it = transformation_factories_.find(type_name);
    if (it == transformation_factories_.end()) {
        throw std::runtime_error(
            std::format("Unknown transformation type: '{}'", type_name));
    }

    auto transformation = it->second();
    transformation->configure(rule.parameters());

    return transformation;
}

bool TransformationEngine::apply_filters(const core::Record& record,
                                        const std::string& filters) const {
    // Simplified filter implementation - would need proper YAML parsing
    return true; // Accept all records for now
}

void TransformationEngine::register_transformation(
    const std::string& type,
    std::function<std::unique_ptr<FieldTransformation>()> factory) {
    transformation_factories_[type] = std::move(factory);
}

} // namespace omop::transform