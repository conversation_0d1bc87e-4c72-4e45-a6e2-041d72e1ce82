#include "transform/vocabulary_service.h"
#include "common/logging.h"
#include <algorithm>
#include <cctype>

namespace omop::transform {

// Static member initialization
std::unique_ptr<VocabularyService> VocabularyServiceManager::instance_;

// VocabularyService Implementation
VocabularyService::VocabularyService(std::unique_ptr<extract::IDatabaseConnection> connection)
    : connection_(std::move(connection)) {

    if (!connection_) {
        throw common::ConfigurationException("VocabularyService requires a database connection");
    }
}

void VocabularyService::initialize(size_t cache_size) {
    auto logger = common::Logger::get("omop-vocabulary");
    logger->info("Initializing vocabulary service with cache size: {}", cache_size);

    max_cache_size_ = cache_size;

    // Validate vocabulary tables exist
    if (!validate_vocabulary_tables()) {
        throw common::ConfigurationException(
            "Required vocabulary tables not found in database");
    }

    // Get vocabulary version
    std::string version = get_vocabulary_version();
    logger->info("Vocabulary version: {}", version);

    // Pre-load common vocabularies
    // This is optional but can improve performance
    try {
        std::string query = R"(
            SELECT concept_id, concept_name, domain_id, vocabulary_id,
                   concept_class_id, concept_code, standard_concept
            FROM concept
            WHERE vocabulary_id IN ('Gender', 'Race', 'Ethnicity', 'Visit')
              AND invalid_reason IS NULL
        )";

        auto result = connection_->execute_query(query);
        while (result->next()) {
            Concept concept_obj(
                std::any_cast<int>(result->get_value(0)),
                std::any_cast<std::string>(result->get_value(1)),
                std::any_cast<std::string>(result->get_value(2)),
                std::any_cast<std::string>(result->get_value(3)),
                std::any_cast<std::string>(result->get_value(4)),
                std::any_cast<std::string>(result->get_value(5))
            );

            if (!result->is_null(6)) {
                concept_obj.set_standard_concept(std::any_cast<std::string>(result->get_value(6)));
            }

            std::unique_lock<std::shared_mutex> lock(cache_mutex_);
            concept_cache_[concept_obj.concept_id()] = concept_obj;

            // Also cache by code
            std::string cache_key = concept_obj.vocabulary_id() + ":" + concept_obj.concept_code();
            code_to_concept_cache_[cache_key] = concept_obj.concept_id();
        }

        logger->info("Pre-loaded {} concepts into cache", concept_cache_.size());

    } catch (const std::exception& e) {
        logger->warn("Failed to pre-load vocabularies: {}", e.what());
    }
}

void VocabularyService::load_mappings(const std::string& mapping_config) {
    auto logger = common::Logger::get("omop-vocabulary");
    logger->info("Loading vocabulary mappings from configuration");

    std::unique_lock<std::shared_mutex> lock(mapping_mutex_);

    // Simplified implementation - would need proper YAML parsing
    logger->info("Vocabulary mapping loading simplified for compilation: {}", mapping_config);
}

void VocabularyService::load_mappings_from_db(const std::string& mapping_table) {
    auto logger = common::Logger::get("omop-vocabulary");
    logger->info("Loading vocabulary mappings from table: {}", mapping_table);

    try {
        std::string query = std::format(R"(
            SELECT source_value, source_vocabulary, target_concept_id,
                   target_vocabulary, mapping_type, confidence, context
            FROM {}
            WHERE valid_end_date IS NULL OR valid_end_date > CURRENT_DATE
        )", mapping_table);

        auto result = connection_->execute_query(query);
        int count = 0;

        while (result->next()) {
            VocabularyMapping vm;
            vm.source_value = std::any_cast<std::string>(result->get_value(0));
            vm.source_vocabulary = std::any_cast<std::string>(result->get_value(1));
            vm.target_concept_id = std::any_cast<int>(result->get_value(2));
            vm.target_vocabulary = std::any_cast<std::string>(result->get_value(3));
            vm.mapping_type = std::any_cast<std::string>(result->get_value(4));
            vm.mapping_confidence = std::any_cast<float>(result->get_value(5));

            if (!result->is_null(6)) {
                vm.context = std::any_cast<std::string>(result->get_value(6));
            }

            std::unique_lock<std::shared_mutex> lock(mapping_mutex_);
            vocabulary_mappings_[vm.source_vocabulary].push_back(vm);

            // Update cache
            std::string key = build_mapping_key(vm.source_value, vm.source_vocabulary, vm.context);
            mapping_cache_[key] = vm.target_concept_id;

            count++;
        }

        logger->info("Loaded {} vocabulary mappings from database", count);

    } catch (const std::exception& e) {
        throw common::DatabaseException(
            std::format("Failed to load vocabulary mappings: {}", e.what()),
            "vocabulary", 0);
    }
}

std::optional<Concept> VocabularyService::get_concept(int concept_id) {
    // Check cache first
    {
        std::shared_lock<std::shared_mutex> lock(cache_mutex_);
        auto it = concept_cache_.find(concept_id);
        if (it != concept_cache_.end()) {
            cache_hits_++;
            return it->second;
        }
    }

    cache_misses_++;

    // Load from database
    auto concept_result = load_concept_from_db(concept_id);

    if (concept_result) {
        // Add to cache if there's room
        std::unique_lock<std::shared_mutex> lock(cache_mutex_);
        if (concept_cache_.size() < max_cache_size_) {
            concept_cache_[concept_id] = *concept_result;
        }
    }

    return concept_result;
}

std::optional<Concept> VocabularyService::get_concept_by_code(
    const std::string& concept_code,
    const std::string& vocabulary_id) {

    std::string cache_key = vocabulary_id + ":" + concept_code;

    // Check cache first
    {
        std::shared_lock<std::shared_mutex> lock(cache_mutex_);
        auto it = code_to_concept_cache_.find(cache_key);
        if (it != code_to_concept_cache_.end()) {
            cache_hits_++;
            return get_concept(it->second);
        }
    }

    cache_misses_++;

    // Load from database
    auto concept_result = load_concept_by_code_from_db(concept_code, vocabulary_id);

    if (concept_result) {
        // Add to cache
        std::unique_lock<std::shared_mutex> lock(cache_mutex_);
        if (code_to_concept_cache_.size() < max_cache_size_) {
            code_to_concept_cache_[cache_key] = concept_result->concept_id();
            concept_cache_[concept_result->concept_id()] = *concept_result;
        }
    }

    return concept_result;
}

int VocabularyService::map_to_concept_id(
    const std::string& source_value,
    const std::string& vocabulary_name,
    const std::optional<std::string>& context) {

    // Normalize value
    std::string normalized = normalize_value(source_value, case_sensitive_matching_);

    // Check mapping cache
    std::string key = build_mapping_key(normalized, vocabulary_name, context);

    {
        std::shared_lock<std::shared_mutex> lock(cache_mutex_);
        auto it = mapping_cache_.find(key);
        if (it != mapping_cache_.end()) {
            return it->second;
        }
    }

    // Check vocabulary mappings
    {
        std::shared_lock<std::shared_mutex> lock(mapping_mutex_);
        auto it = vocabulary_mappings_.find(vocabulary_name);
        if (it != vocabulary_mappings_.end()) {
            for (const auto& mapping : it->second) {
                std::string map_value = normalize_value(mapping.source_value, case_sensitive_matching_);
                if (map_value == normalized) {
                    // Check context if specified
                    if (context && mapping.context && *context != *mapping.context) {
                        continue;
                    }

                    // Cache the result
                    {
                        std::unique_lock<std::shared_mutex> lock2(cache_mutex_);
                        mapping_cache_[key] = mapping.target_concept_id;
                    }

                    return mapping.target_concept_id;
                }
            }
        }
    }

    // Try database lookup by concept code
    auto concept_result = get_concept_by_code(source_value, vocabulary_name);
    if (concept_result) {
        // If it's not a standard concept, try to find the standard one
        if (!concept_result->is_standard()) {
            int standard_id = get_standard_concept(concept_result->concept_id());
            if (standard_id != 0) {
                return standard_id;
            }
        }
        return concept_result->concept_id();
    }

    // No mapping found
    return 0;
}

std::vector<VocabularyMapping> VocabularyService::get_mappings(
    const std::string& source_value,
    const std::string& vocabulary_name) {

    std::vector<VocabularyMapping> results;
    std::string normalized = normalize_value(source_value, case_sensitive_matching_);

    std::shared_lock<std::shared_mutex> lock(mapping_mutex_);
    auto it = vocabulary_mappings_.find(vocabulary_name);
    if (it != vocabulary_mappings_.end()) {
        for (const auto& mapping : it->second) {
            std::string map_value = normalize_value(mapping.source_value, case_sensitive_matching_);
            if (map_value == normalized) {
                results.push_back(mapping);
            }
        }
    }

    return results;
}

int VocabularyService::get_standard_concept(int source_concept_id) {
    // Check cache
    {
        std::shared_lock<std::shared_mutex> lock(cache_mutex_);
        auto it = standard_concept_cache_.find(source_concept_id);
        if (it != standard_concept_cache_.end()) {
            return it->second;
        }
    }

    // Query database for standard concept
    try {
        std::string query = R"(
            SELECT concept_id_2
            FROM concept_relationship
            WHERE concept_id_1 = $1
              AND relationship_id = 'Maps to'
              AND invalid_reason IS NULL
        )";

        auto stmt = connection_->prepare_statement(query);
        stmt->bind(1, source_concept_id);
        auto result = stmt->execute_query();

        if (result->next()) {
            int standard_id = std::any_cast<int>(result->get_value(0));

            // Cache the result
            std::unique_lock<std::shared_mutex> lock(cache_mutex_);
            standard_concept_cache_[source_concept_id] = standard_id;

            return standard_id;
        }
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("omop-vocabulary");
        logger->error("Failed to get standard concept for {}: {}", source_concept_id, e.what());
    }

    return 0;
}

std::vector<int> VocabularyService::get_descendants(int ancestor_concept_id, int max_levels) {
    std::vector<int> descendants;

    try {
        std::string query;
        if (max_levels < 0) {
            query = R"(
                SELECT descendant_concept_id
                FROM concept_ancestor
                WHERE ancestor_concept_id = $1
                  AND ancestor_concept_id != descendant_concept_id
            )";
        } else {
            query = R"(
                SELECT descendant_concept_id
                FROM concept_ancestor
                WHERE ancestor_concept_id = $1
                  AND ancestor_concept_id != descendant_concept_id
                  AND max_levels_of_separation <= $2
            )";
        }

        auto stmt = connection_->prepare_statement(query);
        stmt->bind(1, ancestor_concept_id);
        if (max_levels >= 0) {
            stmt->bind(2, max_levels);
        }

        auto result = stmt->execute_query();
        while (result->next()) {
            descendants.push_back(std::any_cast<int>(result->get_value(0)));
        }

    } catch (const std::exception& e) {
        auto logger = common::Logger::get("omop-vocabulary");
        logger->error("Failed to get descendants for {}: {}", ancestor_concept_id, e.what());
    }

    return descendants;
}

std::vector<int> VocabularyService::get_ancestors(int descendant_concept_id, int max_levels) {
    std::vector<int> ancestors;

    try {
        std::string query;
        if (max_levels < 0) {
            query = R"(
                SELECT ancestor_concept_id
                FROM concept_ancestor
                WHERE descendant_concept_id = $1
                  AND ancestor_concept_id != descendant_concept_id
            )";
        } else {
            query = R"(
                SELECT ancestor_concept_id
                FROM concept_ancestor
                WHERE descendant_concept_id = $1
                  AND ancestor_concept_id != descendant_concept_id
                  AND max_levels_of_separation <= $2
            )";
        }

        auto stmt = connection_->prepare_statement(query);
        stmt->bind(1, descendant_concept_id);
        if (max_levels >= 0) {
            stmt->bind(2, max_levels);
        }

        auto result = stmt->execute_query();
        while (result->next()) {
            ancestors.push_back(std::any_cast<int>(result->get_value(0)));
        }

    } catch (const std::exception& e) {
        auto logger = common::Logger::get("omop-vocabulary");
        logger->error("Failed to get ancestors for {}: {}", descendant_concept_id, e.what());
    }

    return ancestors;
}

bool VocabularyService::is_in_domain(int concept_id, const std::string& domain_id) {
    auto concept_result = get_concept(concept_id);
    return concept_result && concept_result->domain_id() == domain_id;
}

void VocabularyService::add_mapping(const VocabularyMapping& mapping) {
    std::unique_lock<std::shared_mutex> lock(mapping_mutex_);
    vocabulary_mappings_[mapping.source_vocabulary].push_back(mapping);

    // Update cache
    std::string key = build_mapping_key(mapping.source_value,
                                      mapping.source_vocabulary,
                                      mapping.context);
    mapping_cache_[key] = mapping.target_concept_id;
}

void VocabularyService::clear_cache() {
    std::unique_lock<std::shared_mutex> lock(cache_mutex_);
    concept_cache_.clear();
    code_to_concept_cache_.clear();
    mapping_cache_.clear();
    standard_concept_cache_.clear();
    cache_hits_ = 0;
    cache_misses_ = 0;
}

VocabularyService::CacheStats VocabularyService::get_cache_stats() const {
    std::shared_lock<std::shared_mutex> lock(cache_mutex_);

    CacheStats stats;
    stats.cache_size = concept_cache_.size() + code_to_concept_cache_.size() +
                      mapping_cache_.size() + standard_concept_cache_.size();
    stats.max_cache_size = max_cache_size_ * 4; // Approximate total capacity
    stats.hits = cache_hits_;
    stats.misses = cache_misses_;
    stats.hit_rate = (stats.hits + stats.misses > 0)
        ? static_cast<double>(stats.hits) / (stats.hits + stats.misses)
        : 0.0;

    return stats;
}

bool VocabularyService::validate_vocabulary_tables() {
    const std::vector<std::string> required_tables = {
        "concept",
        "vocabulary",
        "domain",
        "concept_class",
        "concept_relationship",
        "relationship",
        "concept_synonym",
        "concept_ancestor"
    };

    for (const auto& table : required_tables) {
        if (!connection_->table_exists(table, vocabulary_schema_)) {
            auto logger = common::Logger::get("omop-vocabulary");
            logger->error("Required vocabulary table '{}' not found", table);
            return false;
        }
    }

    return true;
}

std::string VocabularyService::get_vocabulary_version() {
    try {
        std::string query = R"(
            SELECT vocabulary_version
            FROM vocabulary
            WHERE vocabulary_id = 'None'
        )";

        auto result = connection_->execute_query(query);
        if (result->next()) {
            return std::any_cast<std::string>(result->get_value(0));
        }
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("omop-vocabulary");
        logger->warn("Failed to get vocabulary version: {}", e.what());
    }

    return "Unknown";
}

// Private methods
std::optional<Concept> VocabularyService::load_concept_from_db(int concept_id) {
    try {
        std::string query = R"(
            SELECT concept_id, concept_name, domain_id, vocabulary_id,
                   concept_class_id, standard_concept, concept_code,
                   valid_start_date, valid_end_date
            FROM concept
            WHERE concept_id = $1
        )";

        auto stmt = connection_->prepare_statement(query);
        stmt->bind(1, concept_id);
        auto result = stmt->execute_query();

        if (result->next()) {
            Concept concept_obj(
                std::any_cast<int>(result->get_value(0)),
                std::any_cast<std::string>(result->get_value(1)),
                std::any_cast<std::string>(result->get_value(2)),
                std::any_cast<std::string>(result->get_value(3)),
                std::any_cast<std::string>(result->get_value(4)),
                std::any_cast<std::string>(result->get_value(6))
            );

            if (!result->is_null(5)) {
                concept_obj.set_standard_concept(std::any_cast<std::string>(result->get_value(5)));
            }

            if (!result->is_null(7) && !result->is_null(8)) {
                concept_obj.set_valid_dates(
                    std::any_cast<std::string>(result->get_value(7)),
                    std::any_cast<std::string>(result->get_value(8))
                );
            }

            return concept_obj;
        }
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("omop-vocabulary");
        logger->error("Failed to load concept {}: {}", concept_id, e.what());
    }

    return std::nullopt;
}

std::optional<Concept> VocabularyService::load_concept_by_code_from_db(
    const std::string& concept_code,
    const std::string& vocabulary_id) {

    try {
        std::string query = R"(
            SELECT concept_id, concept_name, domain_id, vocabulary_id,
                   concept_class_id, standard_concept, concept_code,
                   valid_start_date, valid_end_date
            FROM concept
            WHERE concept_code = $1
              AND vocabulary_id = $2
              AND invalid_reason IS NULL
        )";

        auto stmt = connection_->prepare_statement(query);
        stmt->bind(1, concept_code);
        stmt->bind(2, vocabulary_id);
        auto result = stmt->execute_query();

        if (result->next()) {
            Concept concept_obj(
                std::any_cast<int>(result->get_value(0)),
                std::any_cast<std::string>(result->get_value(1)),
                std::any_cast<std::string>(result->get_value(2)),
                std::any_cast<std::string>(result->get_value(3)),
                std::any_cast<std::string>(result->get_value(4)),
                std::any_cast<std::string>(result->get_value(6))
            );

            if (!result->is_null(5)) {
                concept_obj.set_standard_concept(std::any_cast<std::string>(result->get_value(5)));
            }

            return concept_obj;
        }
    } catch (const std::exception& e) {
        auto logger = common::Logger::get("omop-vocabulary");
        logger->error("Failed to load concept by code {} ({}): {}",
                     concept_code, vocabulary_id, e.what());
    }

    return std::nullopt;
}

std::string VocabularyService::normalize_value(const std::string& value, bool case_sensitive) const {
    std::string normalized = value;

    // Trim whitespace
    normalized.erase(0, normalized.find_first_not_of(" \t\r\n"));
    normalized.erase(normalized.find_last_not_of(" \t\r\n") + 1);

    // Convert to uppercase if not case sensitive
    if (!case_sensitive) {
        std::transform(normalized.begin(), normalized.end(), normalized.begin(), ::toupper);
    }

    return normalized;
}

std::string VocabularyService::build_mapping_key(
    const std::string& source_value,
    const std::string& vocabulary_name,
    const std::optional<std::string>& context) const {

    std::string key = vocabulary_name + ":" + source_value;
    if (context) {
        key += ":" + *context;
    }
    return key;
}

// VocabularyValidator Implementation
bool VocabularyValidator::validate_concept_id(
    int concept_id,
    const std::optional<std::string>& expected_domain) {

    if (concept_id == 0) {
        return false; // 0 is not a valid concept ID
    }

    auto concept_result = vocabulary_service_.get_concept(concept_id);
    if (!concept_result) {
        return false;
    }

    // Check if concept is valid (not expired)
    if (!concept_result->is_valid()) {
        return false;
    }

    // Check domain if specified
    if (expected_domain && concept_result->domain_id() != *expected_domain) {
        return false;
    }

    return true;
}

bool VocabularyValidator::validate_mapping_exists(
    const std::string& source_value,
    const std::string& vocabulary_name) {

    int concept_id = vocabulary_service_.map_to_concept_id(source_value, vocabulary_name);
    return concept_id != 0;
}

bool VocabularyValidator::validate_standard_concept(int concept_id) {
    auto concept_result = vocabulary_service_.get_concept(concept_id);
    return concept_result && concept_result->is_standard();
}

std::vector<std::string> VocabularyValidator::get_validation_errors(
    const std::string& source_value,
    const std::string& vocabulary_name,
    const std::optional<std::string>& expected_domain) {

    std::vector<std::string> errors;

    int concept_id = vocabulary_service_.map_to_concept_id(source_value, vocabulary_name);

    if (concept_id == 0) {
        errors.push_back(std::format("No mapping found for value '{}' in vocabulary '{}'",
                                    source_value, vocabulary_name));
        return errors;
    }

    auto concept_result = vocabulary_service_.get_concept(concept_id);
    if (!concept_result) {
        errors.push_back(std::format("Concept ID {} not found in vocabulary", concept_id));
        return errors;
    }

    if (!concept_result->is_valid()) {
        errors.push_back(std::format("Concept ID {} is no longer valid", concept_id));
    }

    if (!concept_result->is_standard()) {
        errors.push_back(std::format("Concept ID {} is not a standard concept", concept_id));
    }

    if (expected_domain && concept_result->domain_id() != *expected_domain) {
        errors.push_back(std::format("Concept ID {} is in domain '{}', expected '{}'",
                                    concept_id, concept_result->domain_id(), *expected_domain));
    }

    return errors;
}

} // namespace omop::transform