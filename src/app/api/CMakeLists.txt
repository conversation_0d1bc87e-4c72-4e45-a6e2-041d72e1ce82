# API application
set(API_SOURCES
    api_service.cpp
    etl_service.cpp
)

set(API_HEADERS
    api_service.h
)

# Create service library
add_library(omop_service STATIC ${API_SOURCES})

# Create API executable
add_executable(omop-etl-api
    api_service.cpp
)

# Set include directories
target_include_directories(omop_service
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/..>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
)

target_include_directories(omop-etl-api
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
)

# Link dependencies
target_link_libraries(omop_service
    PUBLIC
        omop_core
        omop_common
        omop_extract
        omop_transform
        omop_load
        nlohmann_json::nlohmann_json
    PRIVATE
        Threads::Threads
)

target_link_libraries(omop-etl-api
    PRIVATE
        omop_service
        omop_core
        omop_common
        omop_extract
        omop_transform
        omop_load
        nlohmann_json::nlohmann_json
        Threads::Threads
)

# Set compile features
target_compile_features(omop_service PUBLIC cxx_std_20)
target_compile_features(omop-etl-api PUBLIC cxx_std_20)

# Install rules
install(TARGETS omop_service omop-etl-api
    EXPORT omop-etl-targets
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES ${API_HEADERS}
    DESTINATION include/omop/service
)
