#include "api/api_service.h"
#include "common/logging.h"
#include "core/pipeline.h"
#include <regex>
#include <sstream>

namespace omop::api {

// ETLApiService Implementation
ETLApiService::ETLApiService(std::shared_ptr<core::PipelineManager> pipeline_manager,
                           std::shared_ptr<common::ConfigurationManager> config)
    : pipeline_manager_(std::move(pipeline_manager)),
      config_(std::move(config)) {

    register_routes();
}

void ETLApiService::register_routes() {
    // Job management endpoints
    route(HttpMethod::POST, "/api/v1/etl/jobs",
        [this](const HttpRequest& req) { return create_job(req); });

    route(HttpMethod::GET, "/api/v1/etl/jobs/{job_id}",
        [this](const HttpRequest& req) { return get_job(req); });

    route(HttpMethod::GET, "/api/v1/etl/jobs",
        [this](const HttpRequest& req) { return list_jobs(req); });

    route(HttpMethod::DELETE, "/api/v1/etl/jobs/{job_id}",
        [this](const HttpRequest& req) { return cancel_job(req); });

    route(HttpMethod::POST, "/api/v1/etl/jobs/{job_id}/pause",
        [this](const HttpRequest& req) { return pause_job(req); });

    route(HttpMethod::POST, "/api/v1/etl/jobs/{job_id}/resume",
        [this](const HttpRequest& req) { return resume_job(req); });

    route(HttpMethod::GET, "/api/v1/etl/jobs/{job_id}/logs",
        [this](const HttpRequest& req) { return get_job_logs(req); });

    // Configuration endpoints
    route(HttpMethod::GET, "/api/v1/etl/config",
        [this](const HttpRequest& req) { return get_config(req); });

    route(HttpMethod::POST, "/api/v1/etl/config",
        [this](const HttpRequest& req) { return update_config(req); });

    route(HttpMethod::POST, "/api/v1/etl/validation",
        [this](const HttpRequest& req) { return validate_config(req); });

    route(HttpMethod::GET, "/api/v1/etl/mappings",
        [this](const HttpRequest& req) { return get_table_mappings(req); });

    // Vocabulary endpoints
    route(HttpMethod::GET, "/api/v1/vocabulary",
        [this](const HttpRequest& req) { return get_vocabularies(req); });

    route(HttpMethod::GET, "/api/v1/vocabulary/search",
        [this](const HttpRequest& req) { return search_concepts(req); });

    route(HttpMethod::GET, "/api/v1/vocabulary/concepts/{concept_id}",
        [this](const HttpRequest& req) { return get_concept(req); });

    route(HttpMethod::POST, "/api/v1/vocabulary/mappings",
        [this](const HttpRequest& req) { return create_mapping(req); });

    // System endpoints
    route(HttpMethod::GET, "/api/v1/health",
        [this](const HttpRequest& req) { return get_health(req); });

    route(HttpMethod::GET, "/api/v1/metrics",
        [this](const HttpRequest& req) { return get_metrics(req); });

    route(HttpMethod::GET, "/api/v1/version",
        [this](const HttpRequest& req) { return get_version(req); });

    route(HttpMethod::GET, "/api/v1/etl/tables",
        [this](const HttpRequest& req) { return get_supported_tables(req); });

    route(HttpMethod::GET, "/api/v1/etl/extractors",
        [this](const HttpRequest& req) { return get_supported_extractors(req); });

    route(HttpMethod::GET, "/api/v1/etl/loaders",
        [this](const HttpRequest& req) { return get_supported_loaders(req); });

    // OpenAPI spec
    route(HttpMethod::GET, "/api/v1/openapi.json",
        [this](const HttpRequest& req) { return get_openapi_spec(req); });
}

RouteHandler ETLApiService::get_handler(HttpMethod method, const std::string& path) const {
    auto it = routes_.find(method);
    if (it == routes_.end()) {
        return nullptr;
    }

    for (const auto& route : it->second) {
        std::unordered_map<std::string, std::string> params;
        if (match_route(path, route.path_pattern, params)) {
            // Return a handler that injects path params
            return [route, params](HttpRequest req) {
                req.params = params;
                return route.handler(req);
            };
        }
    }

    return nullptr;
}

void ETLApiService::add_middleware(Middleware middleware) {
    middleware_.push_back(std::move(middleware));
}

bool ETLApiService::process_middleware(HttpRequest& request, HttpResponse& response) {
    for (const auto& middleware : middleware_) {
        if (!middleware(request, response)) {
            return false;
        }
    }
    return true;
}

// Job management endpoints
HttpResponse ETLApiService::create_job(const HttpRequest& request) {
    HttpResponse response;
    auto logger = common::Logger::get("omop-api");

    try {
        // Parse job configuration
        auto job_config = parse_job_config(request);

        // Build pipeline
        auto pipeline = build_pipeline(job_config);

        // Submit job
        std::string job_id = pipeline_manager_->submit_job(
            job_config.name, std::move(pipeline));

        // Create response
        json response_data = {
            {"job_id", job_id},
            {"name", job_config.name},
            {"status", "created"},
            {"created_at", std::chrono::system_clock::now().time_since_epoch().count()}
        };

        response.status_code = 201;
        response.set_json(response_data);

        logger->info("Created ETL job: {} ({})", job_id, job_config.name);

    } catch (const std::exception& e) {
        logger->error("Failed to create job: {}", e.what());
        response.set_error(400, "Failed to create job", {{"error", e.what()}});
    }

    return response;
}

HttpResponse ETLApiService::get_job(const HttpRequest& request) {
    HttpResponse response;

    std::string job_id = request.params.at("job_id");
    auto job_info = pipeline_manager_->get_job_info(job_id);

    if (!job_info) {
        response.set_error(404, "Job not found", {{"job_id", job_id}});
        return response;
    }

    json job_data = {
        {"job_id", job_info->job_id},
        {"name", job_info->job_name},
        {"status", static_cast<int>(job_info->status)},
        {"status_name", [&]() {
            switch (job_info->status) {
                case core::JobStatus::Created: return "created";
                case core::JobStatus::Initializing: return "initializing";
                case core::JobStatus::Running: return "running";
                case core::JobStatus::Paused: return "paused";
                case core::JobStatus::Completed: return "completed";
                case core::JobStatus::Failed: return "failed";
                case core::JobStatus::Cancelled: return "cancelled";
                default: return "unknown";
            }
        }()},
        {"progress", job_info->progress()},
        {"total_records", job_info->total_records},
        {"processed_records", job_info->processed_records},
        {"error_records", job_info->error_records},
        {"error_rate", job_info->error_rate()},
        {"start_time", std::chrono::duration_cast<std::chrono::milliseconds>(
            job_info->start_time.time_since_epoch()).count()},
        {"duration_seconds", job_info->duration().count()}
    };

    if (job_info->status == core::JobStatus::Completed ||
        job_info->status == core::JobStatus::Failed) {
        job_data["end_time"] = std::chrono::duration_cast<std::chrono::milliseconds>(
            job_info->end_time.time_since_epoch()).count();
    }

    if (!job_info->error_messages.empty()) {
        job_data["errors"] = job_info->error_messages;
    }

    response.set_json(job_data);
    return response;
}

HttpResponse ETLApiService::list_jobs(const HttpRequest& request) {
    HttpResponse response;

    // Get query parameters
    int page = 1;
    int page_size = 20;
    std::string status_filter;

    if (request.query_params.count("page")) {
        page = std::stoi(request.query_params.at("page"));
    }
    if (request.query_params.count("page_size")) {
        page_size = std::stoi(request.query_params.at("page_size"));
    }
    if (request.query_params.count("status")) {
        status_filter = request.query_params.at("status");
    }

    // Get all jobs
    auto all_jobs = pipeline_manager_->get_all_jobs();

    // Filter by status if requested
    std::vector<core::JobInfo> filtered_jobs;
    for (const auto& job : all_jobs) {
        if (status_filter.empty()) {
            filtered_jobs.push_back(job);
        } else {
            // Match status filter
            // TODO: Implement status filtering
            filtered_jobs.push_back(job);
        }
    }

    // Sort by start time (newest first)
    std::sort(filtered_jobs.begin(), filtered_jobs.end(),
        [](const auto& a, const auto& b) {
            return a.start_time > b.start_time;
        });

    // Paginate
    size_t total = filtered_jobs.size();
    size_t start = (page - 1) * page_size;
    size_t end = std::min(start + page_size, total);

    json jobs_array = json::array();
    for (size_t i = start; i < end; ++i) {
        const auto& job = filtered_jobs[i];
        jobs_array.push_back({
            {"job_id", job.job_id},
            {"name", job.job_name},
            {"status", static_cast<int>(job.status)},
            {"progress", job.progress()},
            {"processed_records", job.processed_records},
            {"error_records", job.error_records},
            {"start_time", std::chrono::duration_cast<std::chrono::milliseconds>(
                job.start_time.time_since_epoch()).count()}
        });
    }

    json response_data = {
        {"jobs", jobs_array},
        {"pagination", {
            {"page", page},
            {"page_size", page_size},
            {"total", total},
            {"total_pages", (total + page_size - 1) / page_size}
        }}
    };

    response.set_json(response_data);
    return response;
}

HttpResponse ETLApiService::cancel_job(const HttpRequest& request) {
    HttpResponse response;

    std::string job_id = request.params.at("job_id");

    if (pipeline_manager_->cancel_job(job_id)) {
        response.set_json({
            {"job_id", job_id},
            {"status", "cancelled"},
            {"message", "Job cancelled successfully"}
        });
    } else {
        response.set_error(404, "Job not found or cannot be cancelled",
                         {{"job_id", job_id}});
    }

    return response;
}

HttpResponse ETLApiService::get_job_logs(const HttpRequest& request) {
    HttpResponse response;

    std::string job_id = request.params.at("job_id");

    // Get log level filter
    std::string level_filter = "info";
    if (request.query_params.count("level")) {
        level_filter = request.query_params.at("level");
    }

    // Get logs (simplified - in production, integrate with logging system)
    std::lock_guard<std::mutex> lock(logs_mutex_);
    auto it = job_logs_.find(job_id);

    if (it == job_logs_.end()) {
        response.set_error(404, "No logs found for job", {{"job_id", job_id}});
        return response;
    }

    json logs_array = json::array();
    for (const auto& log : it->second) {
        logs_array.push_back({
            {"timestamp", std::chrono::system_clock::now().time_since_epoch().count()},
            {"level", level_filter},
            {"message", log}
        });
    }

    response.set_json({
        {"job_id", job_id},
        {"logs", logs_array}
    });

    return response;
}

// Configuration endpoints
HttpResponse ETLApiService::get_config(const HttpRequest& request) {
    HttpResponse response;

    // Get specific configuration section if requested
    std::string section;
    if (request.query_params.count("section")) {
        section = request.query_params.at("section");
    }

    json config_data;

    if (section == "mappings") {
        // Return table mappings
        json mappings = json::object();
        for (const auto& [name, mapping] : config_->get_all_mappings()) {
            mappings[name] = {
                {"source_table", mapping.source_table()},
                {"target_table", mapping.target_table()},
                {"transformation_count", mapping.transformations().size()}
            };
        }
        config_data = mappings;
    } else if (section == "databases") {
        // Return database configurations (sanitized)
        config_data = {
            {"source_database", {
                {"type", config_->get_source_db().database_type()},
                {"host", config_->get_source_db().host()},
                {"port", config_->get_source_db().port()},
                {"database", config_->get_source_db().database()}
            }},
            {"target_database", {
                {"type", config_->get_target_db().database_type()},
                {"host", config_->get_target_db().host()},
                {"port", config_->get_target_db().port()},
                {"database", config_->get_target_db().database()}
            }}
        };
    } else {
        // Return general settings
        config_data = {
            {"etl_settings", {
                {"batch_size", config_->get_value_or<int>("etl_settings.batch_size", 1000)},
                {"parallel_workers", config_->get_value_or<int>("etl_settings.parallel_workers", 4)},
                {"validation_mode", config_->get_value_or<std::string>("etl_settings.validation_mode", "strict")},
                {"error_threshold", config_->get_value_or<double>("etl_settings.error_threshold", 0.01)}
            }}
        };
    }

    response.set_json(config_data);
    return response;
}

HttpResponse ETLApiService::validate_config(const HttpRequest& request) {
    HttpResponse response;

    try {
        // Parse configuration from request body
        json config_json = request.json_body;

        // Create temporary config manager
        common::ConfigurationManager temp_config;

        // Convert JSON to YAML string (simplified)
        std::string yaml_content = "# Validation test\n";
        // TODO: Proper JSON to YAML conversion

        temp_config.load_config_from_string(yaml_content);
        temp_config.validate_config();

        response.set_json({
            {"valid", true},
            {"message", "Configuration is valid"}
        });

    } catch (const common::ConfigurationException& e) {
        response.set_json({
            {"valid", false},
            {"message", "Configuration validation failed"},
            {"errors", {e.what()}}
        });
    } catch (const std::exception& e) {
        response.set_error(400, "Invalid configuration format", {{"error", e.what()}});
    }

    return response;
}

// System endpoints
HttpResponse ETLApiService::get_health(const HttpRequest& request) {
    HttpResponse response;

    // Check system health
    bool db_healthy = true;
    std::string db_status = "healthy";

    try {
        // Test database connections
        // TODO: Implement actual health checks
    } catch (const std::exception& e) {
        db_healthy = false;
        db_status = e.what();
    }

    json health_data = {
        {"status", db_healthy ? "healthy" : "unhealthy"},
        {"timestamp", std::chrono::system_clock::now().time_since_epoch().count()},
        {"checks", {
            {"database", {
                {"status", db_healthy ? "pass" : "fail"},
                {"message", db_status}
            }},
            {"pipeline_manager", {
                {"status", "pass"},
                {"active_jobs", pipeline_manager_->get_all_jobs().size()}
            }}
        }}
    };

    response.status_code = db_healthy ? 200 : 503;
    response.set_json(health_data);
    return response;
}

HttpResponse ETLApiService::get_metrics(const HttpRequest& request) {
    HttpResponse response;

    // Collect metrics from all jobs
    size_t total_processed = 0;
    size_t total_errors = 0;
    double total_duration = 0.0;

    auto jobs = pipeline_manager_->get_all_jobs();
    for (const auto& job : jobs) {
        total_processed += job.processed_records;
        total_errors += job.error_records;
        total_duration += job.duration().count();
    }

    json metrics = {
        {"jobs", {
            {"total", jobs.size()},
            {"running", std::count_if(jobs.begin(), jobs.end(),
                [](const auto& j) { return j.status == core::JobStatus::Running; })},
            {"completed", std::count_if(jobs.begin(), jobs.end(),
                [](const auto& j) { return j.status == core::JobStatus::Completed; })},
            {"failed", std::count_if(jobs.begin(), jobs.end(),
                [](const auto& j) { return j.status == core::JobStatus::Failed; })}
        }},
        {"records", {
            {"total_processed", total_processed},
            {"total_errors", total_errors},
            {"error_rate", total_processed > 0 ?
                static_cast<double>(total_errors) / total_processed : 0.0}
        }},
        {"performance", {
            {"total_duration_seconds", total_duration},
            {"average_records_per_second", total_duration > 0 ?
                total_processed / total_duration : 0.0}
        }}
    };

    response.set_json(metrics);
    return response;
}

HttpResponse ETLApiService::get_version(const HttpRequest& request) {
    HttpResponse response;

    response.set_json({
        {"version", "1.0.0"},
        {"api_version", "v1"},
        {"omop_cdm_version", "5.4"},
        {"build_date", __DATE__},
        {"build_time", __TIME__}
    });

    return response;
}

HttpResponse ETLApiService::get_supported_tables(const HttpRequest& request) {
    HttpResponse response;

    auto tables = cdm::OmopTableFactory::get_supported_tables();
    json tables_array = json::array();

    for (const auto& table : tables) {
        tables_array.push_back({
            {"name", table},
            {"supported", true}
        });
    }

    response.set_json({
        {"tables", tables_array},
        {"count", tables.size()}
    });

    return response;
}

// Helper methods
ETLApiService::JobConfig ETLApiService::parse_job_config(const HttpRequest& request) {
    JobConfig config;

    if (!request.json_body.is_object()) {
        throw std::invalid_argument("Request body must be a JSON object");
    }

    // Required fields
    if (!request.json_body.contains("name")) {
        throw std::invalid_argument("Missing required field: 'name'");
    }
    config.name = request.json_body["name"].get<std::string>();

    if (!request.json_body.contains("target_table")) {
        throw std::invalid_argument("Missing required field: 'target_table'");
    }
    config.target_table = request.json_body["target_table"].get<std::string>();

    // Optional fields
    if (request.json_body.contains("source_table")) {
        config.source_table = request.json_body["source_table"].get<std::string>();
    }

    if (request.json_body.contains("extractor_type")) {
        config.extractor_type = request.json_body["extractor_type"].get<std::string>();
    } else {
        config.extractor_type = "database"; // Default
    }

    if (request.json_body.contains("loader_type")) {
        config.loader_type = request.json_body["loader_type"].get<std::string>();
    } else {
        config.loader_type = "omop_database"; // Default
    }

    if (request.json_body.contains("extractor_params")) {
        config.extractor_params = request.json_body["extractor_params"];
    }

    if (request.json_body.contains("loader_params")) {
        config.loader_params = request.json_body["loader_params"];
    }

    if (request.json_body.contains("pipeline_config")) {
        config.pipeline_config = request.json_body["pipeline_config"];
    }

    return config;
}

std::unique_ptr<core::ETLPipeline> ETLApiService::build_pipeline(const JobConfig& config) {
    auto builder = core::PipelineBuilder();

    // Configure pipeline
    core::PipelineConfig pipeline_config;
    if (config.pipeline_config.contains("batch_size")) {
        pipeline_config.batch_size = config.pipeline_config["batch_size"].get<size_t>();
    }
    if (config.pipeline_config.contains("error_threshold")) {
        pipeline_config.error_threshold = config.pipeline_config["error_threshold"].get<double>();
    }

    builder.with_config(pipeline_config);

    // Set up extractor
    std::unordered_map<std::string, std::any> extractor_params;
    if (!config.source_table.empty()) {
        extractor_params["table_name"] = config.source_table;
    } else if (config_.get()) {
        // Get from configuration
        auto mapping = config_->get_table_mapping(config.target_table);
        if (mapping) {
            extractor_params["table_name"] = mapping->source_table();
        }
    }

    // Add additional extractor params from request
    // TODO: Convert JSON params to std::any map

    builder.with_extractor(config.extractor_type, extractor_params);

    // Set up transformer
    builder.with_transformer_for_table(config.target_table);

    // Set up loader
    std::unordered_map<std::string, std::any> loader_params;
    loader_params["table_name"] = config.target_table;

    // Add additional loader params from request
    // TODO: Convert JSON params to std::any map

    builder.with_loader(config.loader_type, loader_params);

    return builder.build();
}

void ETLApiService::route(HttpMethod method, const std::string& path, RouteHandler handler) {
    Route route;
    route.path_pattern = path;
    route.handler = std::move(handler);

    // Extract path parameters
    std::regex param_regex("\\{([^}]+)\\}");
    std::smatch matches;
    std::string::const_iterator search_start(path.cbegin());

    while (std::regex_search(search_start, path.cend(), matches, param_regex)) {
        route.path_params.push_back(matches[1]);
        search_start = matches.suffix().first;
    }

    routes_[method].push_back(std::move(route));
}

bool ETLApiService::match_route(const std::string& path, const std::string& pattern,
                               std::unordered_map<std::string, std::string>& params) const {
    // Convert pattern to regex
    std::string regex_pattern = pattern;

    // Escape special regex characters except for parameter placeholders
    regex_pattern = std::regex_replace(regex_pattern, std::regex("\\."), "\\.");
    regex_pattern = std::regex_replace(regex_pattern, std::regex("\\*"), "\\*");
    regex_pattern = std::regex_replace(regex_pattern, std::regex("\\+"), "\\+");
    regex_pattern = std::regex_replace(regex_pattern, std::regex("\\?"), "\\?");

    // Replace parameter placeholders with capture groups
    regex_pattern = std::regex_replace(regex_pattern,
                                     std::regex("\\{([^}]+)\\}"), "([^/]+)");

    // Ensure exact match
    regex_pattern = "^" + regex_pattern + "$";

    std::regex route_regex(regex_pattern);
    std::smatch matches;

    if (std::regex_match(path, matches, route_regex)) {
        // Extract parameter values
        size_t param_index = 0;
        for (size_t i = 1; i < matches.size(); ++i) {
            if (param_index < matches.size() - 1) {
                // Get parameter name from pattern
                std::regex param_regex("\\{([^}]+)\\}");
                std::smatch param_matches;
                std::string::const_iterator search_start(pattern.cbegin());

                for (size_t j = 0; j <= param_index; ++j) {
                    if (std::regex_search(search_start, pattern.cend(),
                                        param_matches, param_regex)) {
                        if (j == param_index) {
                            params[param_matches[1]] = matches[i];
                        }
                        search_start = param_matches.suffix().first;
                    }
                }
                param_index++;
            }
        }
        return true;
    }

    return false;
}

// OpenApiGenerator Implementation
json OpenApiGenerator::generate_spec(const std::string& base_url) {
    return {
        {"openapi", "3.0.0"},
        {"info", generate_info()},
        {"servers", {{{"url", base_url}}}},
        {"paths", generate_paths()},
        {"components", {
            {"schemas", generate_schemas()},
            {"securitySchemes", generate_security_schemes()}
        }},
        {"tags", generate_tags()}
    };
}

json OpenApiGenerator::generate_info() {
    return {
        {"title", "OMOP ETL Pipeline API"},
        {"version", "1.0.0"},
        {"description", "REST API for managing OMOP CDM ETL pipelines"},
        {"contact", {
            {"name", "API Support"},
            {"email", "<EMAIL>"}
        }},
        {"license", {
            {"name", "Apache 2.0"},
            {"url", "https://www.apache.org/licenses/LICENSE-2.0"}
        }}
    };
}

json OpenApiGenerator::generate_paths() {
    json paths;

    // Job endpoints
    paths["/api/v1/etl/jobs"] = {
        {"post", {
            {"summary", "Create a new ETL job"},
            {"tags", {"Jobs"}},
            {"requestBody", {
                {"required", true},
                {"content", {
                    {"application/json", {
                        {"schema", {{"$ref", "#/components/schemas/CreateJobRequest"}}}
                    }}
                }}
            }},
            {"responses", {
                {"201", {
                    {"description", "Job created successfully"},
                    {"content", {
                        {"application/json", {
                            {"schema", {{"$ref", "#/components/schemas/JobResponse"}}}
                        }}
                    }}
                }},
                {"400", {
                    {"description", "Invalid request"},
                    {"content", {
                        {"application/json", {
                            {"schema", {{"$ref", "#/components/schemas/ErrorResponse"}}}
                        }}
                    }}
                }}
            }}
        }},
        {"get", {
            {"summary", "List ETL jobs"},
            {"tags", {"Jobs"}},
            {"parameters", {
                {
                    {"name", "page"},
                    {"in", "query"},
                    {"schema", {{"type", "integer"}, {"default", 1}}}
                },
                {
                    {"name", "page_size"},
                    {"in", "query"},
                    {"schema", {{"type", "integer"}, {"default", 20}}}
                },
                {
                    {"name", "status"},
                    {"in", "query"},
                    {"schema", {{"type", "string"}}}
                }
            }},
            {"responses", {
                {"200", {
                    {"description", "List of jobs"},
                    {"content", {
                        {"application/json", {
                            {"schema", {{"$ref", "#/components/schemas/JobListResponse"}}}
                        }}
                    }}
                }}
            }}
        }}
    };

    // Add more paths...

    return paths;
}

json OpenApiGenerator::generate_schemas() {
    json schemas;

    schemas["CreateJobRequest"] = {
        {"type", "object"},
        {"required", {"name", "target_table"}},
        {"properties", {
            {"name", {{"type", "string"}}},
            {"source_table", {{"type", "string"}}},
            {"target_table", {{"type", "string"}}},
            {"extractor_type", {{"type", "string"}, {"default", "database"}}},
            {"loader_type", {{"type", "string"}, {"default", "omop_database"}}},
            {"extractor_params", {{"type", "object"}}},
            {"loader_params", {{"type", "object"}}},
            {"pipeline_config", {{"type", "object"}}}
        }}
    };

    schemas["JobResponse"] = {
        {"type", "object"},
        {"properties", {
            {"job_id", {{"type", "string"}}},
            {"name", {{"type", "string"}}},
            {"status", {{"type", "string"}}},
            {"created_at", {{"type", "integer"}}}
        }}
    };

    schemas["ErrorResponse"] = {
        {"type", "object"},
        {"properties", {
            {"error", {{"type", "string"}}},
            {"status", {{"type", "integer"}}},
            {"timestamp", {{"type", "integer"}}},
            {"details", {{"type", "object"}}}
        }}
    };

    // Add more schemas...

    return schemas;
}

json OpenApiGenerator::generate_security_schemes() {
    return {
        {"bearerAuth", {
            {"type", "http"},
            {"scheme", "bearer"},
            {"bearerFormat", "JWT"}
        }}
    };
}

json OpenApiGenerator::generate_tags() {
    return json::array({
        {
            {"name", "Jobs"},
            {"description", "ETL job management"}
        },
        {
            {"name", "Configuration"},
            {"description", "Configuration management"}
        },
        {
            {"name", "Vocabulary"},
            {"description", "Vocabulary and concept operations"}
        },
        {
            {"name", "System"},
            {"description", "System information and health"}
        }
    });
}

} // namespace omop::api