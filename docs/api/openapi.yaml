openapi: 3.0.3
info:
  title: OMOP ETL Pipeline API
  description: RESTful API for managing OMOP CDM ETL jobs and configurations
  version: 1.0.0
  contact:
    name: OMOP ETL Team
    email: <EMAIL>
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0

servers:
  - url: http://localhost:8080/api/v1
    description: Local development server
  - url: https://api.omop-etl.org/v1
    description: Production server

tags:
  - name: Jobs
    description: ETL job management operations
  - name: Configuration
    description: Configuration management operations
  - name: System
    description: System information and health checks

security:
  - BearerAuth: []
  - ApiKeyAuth: []

paths:
  /etl/jobs:
    get:
      summary: List ETL jobs
      tags: [Jobs]
      operationId: listJobs
      parameters:
        - name: state
          in: query
          schema:
            type: array
            items:
              type: string
              enum: [pending, running, paused, completed, failed, cancelled]
          description: Filter by job state
        - name: name
          in: query
          schema:
            type: string
          description: Filter by job name pattern
        - name: created_after
          in: query
          schema:
            type: string
            format: date-time
          description: Filter jobs created after this date
        - name: created_before
          in: query
          schema:
            type: string
            format: date-time
          description: Filter jobs created before this date
        - name: limit
          in: query
          schema:
            type: integer
            default: 100
            maximum: 1000
          description: Maximum number of results
        - name: offset
          in: query
          schema:
            type: integer
            default: 0
          description: Number of results to skip
      responses:
        '200':
          description: List of ETL jobs
          content:
            application/json:
              schema:
                type: object
                properties:
                  jobs:
                    type: array
                    items:
                      $ref: '#/components/schemas/JobStatus'
                  total:
                    type: integer
                  limit:
                    type: integer
                  offset:
                    type: integer

    post:
      summary: Create new ETL job
      tags: [Jobs]
      operationId: createJob
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/JobRequest'
      responses:
        '201':
          description: Job created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  job_id:
                    type: string
                  message:
                    type: string
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /etl/jobs/{job_id}:
    get:
      summary: Get job details
      tags: [Jobs]
      operationId: getJob
      parameters:
        - $ref: '#/components/parameters/JobId'
      responses:
        '200':
          description: Job details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobDetail'
        '404':
          $ref: '#/components/responses/NotFound'

    delete:
      summary: Cancel job
      tags: [Jobs]
      operationId: cancelJob
      parameters:
        - $ref: '#/components/parameters/JobId'
      responses:
        '204':
          description: Job cancelled successfully
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          $ref: '#/components/responses/Conflict'

  /etl/jobs/{job_id}/pause:
    post:
      summary: Pause job
      tags: [Jobs]
      operationId: pauseJob
      parameters:
        - $ref: '#/components/parameters/JobId'
      responses:
        '204':
          description: Job paused successfully
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          $ref: '#/components/responses/Conflict'

  /etl/jobs/{job_id}/resume:
    post:
      summary: Resume job
      tags: [Jobs]
      operationId: resumeJob
      parameters:
        - $ref: '#/components/parameters/JobId'
      responses:
        '204':
          description: Job resumed successfully
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          $ref: '#/components/responses/Conflict'

  /etl/jobs/{job_id}/retry:
    post:
      summary: Retry failed job
      tags: [Jobs]
      operationId: retryJob
      parameters:
        - $ref: '#/components/parameters/JobId'
      responses:
        '204':
          description: Job retry initiated
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          $ref: '#/components/responses/Conflict'

  /etl/jobs/{job_id}/logs:
    get:
      summary: Get job logs
      tags: [Jobs]
      operationId: getJobLogs
      parameters:
        - $ref: '#/components/parameters/JobId'
        - name: lines
          in: query
          schema:
            type: integer
            default: 1000
          description: Number of log lines to return
        - name: level
          in: query
          schema:
            type: string
            enum: [trace, debug, info, warn, error]
          description: Minimum log level
      responses:
        '200':
          description: Job logs
          content:
            text/plain:
              schema:
                type: string
        '404':
          $ref: '#/components/responses/NotFound'

  /etl/config:
    get:
      summary: List configurations
      tags: [Configuration]
      operationId: listConfigurations
      responses:
        '200':
          description: List of configurations
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ConfigurationSummary'

    post:
      summary: Upload configuration
      tags: [Configuration]
      operationId: uploadConfiguration
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                name:
                  type: string
                description:
                  type: string
      responses:
        '201':
          description: Configuration uploaded
          content:
            application/json:
              schema:
                type: object
                properties:
                  config_id:
                    type: string
                  message:
                    type: string

  /etl/config/validate:
    post:
      summary: Validate configuration
      tags: [Configuration]
      operationId: validateConfiguration
      requestBody:
        required: true
        content:
          application/yaml:
            schema:
              type: string
      responses:
        '200':
          description: Validation result
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationResult'

  /health:
    get:
      summary: Health check
      tags: [System]
      operationId: healthCheck
      security: []
      responses:
        '200':
          description: Service health status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthStatus'

  /etl/vocabulary:
    get:
      summary: List available vocabularies
      tags: [System]
      operationId: listVocabularies
      responses:
        '200':
          description: List of vocabularies
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Vocabulary'

  /etl/transforms:
    get:
      summary: List available transformations
      tags: [System]
      operationId: listTransformations
      responses:
        '200':
          description: List of transformations
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TransformationType'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key

  parameters:
    JobId:
      name: job_id
      in: path
      required: true
      schema:
        type: string
      description: Job identifier

  schemas:
    JobRequest:
      type: object
      required:
        - name
        - config_path
      properties:
        name:
          type: string
          description: Job name
        config_path:
          type: string
          description: Path to configuration file
        priority:
          type: string
          enum: [low, normal, high, critical]
          default: normal
        max_retries:
          type: integer
          default: 3
          minimum: 0
          maximum: 10
        retry_delay_seconds:
          type: integer
          default: 60
        enable_checkpointing:
          type: boolean
          default: true
        parameters:
          type: object
          additionalProperties:
            type: string
        metadata:
          type: object
          additionalProperties:
            type: string

    JobStatus:
      type: object
      properties:
        job_id:
          type: string
        name:
          type: string
        state:
          type: string
          enum: [pending, running, paused, completed, failed, cancelled]
        progress:
          type: number
          format: float
          minimum: 0
          maximum: 1
        created_at:
          type: string
          format: date-time
        started_at:
          type: string
          format: date-time
        completed_at:
          type: string
          format: date-time
        records_processed:
          type: integer
        records_succeeded:
          type: integer
        records_failed:
          type: integer
        error_message:
          type: string

    JobDetail:
      allOf:
        - $ref: '#/components/schemas/JobStatus'
        - type: object
          properties:
            config:
              $ref: '#/components/schemas/JobConfig'
            statistics:
              $ref: '#/components/schemas/JobStatistics'
            checkpoints:
              type: array
              items:
                $ref: '#/components/schemas/Checkpoint'

    JobConfig:
      type: object
      properties:
        pipeline_config_path:
          type: string
        priority:
          type: string
        max_retries:
          type: integer
        retry_delay_seconds:
          type: integer
        enable_checkpointing:
          type: boolean
        parameters:
          type: object
        metadata:
          type: object

    JobStatistics:
      type: object
      properties:
        total_records_processed:
          type: integer
        successful_records:
          type: integer
        failed_records:
          type: integer
        skipped_records:
          type: integer
        processing_rate:
          type: number
          description: Records per second
        elapsed_time_seconds:
          type: number
        memory_usage_mb:
          type: integer
        cpu_usage_percent:
          type: number

    Checkpoint:
      type: object
      properties:
        checkpoint_id:
          type: string
        timestamp:
          type: string
          format: date-time
        records_processed:
          type: integer
        position:
          type: string

    ConfigurationSummary:
      type: object
      properties:
        config_id:
          type: string
        name:
          type: string
        description:
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        source_type:
          type: string
        target_type:
          type: string

    ValidationResult:
      type: object
      properties:
        is_valid:
          type: boolean
        errors:
          type: array
          items:
            type: string
        warnings:
          type: array
          items:
            type: string

    HealthStatus:
      type: object
      properties:
        status:
          type: string
          enum: [healthy, degraded, unhealthy]
        timestamp:
          type: string
          format: date-time
        components:
          type: object
          additionalProperties:
            type: object
            properties:
              status:
                type: string
              message:
                type: string

    Vocabulary:
      type: object
      properties:
        vocabulary_id:
          type: string
        vocabulary_name:
          type: string
        vocabulary_version:
          type: string
        concept_count:
          type: integer

    TransformationType:
      type: object
      properties:
        type:
          type: string
        name:
          type: string
        description:
          type: string
        parameters:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
              type:
                type: string
              required:
                type: boolean
              description:
                type: string

    Error:
      type: object
      properties:
        error:
          type: string
        message:
          type: string
        details:
          type: object

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    Conflict:
      description: Resource conflict
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'