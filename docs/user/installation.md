# OMOP ETL Pipeline Installation Guide

## System Requirements

### Hardware Requirements
- **CPU**: 4+ cores recommended
- **RAM**: Minimum 8GB, 16GB+ recommended for production
- **Storage**: 50GB+ free space for data processing

### Software Requirements
- **Operating System**: Linux (Ubuntu 20.04+), macOS (10.15+), or Windows 10+
- **C++ Compiler**: GCC 10+, Clang 12+, or MSVC 2019+
- **CMake**: Version 3.23 or higher
- **PostgreSQL**: Version 12+ (for OMOP CDM database)
- **Python**: Version 3.8+ (for helper scripts)

## Prerequisites

### 1. Install Build Tools

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install -y build-essential cmake git
sudo apt install -y libpq-dev unixodbc-dev
```

#### macOS
```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install dependencies
brew install cmake postgresql unixodbc
```

#### Windows
1. Install Visual Studio 2019 or later with C++ development tools
2. Install CMake from https://cmake.org/download/
3. Install PostgreSQL from https://www.postgresql.org/download/windows/

### 2. Install Required Libraries

#### yaml-cpp
```bash
# Ubuntu/Debian
sudo apt install -y libyaml-cpp-dev

# macOS
brew install yaml-cpp

# Build from source (all platforms)
git clone https://github.com/jbeder/yaml-cpp.git
cd yaml-cpp
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
make && sudo make install
```

#### nlohmann/json
```bash
# Ubuntu/Debian
sudo apt install -y nlohmann-json3-dev

# macOS
brew install nlohmann-json

# Header-only library - can also be downloaded directly
wget https://github.com/nlohmann/json/releases/download/v3.11.2/json.hpp
```

#### spdlog
```bash
# Ubuntu/Debian
sudo apt install -y libspdlog-dev

# macOS
brew install spdlog

# Build from source
git clone https://github.com/gabime/spdlog.git
cd spdlog
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
make && sudo make install
```

#### cpp-httplib
```bash
# Header-only library
wget https://github.com/yhirose/cpp-httplib/archive/refs/tags/v0.14.0.tar.gz
tar -xzf v0.14.0.tar.gz
sudo cp cpp-httplib-0.14.0/httplib.h /usr/local/include/
```

## Installation Steps

### 1. Clone the Repository
```bash
git clone https://github.com/WTDII/omop-etl.git
cd omop-etl
```

### 2. Create Build Directory
```bash
mkdir build
cd build
```

### 3. Configure with CMake
```bash
# Basic configuration
cmake ..

# With specific options
cmake .. \
    -DCMAKE_BUILD_TYPE=Release \
    -DBUILD_TESTS=ON \
    -DBUILD_DOCS=ON \
    -DCMAKE_INSTALL_PREFIX=/usr/local
```

### 4. Build the Project
```bash
# Build with all available cores
make -j$(nproc)

# Or specify number of cores
make -j4
```

### 5. Run Tests (Optional)
```bash
make test
# Or run tests with verbose output
ctest -V
```

### 6. Install
```bash
sudo make install
```

## Docker Installation

### Using Docker Compose
```bash
# Clone the repository
git clone https://github.com/WTDII/omop-etl.git
cd omop-etl

# Build and start services
docker-compose up -d

# Check service status
docker-compose ps
```

### Building Docker Image Manually
```bash
# Build the image
docker build -t omop-etl:latest .

# Run the container
docker run -d \
    --name omop-etl \
    -p 8080:8080 \
    -v $(pwd)/config:/etc/omop-etl \
    -v $(pwd)/data:/var/lib/omop-etl \
    omop-etl:latest
```

## Post-Installation Setup

### 1. Configure Database Connection

Create a configuration file at `/etc/omop-etl/database.yaml`:

```yaml
database:
  type: postgresql
  connection:
    host: localhost
    port: 5432
    database: omop_cdm
    username: omop_user
    password: ${OMOP_DB_PASSWORD}
```

### 2. Set Environment Variables

Create a `.env` file or export variables:

```bash
export OMOP_DB_PASSWORD=your_secure_password
export OMOP_API_KEY=your_api_key
export LOG_LEVEL=INFO
```

### 3. Initialize OMOP CDM Database

```bash
# Download OMOP CDM DDL scripts
wget https://github.com/OHDSI/CommonDataModel/archive/refs/tags/v5.4.0.tar.gz
tar -xzf v5.4.0.tar.gz

# Apply DDL to your database
psql -h localhost -U omop_user -d omop_cdm < CommonDataModel-5.4.0/PostgreSQL/OMOPCDM_postgresql_5.4_ddl.sql
```

### 4. Verify Installation

```bash
# Check CLI tool
omop-etl --version

# Test configuration validation
omop-etl validate --config /path/to/test/config.yaml

# Check API service
curl http://localhost:8080/health
```

## Troubleshooting

### Common Issues

#### Missing Libraries
```
Error: libpq.so.5: cannot open shared object file
Solution: sudo ldconfig or add /usr/local/lib to LD_LIBRARY_PATH
```

#### CMake Configuration Errors
```
Error: Could not find yaml-cpp
Solution: Specify path: cmake .. -Dyaml-cpp_DIR=/path/to/yaml-cpp/lib/cmake/yaml-cpp
```

#### Permission Errors
```
Error: Permission denied writing to /var/log/omop-etl
Solution: Create directory with proper permissions:
sudo mkdir -p /var/log/omop-etl
sudo chown $USER:$USER /var/log/omop-etl
```

### Getting Help

- Check the [documentation](https://omop-etl.github.io)
- Report issues on [GitHub](https://github.com/WTDII/omop-etl/issues)
- Join the community discussion