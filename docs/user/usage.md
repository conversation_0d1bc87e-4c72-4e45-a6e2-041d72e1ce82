# OMOP ETL Pipeline Usage Guide

## Overview

The OMOP ETL Pipeline provides two primary interfaces for managing ETL jobs:
- **Command Line Interface (CLI)**: For direct execution and scripting
- **REST API**: For integration with other systems and web interfaces

## Command Line Interface

### Basic Commands

#### Running an ETL Job
```bash
# Run with a configuration file
omop-etl run --config /path/to/config.yaml

# Run with additional parameters
omop-etl run --config config.yaml --batch-size 5000 --parallel-threads 8

# Run in dry-run mode (validate without executing)
omop-etl run --config config.yaml --dry-run
```

#### Validating Configuration
```bash
# Validate a configuration file
omop-etl validate --config config.yaml

# Validate with verbose output
omop-etl validate --config config.yaml --verbose
```

#### Listing Available Components
```bash
# List available extractors
omop-etl list-extractors

# List available transformers
omop-etl list-transformers

# List available loaders
omop-etl list-loaders
```

### Advanced CLI Usage

#### Job Management
```bash
# Submit a job to run in background
omop-etl submit --config config.yaml --name "Daily Patient Import"

# Check job status
omop-etl status --job-id abc123

# List all jobs
omop-etl list-jobs --limit 10 --state running

# Cancel a running job
omop-etl cancel --job-id abc123

# Retry a failed job
omop-etl retry --job-id abc123
```

#### Configuration Management
```bash
# Generate a sample configuration
omop-etl generate-config --source postgresql --target postgresql > my-config.yaml

# Test database connections
omop-etl test-connection --config config.yaml

# Export configuration schema
omop-etl export-schema --format json > schema.json
```

## REST API Usage

### Starting the API Server
```bash
# Start with default settings
omop-etl-api

# Start with custom configuration
omop-etl-api --config /etc/omop-etl/api/config.yaml --port 8080

# Start in development mode
omop-etl-api --dev --log-level debug
```

### API Authentication

#### Using JWT Tokens
```bash
# Obtain authentication token
curl -X POST http://localhost:8080/api/v1/auth/token \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "password"}'

# Use token in requests
curl -H "Authorization: Bearer <token>" \
  http://localhost:8080/api/v1/etl/jobs
```

#### Using API Keys
```bash
# Use API key in header
curl -H "X-API-Key: your-api-key" \
  http://localhost:8080/api/v1/etl/jobs
```

### Common API Operations

#### Submit a Job
```bash
curl -X POST http://localhost:8080/api/v1/etl/jobs \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Patient Data Import",
    "config_path": "/etc/omop-etl/configs/patient_import.yaml",
    "priority": "high",
    "parameters": {
      "start_date": "2024-01-01",
      "end_date": "2024-01-31"
    }
  }'
```

#### Monitor Job Progress
```bash
# Get job status
curl http://localhost:8080/api/v1/etl/jobs/abc123 \
  -H "Authorization: Bearer <token>"

# Get job logs
curl http://localhost:8080/api/v1/etl/jobs/abc123/logs \
  -H "Authorization: Bearer <token>"

# Stream job logs
curl -N http://localhost:8080/api/v1/etl/jobs/abc123/logs?follow=true \
  -H "Authorization: Bearer <token>"
```

#### Manage Jobs
```bash
# Pause a running job
curl -X POST http://localhost:8080/api/v1/etl/jobs/abc123/pause \
  -H "Authorization: Bearer <token>"

# Resume a paused job
curl -X POST http://localhost:8080/api/v1/etl/jobs/abc123/resume \
  -H "Authorization: Bearer <token>"

# Cancel a job
curl -X DELETE http://localhost:8080/api/v1/etl/jobs/abc123 \
  -H "Authorization: Bearer <token>"
```

## Configuration Examples

### PostgreSQL to PostgreSQL
```yaml
source:
  type: postgresql
  connection:
    host: source-db.example.com
    port: 5432
    database: clinical_db
    username: ${SOURCE_USER}
    password: ${SOURCE_PASSWORD}

target:
  type: postgresql
  connection:
    host: omop-db.example.com
    port: 5432
    database: omop_cdm
    username: ${TARGET_USER}
    password: ${TARGET_PASSWORD}

mappings:
  person:
    source_table: patients
    target_table: person
    transformations:
      - source_column: patient_id
        target_column: person_id
        type: direct
      - source_column: birth_date
        target_column: birth_datetime
        type: date_transform
        format: "%Y-%m-%d"
```

### CSV Import
```yaml
source:
  type: csv
  encoding: UTF-8
  delimiter: ","
  files:
    patients:
      path: /data/imports/patients.csv
      columns: [patient_id, birth_date, gender, race]

target:
  type: postgresql
  connection:
    host: localhost
    database: omop_cdm

mappings:
  person:
    source_file: patients
    target_table: person
    transformations:
      - source_column: patient_id
        target_column: person_id
        type: direct
```

## Performance Tuning

### Batch Size Optimization
```yaml
job_config:
  batch_size: 10000  # Adjust based on memory and network
  parallel_threads: 4  # Number of CPU cores - 1
  checkpoint_interval: 50000  # Checkpoint every 50k records
```

### Memory Management
```bash
# Set JVM-style memory limits
export OMOP_ETL_MAX_MEMORY=8G

# Or use command line
omop-etl run --config config.yaml --max-memory 8G
```

### Database Optimization
```yaml
source:
  connection:
    pool_size: 10
    connection_timeout: 30
    # PostgreSQL specific
    options:
      statement_timeout: 300000
      work_mem: "256MB"
```

## Monitoring and Logging

### Log Configuration
```yaml
logging:
  level: INFO
  format: json
  file:
    enabled: true
    path: /var/log/omop-etl
    max_size: 100MB
    max_backups: 10
```

### Metrics Collection
```bash
# View real-time metrics
omop-etl metrics --job-id abc123

# Export metrics to Prometheus
omop-etl metrics --job-id abc123 --format prometheus
```

### Health Checks
```bash
# Check system health
curl http://localhost:8080/health

# Detailed health check
curl http://localhost:8080/health?detailed=true
```

## Error Handling

### Retry Configuration
```yaml
job_config:
  max_retries: 3
  retry_delay: 60  # seconds
  error_tolerance: 0.01  # 1% error rate allowed
```

### Error Recovery
```bash
# Resume from last checkpoint
omop-etl resume --job-id abc123

# Reprocess failed records
omop-etl reprocess --job-id abc123 --failed-only
```

### Debugging
```bash
# Run with debug logging
omop-etl run --config config.yaml --log-level debug

# Enable SQL query logging
omop-etl run --config config.yaml --debug-sql

# Profile performance
omop-etl run --config config.yaml --profile
```

## Best Practices

### 1. Use Environment Variables for Credentials
```yaml
connection:
  username: ${DB_USER}
  password: ${DB_PASSWORD}
```

### 2. Enable Checkpointing for Large Jobs
```yaml
job_config:
  enable_checkpointing: true
  checkpoint_interval: 10000
```

### 3. Validate Before Running
```bash
omop-etl validate --config config.yaml && \
omop-etl run --config config.yaml
```

### 4. Monitor Resource Usage
```bash
# Run with resource monitoring
omop-etl run --config config.yaml --monitor-resources
```

### 5. Use Appropriate Batch Sizes
- Small datasets (< 100k records): 1,000 - 5,000
- Medium datasets (100k - 1M records): 5,000 - 10,000
- Large datasets (> 1M records): 10,000 - 50,000

## Troubleshooting

### Common Issues

#### Out of Memory
```
Error: Java heap space / Out of memory
Solution: Increase memory allocation
omop-etl run --config config.yaml --max-memory 16G
```

#### Connection Timeout
```
Error: Connection timeout
Solution: Increase timeout in configuration
connection:
  connection_timeout: 60
  pool_timeout: 30
```

#### Slow Performance
```
Solution: Enable parallel processing
job_config:
  parallel_threads: 8
  batch_size: 20000
```

### Getting Help
```bash
# View help for any command
omop-etl --help
omop-etl run --help

# View version information
omop-etl --version

# Generate diagnostic report
omop-etl diagnose --output diagnostic-report.txt
```