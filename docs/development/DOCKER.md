# Docker Development Guide for OMOP ETL Pipeline

This guide explains how to use Docker for consistent, cross-platform development and deployment of the OMOP ETL Pipeline.

## Overview

The Docker setup provides:
- **Consistent build environment** across different platforms (Linux, macOS, Windows)
- **Isolated dependencies** without affecting your host system
- **Production-ready containers** for deployment
- **Development containers** with all tools pre-installed

## Quick Start

### **🚀 Recommended: Docker Compose with Increased Resources**

For comprehensive test suite development, use Docker Compose with increased resources:

```bash
# Start development environment with increased resources (8GB RAM, 4 CPU cores)
docker-compose -f scripts/docker-compose.yml --profile dev up -d

# Build with memory-efficient settings
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "
cmake --preset docker-debug -DCODE_COVERAGE=OFF -DENABLE_COVERAGE=OFF
cmake --build --preset docker-debug --config Debug -j 1
"

# Run tests
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "
cd build/docker-debug
ctest --output-on-failure --verbose
"
```

### **⚡ Alternative: Direct Docker Commands**

For lighter development work:
```bash
docker run --rm -v $(pwd):/workspace omop-etl-dev:latest bash -c "cd /workspace && cmake --preset docker-release && cmake --build --preset docker-release --parallel 2"
```

**See `DOCKER-USAGE.md` for comprehensive Docker build commands.**

### 1. Build the Docker Images

```bash
# Build both production and development images
./scripts/docker-build.sh

# Build with specific options
./scripts/docker-build.sh --type release --clean
```

### 2. Start Development Environment

```bash
# Start the full development stack (databases + dev container)
./scripts/docker-dev.sh up

# Start interactive development shell
./scripts/docker-dev.sh shell
```

### 3. Build the Project in Docker

```bash
# Build release version
./scripts/docker-dev.sh build release

# Build debug version
./scripts/docker-dev.sh build debug
```

### 4. Run Tests

```bash
# Run tests
./scripts/docker-dev.sh test

# Run tests with debug build
./scripts/docker-dev.sh test debug
```

## Docker Resource Requirements

### **🔧 Container Resource Configuration**

The Docker Compose configuration includes increased resources for comprehensive test compilation:

- **Memory**: 8GB limit (4GB reserved minimum)
- **CPU**: Up to 4 cores (2 cores reserved minimum)
- **Shared Memory**: 2GB for compilation processes
- **File Descriptors**: Increased ulimits for better performance

### **⚠️ Test Resource Considerations**

The comprehensive test suite requires significant resources:

- **Memory-intensive tests**: `test_interfaces.cpp` and `test_pipeline.cpp` contain extensive mock classes
- **Compilation memory usage**: Can exceed 4GB during parallel compilation
- **Recommended approach**: Single-threaded builds (`-j 1`) to prevent memory exhaustion
- **Coverage disabled**: Use `-DCODE_COVERAGE=OFF` to reduce memory overhead

### **🎯 Build Strategy for Tests**

```bash
# Memory-efficient build approach
cmake --preset docker-debug -DCODE_COVERAGE=OFF -DENABLE_COVERAGE=OFF
cmake --build --preset docker-debug --config Debug -j 1

# Individual test execution if needed
./build/docker-debug/tests/unit/core/test_interfaces
./build/docker-debug/tests/unit/core/test_record
./build/docker-debug/tests/unit/core/test_pipeline
```

## Docker Images

### Production Image (`omop-etl:latest`)
- **Base**: Ubuntu 22.04
- **Size**: Optimized multi-stage build
- **Purpose**: Production deployment
- **User**: Non-root user `omop`
- **Includes**: Only runtime dependencies and compiled binaries

### Development Image (`omop-etl-dev:latest`)
- **Base**: Ubuntu 22.04
- **Size**: Larger with development tools
- **Purpose**: Development and building
- **User**: `dev` user with sudo access
- **Includes**: Compilers, debuggers, static analysis tools
- **Resources**: Configured for memory-intensive test compilation

## CMake Presets for Docker

The project includes Docker-specific CMake presets that avoid macOS-specific paths:

```bash
# Configure for Docker release build
cmake --preset docker-release

# Configure for Docker debug build
cmake --preset docker-debug

# Build
cmake --build --preset docker-release
```

## Docker Compose Services

### Core Services

- **omop-etl-api**: Main application API server
- **postgres**: Source clinical database
- **omop-db**: Target OMOP CDM database

### Development Services

- **omop-etl-dev**: Development container (profile: `dev`)

### Optional Services

- **redis**: Caching layer
- **nginx**: Reverse proxy
- **prometheus**: Monitoring
- **grafana**: Visualization

## Development Workflow

### 1. Start Development Environment

```bash
# Start databases and development container
./scripts/docker-dev.sh up
```

### 2. Enter Development Container

```bash
# Interactive shell
./scripts/docker-dev.sh shell

# Or execute specific commands
./scripts/docker-dev.sh exec "ls -la"
```

### 3. Build and Test

```bash
# Build the project
./scripts/docker-dev.sh build

# Run tests
./scripts/docker-dev.sh test

# Format code
./scripts/docker-dev.sh format

# Run static analysis
./scripts/docker-dev.sh lint
```

### 4. Clean Up

```bash
# Clean build artifacts
./scripts/docker-dev.sh clean

# Stop development environment
./scripts/docker-dev.sh down
```

## Production Deployment

### 1. Build Production Image

```bash
./scripts/docker-build.sh --type release
```

### 2. Start Production Stack

```bash
# Start core services only
docker-compose -f scripts/docker-compose.yml up omop-etl-api postgres omop-db

# Start with monitoring
docker-compose -f scripts/docker-compose.yml up
```

### 3. Health Checks

The containers include health checks:
- **Databases**: `pg_isready` checks
- **API**: HTTP health endpoint at `/health`

## Configuration

### Environment Variables

Key environment variables for the application:

```bash
# Database connections
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=clinical_db
POSTGRES_USER=clinical_user
POSTGRES_PASSWORD=clinical_pass

OMOP_HOST=omop-db
OMOP_PORT=5432
OMOP_DB=omop_cdm
OMOP_USER=omop_user
OMOP_PASSWORD=omop_pass

# Application settings
LOG_LEVEL=INFO
JWT_SECRET=your-secret-key-change-this
OMOP_CONFIG_PATH=/etc/omop-etl
OMOP_LOG_PATH=/var/log/omop-etl
OMOP_DATA_PATH=/var/lib/omop-etl/data
```

### Volume Mounts

- **Configuration**: `./config:/etc/omop-etl:ro`
- **Logs**: `etl-logs:/var/log/omop-etl`
- **Data**: `etl-data:/var/lib/omop-etl/data`
- **Development**: `..:/workspace:cached` (dev container only)

## Troubleshooting

### Build Issues

```bash
# Clean build with verbose output
./scripts/docker-build.sh --clean --type debug

# Check build logs
docker-compose -f scripts/docker-compose.yml logs omop-etl-dev
```

### Runtime Issues

```bash
# Check application logs
docker-compose -f scripts/docker-compose.yml logs omop-etl-api

# Check database connectivity
docker-compose -f scripts/docker-compose.yml exec postgres pg_isready -U clinical_user

# Enter container for debugging
docker-compose -f scripts/docker-compose.yml exec omop-etl-api bash
```

### Performance Issues

```bash
# Monitor resource usage
docker stats

# Check container health
docker-compose -f scripts/docker-compose.yml ps
```

## Advanced Usage

### Custom Registry

```bash
# Build and push to custom registry
./scripts/docker-build.sh --push --registry myregistry.com/myorg
```

### Development with IDE

Mount your project directory and use your IDE with the development container:

```bash
# VS Code with Dev Containers extension
code .

# Or manually mount and use remote development
docker run -it -v $(pwd):/workspace omop-etl-dev:latest
```

### CI/CD Integration

```yaml
# Example GitHub Actions workflow
- name: Build Docker Image
  run: ./scripts/docker-build.sh --type release

- name: Run Tests
  run: ./scripts/docker-dev.sh test
```

## Security Considerations

- **Non-root containers**: Both images run as non-root users
- **Read-only mounts**: Configuration mounted as read-only
- **Secret management**: Use Docker secrets or external secret management
- **Network isolation**: Services communicate through dedicated network

## Performance Optimization

- **Multi-stage builds**: Minimal production image size
- **Build cache**: Development container uses cached volumes
- **Health checks**: Proper startup and readiness checks
- **Resource limits**: Configure appropriate CPU/memory limits

## 📚 **Related Documentation**

- **`DOCKER-USAGE.md`** - Comprehensive Docker build commands and examples
- **`DOCKER-MULTIARCH.md`** - Multi-architecture support for Intel, AMD, and ARM
- **Main project documentation** - See the root README.md for general information

## 🎯 **Quick Reference**

### **Most Common Commands**
```bash
# Quick build (recommended)
docker run --rm -v $(pwd):/workspace omop-etl-dev:latest bash -c "cd /workspace && cmake --preset docker-release && cmake --build --preset docker-release --parallel 2"

# Clean build
docker run --rm -v $(pwd):/workspace omop-etl-dev:latest bash -c "cd /workspace && rm -rf build/docker-release && cmake --preset docker-release && cmake --build --preset docker-release --parallel 2"

# Debug build
docker run --rm -v $(pwd):/workspace omop-etl-dev:latest bash -c "cd /workspace && cmake --preset docker-debug && cmake --build --preset docker-debug --parallel 2"

# Check build artifacts
docker run --rm -v $(pwd):/workspace omop-etl-dev:latest bash -c "cd /workspace && ls -la build/docker-release/lib/"
```

For more information, see the main project documentation.
