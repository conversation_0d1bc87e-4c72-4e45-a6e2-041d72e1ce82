# OMOP ETL Docker Build - Quick Reference

## 🚀 Quick Start Commands

### One-Command Build & Test
```bash
# Build development image and run complete build+test cycle
docker build -f Dockerfile.dev -t omop-etl-dev . && \
docker run --rm -v "$(pwd):/workspace" omop-etl-dev bash -c "
  cmake --preset docker-debug -DCODE_COVERAGE=OFF &&
  cmake --build --preset docker-debug --config Debug -j 1 &&
  cd build/docker-debug && ctest --output-on-failure --verbose
"
```

### Interactive Development
```bash
# Build development image
docker build -f Dockerfile.dev -t omop-etl-dev .

# Enter container
docker run -it --rm -v "$(pwd):/workspace" omop-etl-dev bash

# Inside container:
cmake --preset docker-debug -DCODE_COVERAGE=OFF
cmake --build --preset docker-debug --config Debug -j 1
cd build/docker-debug && ctest --output-on-failure --verbose

# Exit (container auto-removes)
exit
```

## 📋 Available Docker Presets

| Preset | Type | Coverage | Tests | Use Case |
|--------|------|----------|-------|----------|
| `docker-debug` | Debug | ON/OFF | ON | Development, Testing |
| `docker-release` | Release | OFF | ON | Production Testing |

## 🔧 Essential Commands

### Environment Management
```bash
# Build development image
docker build -f Dockerfile.dev -t omop-etl-dev .

# Run development container (interactive)
docker run -it --rm -v "$(pwd):/workspace" omop-etl-dev bash

# Run development container (one-off command)
docker run --rm -v "$(pwd):/workspace" omop-etl-dev [command]

# Check Docker images
docker images | grep omop-etl-dev
```

### Build Configuration
```bash
# Debug build (recommended for development)
cmake --preset docker-debug -DCODE_COVERAGE=OFF

# Release build (optimized)
cmake --preset docker-release -DCODE_COVERAGE=OFF
```

### Build Execution
```bash
# Debug build (single-threaded for memory efficiency)
cmake --build --preset docker-debug --config Debug -j 1

# Release build
cmake --build --preset docker-release --config Release -j 1
```

### Test Execution
```bash
# Run all tests
cd build/docker-debug && ctest --output-on-failure --verbose

# Run specific test suites
ctest --output-on-failure --verbose -R "cdm"        # CDM tests only
ctest --output-on-failure --verbose -R "core"       # Core tests only

# Run individual test executables
./tests/unit/cdm/table_definitions_test
./tests/unit/cdm/omop_tables_test
./tests/unit/cdm/test_cdm_all
./tests/unit/core/test_interfaces
./tests/unit/core/test_job_manager
```

### Clean and Rebuild
```bash
# Clean build directory
rm -rf build/docker-debug

# Full rebuild
cmake --preset docker-debug -DCODE_COVERAGE=OFF
cmake --build --preset docker-debug --config Debug -j 1
```

## 🛠️ Memory-Optimized Workflow

For systems with limited memory:

```bash
# 1. Build development image
docker build -f Dockerfile.dev -t omop-etl-dev .

# 2. Run with memory-optimized settings
docker run --rm -v "$(pwd):/workspace" omop-etl-dev bash -c "
  cmake --preset docker-debug -DCODE_COVERAGE=OFF -DENABLE_COVERAGE=OFF &&
  cmake --build --preset docker-debug --config Debug -j 1
"

# 3. Run tests individually if needed
docker run --rm -v "$(pwd):/workspace" omop-etl-dev \
  ./build/docker-debug/tests/unit/cdm/omop_tables_test
```

## 🔍 Troubleshooting Quick Fixes

### Memory Issues
```bash
# Use single-threaded build
cmake --build --preset docker-debug --config Debug -j 1

# Disable coverage
cmake --preset docker-debug -DCODE_COVERAGE=OFF -DENABLE_COVERAGE=OFF
```

### Permission Issues
```bash
# Fix file permissions (if needed)
docker run --rm -v "$(pwd):/workspace" omop-etl-dev \
  sudo chown -R dev:dev /workspace
```

### Build Cache Issues
```bash
# Clear build cache
rm -rf build/docker-debug build/docker-release

# Rebuild development image
docker build --no-cache -f Dockerfile.dev -t omop-etl-dev .
```

### Container Resource Check
```bash
# Check memory and CPU inside container
docker run --rm -v "$(pwd):/workspace" omop-etl-dev free -h
docker run --rm -v "$(pwd):/workspace" omop-etl-dev nproc

# Monitor container resources (run in separate terminal)
docker stats
```

## 🎯 Test Results Summary

After running tests, you should see:

### ✅ Expected Success Results
```
CDM Tests:
- table_definitions_test: 29/29 tests PASSED
- omop_tables_test: 38/38 tests PASSED  
- test_cdm_all: 67/67 tests PASSED

Core Tests:
- test_interfaces: All tests PASSED
- test_job_manager: All tests PASSED (may have long retry delays)
```

### 🚨 Common Test Issues
- **Memory exhaustion**: Use `-j 1` for builds
- **Long test times**: Job manager tests have 60-second retry delays
- **Permission errors**: Run `sudo chown -R dev:dev /workspace`

## 📁 Key Directories

```
/workspace/                    # Project root (inside container)
├── build/docker-debug/        # Debug build artifacts
├── build/docker-release/      # Release build artifacts
├── tests/unit/cdm/            # CDM unit tests
├── tests/unit/core/           # Core unit tests
└── src/lib/                   # Source code
```

## 🔄 Development Cycle

1. **Build image**: `docker build -f Dockerfile.dev -t omop-etl-dev .`
2. **Enter container**: `docker run -it --rm -v "$(pwd):/workspace" omop-etl-dev bash`
3. **Configure**: `cmake --preset docker-debug -DCODE_COVERAGE=OFF`
4. **Build**: `cmake --build --preset docker-debug --config Debug -j 1`
5. **Test**: `cd build/docker-debug && ctest --output-on-failure --verbose`
6. **Iterate**: Make changes and repeat steps 4-5
7. **Exit**: `exit` (container auto-removes)

## 📚 Additional Resources

- **Full Guide**: See `DOCKER_BUILD_GUIDE.md` for comprehensive instructions
- **Project README**: See `README.md` for project overview
- **CMake Presets**: See `CMakePresets.json` for preset configurations
- **Docker Compose**: See `scripts/docker-compose.yml` for service definitions

## 💡 Pro Tips

1. **Always use `-j 1`** for builds to prevent memory issues
2. **Disable coverage** during development for faster builds
3. **Use debug preset** for development (faster than release)
4. **Run tests individually** if experiencing timeouts
5. **Keep development container running** for faster iteration
6. **Use build cache volumes** to speed up subsequent builds

---

**Need Help?** Check the full `DOCKER_BUILD_GUIDE.md` or project documentation.
