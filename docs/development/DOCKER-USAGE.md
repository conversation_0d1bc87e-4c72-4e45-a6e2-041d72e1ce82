# Docker Usage Guide - OMOP ETL Pipeline

This guide demonstrates the exact Docker-based build commands for consistent, cross-platform development.

## 🐳 **Recommended Docker Build Commands**

**Docker-based builds are the recommended approach** for consistent development across Intel, AMD, and ARM architectures.

### **🚀 Recommended: Docker Compose with Increased Resources**

For comprehensive test suite development with memory-intensive tests:

```bash
# Start development environment with increased resources (8GB RAM, 4 CPU cores)
docker-compose -f scripts/docker-compose.yml --profile dev up -d

# Build with memory-efficient settings for comprehensive test suite
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "
cmake --preset docker-debug -DCODE_COVERAGE=OFF -DENABLE_COVERAGE=OFF
cmake --build --preset docker-debug --config Debug -j 1
"

# Run tests with verbose output
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "
cd build/docker-debug
ctest --output-on-failure --verbose
"
```

### **⚡ Alternative: Quick Start Command**

For lighter development work:
```bash
docker run --rm -v $(pwd):/workspace omop-etl-dev:latest bash -c "cd /workspace && cmake --preset docker-release && cmake --build --preset docker-release --parallel 2"
```

### **🧪 Test Resource Considerations**

The comprehensive test suite includes memory-intensive test files:
- `test_interfaces.cpp` - Extensive mock classes and interface tests
- `test_pipeline.cpp` - Complex pipeline integration tests
- **Memory usage**: Can exceed 4GB during compilation
- **Solution**: Single-threaded builds (`-j 1`) and coverage disabled

## 🎯 **Core Docker CMake Commands**

### **1. Basic Build Commands**

#### **Configure and Build (Release)**
```bash
cd /path/to/omop-etl
docker run --rm -v $(pwd):/workspace omop-etl-dev:latest bash -c "cd /workspace && cmake --preset docker-release && cmake --build --preset docker-release --parallel 2"
```

#### **Configure and Build (Debug)**
```bash
cd /path/to/omop-etl
docker run --rm -v $(pwd):/workspace omop-etl-dev:latest bash -c "cd /workspace && cmake --preset docker-debug && cmake --build --preset docker-debug --parallel 2"
```

### **2. Specific Target Builds**

#### **Build Only Core Library**
```bash
docker run --rm -v $(pwd):/workspace omop-etl-dev:latest bash -c "cd /workspace && cmake --preset docker-release && cmake --build --preset docker-release --target omop_core --parallel 2"
```

#### **Build Only Common Library**
```bash
docker run --rm -v $(pwd):/workspace omop-etl-dev:latest bash -c "cd /workspace && cmake --preset docker-release && cmake --build --preset docker-release --target omop_common --parallel 2"
```

#### **Build Only CDM Library**
```bash
docker run --rm -v $(pwd):/workspace omop-etl-dev:latest bash -c "cd /workspace && cmake --preset docker-release && cmake --build --preset docker-release --target omop_cdm --parallel 2"
```

### **3. Clean Build Commands**

#### **Clean and Rebuild**
```bash
docker run --rm -v $(pwd):/workspace omop-etl-dev:latest bash -c "cd /workspace && rm -rf build/docker-release && cmake --preset docker-release && cmake --build --preset docker-release --parallel 2"
```

#### **Clean Debug Build**
```bash
docker run --rm -v $(pwd):/workspace omop-etl-dev:latest bash -c "cd /workspace && rm -rf build/docker-debug && cmake --preset docker-debug && cmake --build --preset docker-debug --parallel 2"
```

### **4. Build with Tests (if enabled)**

#### **Build with Tests**
```bash
docker run --rm -v $(pwd):/workspace omop-etl-dev:latest bash -c "cd /workspace && cmake --preset docker-release -DBUILD_TESTS=ON && cmake --build --preset docker-release --parallel 2"
```

#### **Run Tests**
```bash
docker run --rm -v $(pwd):/workspace omop-etl-dev:latest bash -c "cd /workspace && cmake --build --preset docker-release --target test"
```

### **5. Installation Commands**

#### **Install Built Libraries**
```bash
docker run --rm -v $(pwd):/workspace omop-etl-dev:latest bash -c "cd /workspace && cmake --build --preset docker-release --target install"
```

### **6. Verbose Build (for debugging)**

#### **Verbose Build Output**
```bash
docker run --rm -v $(pwd):/workspace omop-etl-dev:latest bash -c "cd /workspace && cmake --preset docker-release && cmake --build --preset docker-release --parallel 2 --verbose"
```

### **7. Build Status Check**

#### **Check Build Artifacts**
```bash
docker run --rm -v $(pwd):/workspace omop-etl-dev:latest bash -c "cd /workspace && ls -la build/docker-release/lib/ && ls -la build/docker-release/bin/"
```

## 🎯 **Why Docker-Based Builds Are Recommended**

### **✅ Advantages:**

1. **Consistency**: Same build environment across all platforms (Intel, AMD, ARM)
2. **Isolation**: No conflicts with host system dependencies
3. **Reproducibility**: Identical builds regardless of host OS
4. **Cross-Platform**: Works on macOS, Linux, and Windows
5. **Version Control**: Exact compiler and dependency versions
6. **Clean Environment**: No leftover build artifacts affecting builds

### **✅ Available Build Presets:**

Based on the CMake configuration, you have these presets:
- `docker-release` - Optimized release build (-O3)
- `docker-debug` - Debug build with symbols (-g -O0)

### **✅ Build Outputs:**

After a successful build, you'll find:
```
build/docker-release/lib/
├── libomop_core.a      # Core ETL pipeline library
├── libomop_common.a    # Common utilities library
├── libomop_cdm.a       # OMOP CDM library
└── libspdlog.so*       # Logging library
```

## Complete Workflow Examples

### 1. Using Docker Container Directly

```bash
# Build development image
docker build -f Dockerfile.dev -t omop-etl-dev .

# Configure project in container
docker run --rm -v $(pwd):/workspace omop-etl-dev bash -c "
    cd /workspace
    cmake --preset docker-release
"

# Build project in container
docker run --rm -v $(pwd):/workspace omop-etl-dev bash -c "
    cd /workspace
    cmake --build --preset docker-release --parallel 2
"

# Run tests in container
docker run --rm -v $(pwd):/workspace omop-etl-dev bash -c "
    cd /workspace
    ctest --preset docker-release
"
```

### 2. Using Development Scripts (Recommended)

```bash
# Build Docker images
./scripts/docker-build.sh

# Start development environment
./scripts/docker-dev.sh up

# Build project (uses docker-release preset internally)
./scripts/docker-dev.sh build release

# Build debug version (uses docker-debug preset internally)
./scripts/docker-dev.sh build debug

# Run tests
./scripts/docker-dev.sh test

# Interactive development shell
./scripts/docker-dev.sh shell
```

### 3. Using Docker Compose

```bash
# Start full development stack
docker-compose -f scripts/docker-compose.yml --profile dev up

# Build in development container
docker-compose -f scripts/docker-compose.yml exec omop-etl-dev bash -c "
    cmake --preset docker-release
    cmake --build --preset docker-release
"
```

## Available CMake Presets

### Configure Presets
- `docker-release` - Release build for Docker environment
- `docker-debug` - Debug build for Docker environment

### Build Presets
- `docker-release` - Build release configuration
- `docker-debug` - Build debug configuration

### Test Presets
- `docker-release` - Run tests with release build
- `docker-debug` - Run tests with debug build

## Preset Configuration Details

### docker-release
```json
{
    "name": "docker-release",
    "inherits": "docker-base",
    "cacheVariables": {
        "CMAKE_BUILD_TYPE": "Release",
        "ENABLE_COVERAGE": "OFF"
    }
}
```

### docker-debug
```json
{
    "name": "docker-debug", 
    "inherits": "docker-base",
    "cacheVariables": {
        "CMAKE_BUILD_TYPE": "Debug",
        "ENABLE_COVERAGE": "ON"
    }
}
```

### docker-base (inherited)
```json
{
    "name": "docker-base",
    "generator": "Ninja",
    "cacheVariables": {
        "CMAKE_CXX_STANDARD": "20",
        "CMAKE_CXX_COMPILER": "/usr/bin/g++",
        "CMAKE_C_COMPILER": "/usr/bin/gcc",
        "BUILD_TESTS": "OFF",
        "BUILD_DOCS": "OFF"
    }
}
```

## Step-by-Step Build Process

### Method 1: Direct Docker Commands

```bash
# 1. Build development image
docker build -f Dockerfile.dev -t omop-etl-dev .

# 2. Configure with Docker preset
docker run --rm -v $(pwd):/workspace omop-etl-dev bash -c "
    cd /workspace
    cmake --preset docker-release
"

# 3. Build with Docker preset
docker run --rm -v $(pwd):/workspace omop-etl-dev bash -c "
    cd /workspace
    cmake --build --preset docker-release --parallel $(nproc)
"

# 4. Run tests
docker run --rm -v $(pwd):/workspace omop-etl-dev bash -c "
    cd /workspace
    ctest --preset docker-release --output-on-failure
"
```

### Method 2: Development Scripts

```bash
# 1. Build images
./scripts/docker-build.sh

# 2. Build project
./scripts/docker-dev.sh build release

# 3. Run tests
./scripts/docker-dev.sh test release
```

## Production Build

```bash
# Build production image
docker build -t omop-etl:latest .

# Or use build script
./scripts/docker-build.sh --type release --tag latest
```

## Troubleshooting

### Architecture Issues (Apple Silicon)
If you encounter Rosetta errors on Apple Silicon Macs:

```bash
# Use platform flag
docker run --platform linux/amd64 --rm -v $(pwd):/workspace omop-etl-dev bash -c "..."

# Or build ARM64 image
docker build --platform linux/arm64 -f Dockerfile.dev -t omop-etl-dev .
```

### Build Failures
```bash
# Check build logs
./scripts/docker-dev.sh build 2>&1 | tee build.log

# Clean build
./scripts/docker-dev.sh clean
./scripts/docker-dev.sh build
```

### Container Issues
```bash
# Check container status
docker ps -a

# View logs
docker logs <container-id>

# Clean up
docker system prune -f
```

## 🚨 **Troubleshooting Docker Builds**

### **Common Issues**

#### **1. Architecture Mismatch**
```bash
# Error: exec format error
# Solution: Use correct platform
docker run --platform linux/amd64 --rm -v $(pwd):/workspace omop-etl-dev:latest bash -c "..."

# Or build ARM64 image for Apple Silicon
docker build --platform linux/arm64 -f Dockerfile.dev -t omop-etl-dev .
```

#### **2. Build Failures**
```bash
# Check build logs with verbose output
docker run --rm -v $(pwd):/workspace omop-etl-dev:latest bash -c "cd /workspace && cmake --preset docker-release && cmake --build --preset docker-release --parallel 2 --verbose 2>&1 | tee build.log"

# Clean build
docker run --rm -v $(pwd):/workspace omop-etl-dev:latest bash -c "cd /workspace && rm -rf build/docker-release && cmake --preset docker-release && cmake --build --preset docker-release --parallel 2"
```

#### **3. Container Issues**
```bash
# Check container status
docker ps -a

# View logs
docker logs <container-id>

# Clean up
docker system prune -f
```

#### **4. Memory and Resource Issues**
```bash
# Compiler killed during test compilation
# Solution: Use single-threaded build to reduce memory pressure
cmake --build --preset docker-debug --config Debug -j 1

# Disable coverage to reduce memory usage
cmake --preset docker-debug -DCODE_COVERAGE=OFF -DENABLE_COVERAGE=OFF

# Check Docker resource allocation
docker stats omop-etl-dev --no-stream

# Individual test execution for debugging memory-intensive tests
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "
./build/docker-debug/tests/unit/core/test_interfaces
./build/docker-debug/tests/unit/core/test_record
./build/docker-debug/tests/unit/core/test_pipeline
"
```

#### **5. Permission Issues**
```bash
# Fix file permissions after Docker build
sudo chown -R $(id -u):$(id -g) build/
```

## 🎉 **Benefits of Docker-Based Build**

✅ **Consistent Environment**: Same build environment across all platforms (Intel, AMD, ARM)
✅ **No Host Dependencies**: All tools contained in Docker images
✅ **Reproducible Builds**: Exact same versions every time
✅ **Easy CI/CD**: Simple integration with build pipelines
✅ **Isolated Dependencies**: No conflicts with host system
✅ **Cross-Platform**: Works on macOS, Linux, and Windows
✅ **Version Control**: Exact compiler and dependency versions

## 🚀 **Next Steps**

1. **Test the setup**: Run the quick start command above
2. **Start developing**: Use `./scripts/docker-dev.sh shell` for interactive development
3. **Set up CI/CD**: Use Docker images in your pipeline
4. **Deploy**: Use production Docker images
5. **Multi-architecture**: See `DOCKER-MULTIARCH.md` for cross-platform builds

For more details, see `DOCKER.md` for comprehensive documentation and `DOCKER-MULTIARCH.md` for multi-architecture support.
