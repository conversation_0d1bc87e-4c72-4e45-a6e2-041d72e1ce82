# Docker-Based Build and Test Guide

This guide provides comprehensive step-by-step instructions for building the OMOP ETL project and running unit tests using Docker-based build presets. Docker-based builds are the **recommended approach** for consistent, cross-platform development.

## Prerequisites

### System Requirements
- **Docker**: Version 20.10+ with Docker Compose
- **Memory**: 8GB allocated to <PERSON><PERSON> (4GB minimum reserved)
- **CPU**: 4 cores allocated (2 cores minimum reserved)
- **Disk**: 10GB free space for build artifacts and dependencies
- **Platform**: Supports Intel (x86_64), AMD, and ARM64 architectures

### Docker Configuration
```bash
# Verify Docker is running and has sufficient resources
docker info
docker-compose --version

# Set platform environment variable (optional, auto-detected)
export DOCKER_PLATFORM=linux/amd64  # For Intel/AMD
# export DOCKER_PLATFORM=linux/arm64  # For Apple Silicon
```

## Available Docker Build Presets

The project provides two Docker-based CMake presets:

1. **`docker-debug`**: Debug build with tests enabled, coverage enabled
2. **`docker-release`**: Release build with tests enabled, coverage disabled

## Quick Start (Recommended)

### Option 1: One-Command Build and Test
```bash
# Clone and navigate to project
git clone https://github.com/UCL-Cancer-Data-Engineering/omop-etl.git
cd omop-etl

# Build development Docker image
docker build -f Dockerfile.dev -t omop-etl-dev .

# Run complete build+test cycle
docker run --rm -v "$(pwd):/workspace" omop-etl-dev bash -c "
  cmake --preset docker-debug -DCODE_COVERAGE=OFF &&
  cmake --build --preset docker-debug --config Debug -j 1 &&
  cd build/docker-debug && ctest --output-on-failure --verbose
"
```

### Option 2: Interactive Development
```bash
# Build development Docker image
docker build -f Dockerfile.dev -t omop-etl-dev .

# Enter development container for interactive work
docker run -it --rm -v "$(pwd):/workspace" omop-etl-dev bash

# Inside the container, run commands interactively:
cmake --preset docker-debug -DCODE_COVERAGE=OFF
cmake --build --preset docker-debug --config Debug -j 1
cd build/docker-debug && ctest --output-on-failure --verbose

# Exit container (container will be automatically removed)
exit
```

## Detailed Step-by-Step Instructions

### 1. Environment Setup

#### Build Development Image
```bash
# Navigate to project directory
cd omop-etl

# Build the development Docker image
docker build -f Dockerfile.dev -t omop-etl-dev .

# Verify image was built successfully
docker images | grep omop-etl-dev
```

#### Enter Development Container
```bash
# Access the development container interactively
docker run -it --rm -v "$(pwd):/workspace" omop-etl-dev bash

# Verify you're in the correct directory
pwd  # Should show /workspace
ls   # Should show project files
```

### 2. Build Configuration

#### Configure Docker Debug Build
```bash
# Configure with coverage disabled for memory efficiency
cmake --preset docker-debug -DCODE_COVERAGE=OFF -DENABLE_COVERAGE=OFF

# Alternative: Configure with coverage enabled (requires more memory)
# cmake --preset docker-debug
```

#### Configure Docker Release Build
```bash
# Configure release build (optimized, no debug symbols)
cmake --preset docker-release -DCODE_COVERAGE=OFF -DENABLE_COVERAGE=OFF
```

### 3. Build Execution

#### Build Debug Version
```bash
# Build with single-threaded compilation (recommended for memory efficiency)
cmake --build --preset docker-debug --config Debug -j 1

# Alternative: Multi-threaded build (only if you have sufficient memory)
# cmake --build --preset docker-debug --config Debug -j 4
```

#### Build Release Version
```bash
# Build release version
cmake --build --preset docker-release --config Release -j 1
```

### 4. Test Execution

#### Run All Tests
```bash
# Navigate to build directory (inside container)
cd build/docker-debug  # or build/docker-release

# Run all tests with verbose output
ctest --output-on-failure --verbose

# Alternative: Run from host using Docker
docker run --rm -v "$(pwd):/workspace" omop-etl-dev bash -c "
  cd build/docker-debug && ctest --output-on-failure --verbose
"
```

#### Run Specific Test Suites
```bash
# Run only CDM tests (inside container)
ctest --output-on-failure --verbose -R "cdm"

# Run only core tests (inside container)
ctest --output-on-failure --verbose -R "core"

# Run specific test by name (inside container)
ctest --output-on-failure --verbose -R "table_definitions_test"
```

#### Run Individual Test Executables
```bash
# Run CDM tests individually (inside container)
./tests/unit/cdm/table_definitions_test
./tests/unit/cdm/omop_tables_test
./tests/unit/cdm/test_cdm_all

# Run core tests individually (inside container)
./tests/unit/core/test_interfaces
./tests/unit/core/test_job_manager

# Alternative: Run from host using Docker
docker run --rm -v "$(pwd):/workspace" omop-etl-dev \
  ./build/docker-debug/tests/unit/cdm/omop_tables_test
```

### 5. Clean and Rebuild

#### Clean Build Directory
```bash
# Remove build artifacts
rm -rf build/docker-debug
rm -rf build/docker-release

# Or clean specific preset
cmake --build --preset docker-debug --target clean
```

#### Full Rebuild
```bash
# Clean and reconfigure
rm -rf build/docker-debug
cmake --preset docker-debug -DCODE_COVERAGE=OFF
cmake --build --preset docker-debug --config Debug -j 1
```

## Memory-Optimized Build Process

For systems with limited memory, use this optimized approach:

```bash
# 1. Build development image
docker build -f Dockerfile.dev -t omop-etl-dev .

# 2. Configure and build with memory-optimized settings
docker run --rm -v "$(pwd):/workspace" omop-etl-dev bash -c "
  cmake --preset docker-debug -DCODE_COVERAGE=OFF -DENABLE_COVERAGE=OFF &&
  cmake --build --preset docker-debug --config Debug -j 1
"

# 3. Run tests individually if needed
docker run --rm -v "$(pwd):/workspace" omop-etl-dev \
  ./build/docker-debug/tests/unit/cdm/table_definitions_test

docker run --rm -v "$(pwd):/workspace" omop-etl-dev \
  ./build/docker-debug/tests/unit/cdm/omop_tables_test
```

## Troubleshooting

### Common Issues and Solutions

#### Memory Issues
```bash
# Error: "virtual memory exhausted: Cannot allocate memory"
# Solution: Use single-threaded build
cmake --build --preset docker-debug --config Debug -j 1
```

#### Container Resource Issues
```bash
# Error: Container runs out of memory
# Solution: Increase Docker memory allocation to 8GB minimum
# Check Docker Desktop settings -> Resources -> Memory
```

#### Permission Issues
```bash
# Error: Permission denied when accessing files
# Solution: Fix file permissions
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev \
  sudo chown -R dev:dev /workspace
```

#### Build Cache Issues
```bash
# Error: Stale build cache causing issues
# Solution: Clear build cache
docker volume rm omop-etl_dev_build_cache
docker-compose -f scripts/docker-compose.yml --profile dev down
docker-compose -f scripts/docker-compose.yml --profile dev up -d
```

### Debug Information
```bash
# Check container resources
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev \
  free -h

# Check disk space
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev \
  df -h

# Check CMake configuration
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev \
  cmake --preset docker-debug --list-cache
```

## Environment Cleanup

### Stop Development Environment
```bash
# Stop all services
docker-compose -f scripts/docker-compose.yml --profile dev down

# Stop and remove volumes (WARNING: This deletes build cache)
docker-compose -f scripts/docker-compose.yml --profile dev down -v

# Remove unused Docker resources
docker system prune -f
```

### Complete Cleanup
```bash
# Remove all project-related Docker resources
docker-compose -f scripts/docker-compose.yml down -v --remove-orphans
docker system prune -a -f --volumes
```

## Performance Tips

1. **Use single-threaded builds** (`-j 1`) to prevent memory exhaustion
2. **Disable coverage** (`-DCODE_COVERAGE=OFF`) during development
3. **Use debug builds** for testing (faster compilation than release with debug info)
4. **Run tests individually** if experiencing timeout issues
5. **Allocate sufficient Docker resources** (8GB RAM, 4 CPU cores)
6. **Use build cache volumes** to speed up subsequent builds

## Next Steps

After successful build and test execution:
1. Review test results for any failures
2. Run specific test suites relevant to your changes
3. Use the built executables for development and debugging
4. Configure your IDE to use the Docker environment for development

For production deployment, use the release preset and follow the deployment documentation.

## Complete Build Matrix

### All Docker Preset Combinations

| Preset | Configuration | Coverage | Tests | Use Case |
|--------|---------------|----------|-------|----------|
| `docker-debug` | Debug | ON/OFF | ON | Development, Testing |
| `docker-release` | Release | OFF | ON | Production Testing |

### Build Commands Reference

#### Docker Debug Preset
```bash
# Configuration
cmake --preset docker-debug                           # With coverage
cmake --preset docker-debug -DCODE_COVERAGE=OFF       # Without coverage

# Build
cmake --build --preset docker-debug --config Debug -j 1

# Test
cd build/docker-debug && ctest --output-on-failure --verbose
```

#### Docker Release Preset
```bash
# Configuration
cmake --preset docker-release                         # Standard release
cmake --preset docker-release -DCODE_COVERAGE=OFF     # Explicit no coverage

# Build
cmake --build --preset docker-release --config Release -j 1

# Test
cd build/docker-release && ctest --output-on-failure --verbose
```

## Advanced Usage

### Cross-Platform Builds

#### Intel/AMD (x86_64)
```bash
export DOCKER_PLATFORM=linux/amd64
docker-compose -f scripts/docker-compose.yml --profile dev up -d
```

#### Apple Silicon (ARM64)
```bash
export DOCKER_PLATFORM=linux/arm64
docker-compose -f scripts/docker-compose.yml --profile dev up -d
```

### Custom Build Options

#### Enable Specific Features
```bash
# Build with documentation
cmake --preset docker-debug -DBUILD_DOCS=ON

# Build without tests (faster compilation)
cmake --preset docker-debug -DBUILD_TESTS=OFF

# Custom schema names
cmake --preset docker-debug -DCDM_SCHEMA=my_cdm -DVOCAB_SCHEMA=my_vocab
```

#### Compiler-Specific Builds
```bash
# Use specific compiler (already configured in presets)
# docker-debug uses GCC by default
# For custom compiler, modify CMakePresets.json
```

### Development Workflows

#### Continuous Development
```bash
# Start long-running development session
docker-compose -f scripts/docker-compose.yml --profile dev up -d

# Enter container for interactive development
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash

# Inside container - iterative development cycle:
while true; do
  # Make code changes (in your host editor)

  # Quick rebuild (only changed files)
  cmake --build --preset docker-debug --config Debug -j 1

  # Run specific tests
  ./build/docker-debug/tests/unit/cdm/table_definitions_test

  # Or run all tests
  cd build/docker-debug && ctest --output-on-failure --verbose

  # Continue development...
done
```

#### Test-Driven Development
```bash
# Start with failing test
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash -c "
  cmake --preset docker-debug -DCODE_COVERAGE=OFF &&
  cmake --build --preset docker-debug --config Debug -j 1 &&
  ./build/docker-debug/tests/unit/cdm/table_definitions_test
"

# Implement feature to make test pass
# Repeat build and test cycle
```

### Performance Monitoring

#### Build Time Optimization
```bash
# Time the build process
time cmake --build --preset docker-debug --config Debug -j 1

# Monitor resource usage during build
docker stats omop-etl-dev
```

#### Test Execution Monitoring
```bash
# Time test execution
time ctest --output-on-failure --verbose

# Run tests with timing information
ctest --output-on-failure --verbose --schedule-random --timeout 300
```

## Integration with IDEs

### Visual Studio Code
```bash
# Use Dev Containers extension
# .devcontainer/devcontainer.json should reference the Docker setup

# Or use Remote-Containers with docker-compose
# Command Palette: "Remote-Containers: Reopen in Container"
```

### CLion
```bash
# Configure CLion to use Docker toolchain
# Settings -> Build, Execution, Deployment -> Toolchains
# Add Docker toolchain pointing to omop-etl-dev container
```

### Command Line Development
```bash
# Use tmux/screen for persistent sessions
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev tmux

# Multiple terminal sessions
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash  # Terminal 1
docker-compose -f scripts/docker-compose.yml --profile dev exec omop-etl-dev bash  # Terminal 2
```

## Automated Testing Scripts

### Create Test Runner Script
```bash
# Create automated test script
cat > run_tests.sh << 'EOF'
#!/bin/bash
set -e

echo "Starting OMOP ETL Docker-based testing..."

# Start development environment
docker-compose -f scripts/docker-compose.yml --profile dev up -d

# Wait for services to be ready
sleep 10

# Run build and tests
docker-compose -f scripts/docker-compose.yml --profile dev exec -T omop-etl-dev bash -c "
  set -e
  echo 'Configuring build...'
  cmake --preset docker-debug -DCODE_COVERAGE=OFF -DENABLE_COVERAGE=OFF

  echo 'Building project...'
  cmake --build --preset docker-debug --config Debug -j 1

  echo 'Running tests...'
  cd build/docker-debug
  ctest --output-on-failure --verbose

  echo 'All tests completed successfully!'
"

# Cleanup
docker-compose -f scripts/docker-compose.yml --profile dev down

echo "Testing completed successfully!"
EOF

chmod +x run_tests.sh
./run_tests.sh
```

### CI/CD Integration
```bash
# Example GitHub Actions workflow snippet
# .github/workflows/docker-build.yml
name: Docker Build and Test
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build and Test
        run: |
          docker-compose -f scripts/docker-compose.yml --profile dev up -d
          docker-compose -f scripts/docker-compose.yml --profile dev exec -T omop-etl-dev bash -c "
            cmake --preset docker-debug -DCODE_COVERAGE=OFF &&
            cmake --build --preset docker-debug --config Debug -j 1 &&
            cd build/docker-debug &&
            ctest --output-on-failure --verbose
          "
          docker-compose -f scripts/docker-compose.yml --profile dev down
```

## Summary

This guide provides comprehensive instructions for building and testing the OMOP ETL project using Docker-based presets. The recommended approach is:

1. **Use `docker-debug` preset** for development and testing
2. **Use `docker-release` preset** for production builds
3. **Always use single-threaded builds** (`-j 1`) to prevent memory issues
4. **Disable coverage** (`-DCODE_COVERAGE=OFF`) during development for faster builds
5. **Use the development container** for consistent cross-platform builds

For any issues or questions, refer to the troubleshooting section or check the project's issue tracker.
