# OMOP ETL Pipeline Data Flow

## Overview

This document describes the data flow through the OMOP ETL pipeline, from source data extraction to final OMOP CDM table population.

## Data Flow Stages

### 1. Extraction Stage

The extraction stage is responsible for reading data from various sources:

```
Source Systems → Extractors → Raw Records
```

#### Supported Sources:
- **PostgreSQL Database**: Direct SQL queries with connection pooling
- **MySQL Database**: ODBC-based extraction with batch fetching
- **CSV Files**: Streaming file reader with configurable parsing
- **JSON Files**: Document parsing with JSONPath support

#### Extraction Process:
1. **Connection Establishment**: Secure connection to data source
2. **Schema Validation**: Verify source schema matches configuration
3. **Batch Reading**: Extract data in configurable batch sizes
4. **Raw Record Creation**: Convert source data to internal record format

### 2. Transformation Stage

The transformation stage applies business rules and OMOP CDM mappings:

```
Raw Records → Transformers → OMOP-Compliant Records
```

#### Transformation Types:

##### Direct Mapping
```yaml
- source_column: patient_id
  target_column: person_id
  type: direct
```

##### Vocabulary Mapping
```yaml
- source_column: gender
  target_column: gender_concept_id
  type: vocabulary_mapping
  vocabulary: Gender
  mapping:
    "M": 8507
    "F": 8532
```

##### Date Transformation
```yaml
- source_column: birth_date
  target_column: birth_datetime
  type: date_transform
  format: "%Y-%m-%d"
```

##### Custom Transformation
```yaml
- source_columns: [drug_name, drug_strength]
  target_column: drug_concept_id
  type: custom_transform
  function: lookup_drug_concept
```

### 3. Validation Stage

Each record undergoes validation before loading:

```
OMOP Records → Validators → Valid/Invalid Records
```

#### Validation Rules:
- **Field Validation**: Not null, data type, format
- **Business Rules**: Date ranges, value constraints
- **Referential Integrity**: Foreign key relationships
- **Concept Validation**: Valid OMOP concept IDs

### 4. Loading Stage

Valid records are loaded into the target OMOP CDM database:

```
Valid Records → Loaders → OMOP CDM Tables
```

#### Loading Strategies:
- **Batch Insert**: Bulk loading for performance
- **Upsert Logic**: Handle updates to existing records
- **Transaction Management**: Ensure data consistency
- **Error Handling**: Rollback on failures

## Data Flow Example: Patient to Person

### 1. Source Data (CSV)
```csv
patient_id,birth_date,gender,race,ethnicity
12345,1980-05-15,M,White,Non-Hispanic
```

### 2. Extraction
```json
{
  "patient_id": "12345",
  "birth_date": "1980-05-15",
  "gender": "M",
  "race": "White",
  "ethnicity": "Non-Hispanic"
}
```

### 3. Transformation
```json
{
  "person_id": 12345,
  "birth_datetime": "1980-05-15 00:00:00",
  "gender_concept_id": 8507,
  "race_concept_id": 8527,
  "ethnicity_concept_id": 38003564
}
```

### 4. Validation
- ✓ person_id: Not null
- ✓ birth_datetime: Valid date range
- ✓ gender_concept_id: Valid concept

### 5. Loading
```sql
INSERT INTO person (
  person_id,
  gender_concept_id,
  year_of_birth,
  month_of_birth,
  day_of_birth,
  birth_datetime,
  race_concept_id,
  ethnicity_concept_id
) VALUES (
  12345,
  8507,
  1980,
  5,
  15,
  '1980-05-15 00:00:00',
  8527,
  38003564
);
```

## Batch Processing Flow

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│   Extract   │     │  Transform  │     │    Load     │
│   Batch     │────▶│    Batch    │────▶│   Batch     │
│  (10,000)   │     │  (10,000)   │     │  (10,000)   │
└─────────────┘     └─────────────┘     └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  Progress   │     │ Validation  │     │   Commit    │
│   Update    │     │   Report    │     │ Checkpoint  │
└─────────────┘     └─────────────┘     └─────────────┘
```

## Error Handling Flow

```
Record Processing
       │
       ▼
   Validation
       │
   ┌───┴───┐
   │ Pass  │ Fail
   ▼       ▼
 Load    Error Log
   │        │
   ▼        ▼
Success  Retry Queue
           │
           ▼
      Dead Letter
```

## Performance Optimization

### 1. Parallel Processing
- Multiple extractor threads
- Concurrent transformation
- Parallel validation
- Batch loading

### 2. Memory Management
- Streaming large files
- Batch size optimization
- Record pooling
- Garbage collection tuning

### 3. Database Optimization
- Connection pooling
- Prepared statements
- Bulk operations
- Index utilization

## Monitoring Points

### Metrics Collection
- Records per second
- Transformation time
- Validation failures
- Load throughput

### Checkpointing
- Extraction position
- Transformation state
- Load progress
- Error recovery

## Security Considerations

### Data Protection
- Encryption in transit
- Credential management
- Audit logging
- PHI handling

### Access Control
- Source system permissions
- Target database roles
- API authentication
- Configuration security