#!/bin/bash

# Multi-architecture Docker build script for OMOP ETL Pipeline
# Supports Intel (x86_64), AMD (x86_64), and ARM (aarch64/arm64) chips

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

print_header() {
    echo -e "\n${BLUE}==== $1 ====${NC}"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Default values
BUILD_TYPE="all"
PUSH_IMAGES=false
TAG_SUFFIX=""
MULTI_ARCH=false
PLATFORMS="linux/amd64,linux/arm64"
NATIVE_ONLY=false
REGISTRY=""
USE_BUILDX=true

show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Multi-architecture Docker build script for OMOP ETL Pipeline

OPTIONS:
    --type TYPE       Build type: 'dev', 'prod', or 'all' (default: all)
    --push            Push images to registry after building
    --tag SUFFIX      Add suffix to image tags
    --multi-arch      Build multi-architecture images (Intel, AMD, ARM)
    --platforms LIST  Comma-separated list of platforms (default: linux/amd64,linux/arm64)
    --native-only     Build only for current platform architecture
    --registry REG    Registry prefix (e.g., docker.io/myorg)
    --no-buildx       Use regular docker build instead of buildx
    --help            Show this help message

SUPPORTED PLATFORMS:
    linux/amd64       Intel/AMD 64-bit (x86_64)
    linux/arm64       ARM 64-bit (aarch64) - Apple Silicon, ARM servers
    linux/arm/v7      ARM 32-bit (armhf) - Raspberry Pi, etc.

EXAMPLES:
    $0                              # Build all images for current platform
    $0 --type dev                   # Build only development image
    $0 --multi-arch                 # Build for Intel, AMD, and ARM64
    $0 --platforms linux/amd64     # Build only for Intel/AMD
    $0 --native-only                # Build only for current architecture
    $0 --type prod --push --multi-arch --registry myregistry.com/omop
    $0 --tag v1.0.0                 # Build with tag suffix

ARCHITECTURE DETECTION:
    The script automatically detects your current architecture and builds
    appropriate images. Use --multi-arch to build for multiple platforms.

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --type)
            BUILD_TYPE="$2"
            shift 2
            ;;
        --push)
            PUSH_IMAGES=true
            shift
            ;;
        --tag)
            TAG_SUFFIX="-$2"
            shift 2
            ;;
        --multi-arch)
            MULTI_ARCH=true
            shift
            ;;
        --platforms)
            PLATFORMS="$2"
            MULTI_ARCH=true
            shift 2
            ;;
        --native-only)
            NATIVE_ONLY=true
            shift
            ;;
        --registry)
            REGISTRY="$2"
            shift 2
            ;;
        --no-buildx)
            USE_BUILDX=false
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

cd "$PROJECT_ROOT"

print_header "Multi-Architecture Docker Build Script for OMOP ETL Pipeline"

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

print_success "Docker is available"

# Check if Docker daemon is running
if ! docker info &> /dev/null; then
    print_error "Docker daemon is not running"
    exit 1
fi

print_success "Docker daemon is running"

# Detect current architecture
CURRENT_ARCH=$(uname -m)
case "$CURRENT_ARCH" in
    "x86_64") CURRENT_PLATFORM="linux/amd64" ;;
    "aarch64"|"arm64") CURRENT_PLATFORM="linux/arm64" ;;
    "armv7l") CURRENT_PLATFORM="linux/arm/v7" ;;
    *) 
        print_warning "Unknown architecture: $CURRENT_ARCH"
        CURRENT_PLATFORM="linux/amd64"
        ;;
esac

print_info "Current architecture: $CURRENT_ARCH ($CURRENT_PLATFORM)"

# Set platform strategy
if [[ "$NATIVE_ONLY" == "true" ]]; then
    PLATFORMS="$CURRENT_PLATFORM"
    print_info "Building for native platform only: $PLATFORMS"
elif [[ "$MULTI_ARCH" == "true" ]]; then
    print_info "Building for multiple architectures: $PLATFORMS"
else
    PLATFORMS="$CURRENT_PLATFORM"
    print_info "Building for current platform: $PLATFORMS"
fi

# Check if buildx is available for multi-arch builds
if [[ "$MULTI_ARCH" == "true" ]] && [[ "$USE_BUILDX" == "true" ]]; then
    if ! docker buildx version &> /dev/null; then
        print_warning "Docker buildx not available, falling back to regular build"
        USE_BUILDX=false
        if [[ "$PLATFORMS" == *","* ]]; then
            print_warning "Multi-platform build requires buildx, building for current platform only"
            PLATFORMS="$CURRENT_PLATFORM"
        fi
    else
        print_success "Docker buildx is available"
        # Create/use buildx builder
        if ! docker buildx inspect multiarch-builder &> /dev/null; then
            print_info "Creating multi-architecture builder..."
            docker buildx create --name multiarch-builder --use --bootstrap
        else
            print_info "Using existing multi-architecture builder..."
            docker buildx use multiarch-builder
        fi
    fi
fi

# Set image names
DEV_IMAGE="omop-etl-dev"
PROD_IMAGE="omop-etl"

if [[ -n "$REGISTRY" ]]; then
    DEV_IMAGE="${REGISTRY}/${DEV_IMAGE}"
    PROD_IMAGE="${REGISTRY}/${PROD_IMAGE}"
fi

if [[ -n "$TAG_SUFFIX" ]]; then
    DEV_TAG="latest${TAG_SUFFIX}"
    PROD_TAG="latest${TAG_SUFFIX}"
else
    DEV_TAG="latest"
    PROD_TAG="latest"
fi

print_info "Development image: ${DEV_IMAGE}:${DEV_TAG}"
print_info "Production image: ${PROD_IMAGE}:${PROD_TAG}"

# Build function
build_image() {
    local dockerfile="$1"
    local image_name="$2"
    local tag="$3"
    local build_args="$4"
    
    print_header "Building ${image_name}:${tag}"
    
    if [[ "$USE_BUILDX" == "true" ]] && [[ "$MULTI_ARCH" == "true" ]]; then
        # Multi-architecture build with buildx
        local push_flag=""
        if [[ "$PUSH_IMAGES" == "true" ]]; then
            push_flag="--push"
        else
            push_flag="--load"
            if [[ "$PLATFORMS" == *","* ]]; then
                print_warning "Multi-platform images cannot be loaded to local Docker, will push to registry"
                push_flag="--push"
                PUSH_IMAGES=true
            fi
        fi
        
        docker buildx build \
            --platform "$PLATFORMS" \
            --file "$dockerfile" \
            --tag "${image_name}:${tag}" \
            $build_args \
            $push_flag \
            .
    else
        # Regular single-architecture build
        docker build \
            --file "$dockerfile" \
            --tag "${image_name}:${tag}" \
            $build_args \
            .
        
        if [[ "$PUSH_IMAGES" == "true" ]]; then
            print_info "Pushing ${image_name}:${tag}..."
            docker push "${image_name}:${tag}"
        fi
    fi
    
    print_success "Built ${image_name}:${tag}"
}

# Build images based on type
case "$BUILD_TYPE" in
    "dev")
        build_image "Dockerfile.dev" "$DEV_IMAGE" "$DEV_TAG" ""
        ;;
    "prod")
        build_image "Dockerfile" "$PROD_IMAGE" "$PROD_TAG" ""
        ;;
    "all")
        build_image "Dockerfile.dev" "$DEV_IMAGE" "$DEV_TAG" ""
        build_image "Dockerfile" "$PROD_IMAGE" "$PROD_TAG" ""
        ;;
    *)
        print_error "Invalid build type: $BUILD_TYPE"
        echo "Valid types: dev, prod, all"
        exit 1
        ;;
esac

print_header "Build Summary"
print_success "Build completed successfully!"
print_info "Platforms: $PLATFORMS"
print_info "Images built:"

case "$BUILD_TYPE" in
    "dev")
        echo "  - ${DEV_IMAGE}:${DEV_TAG}"
        ;;
    "prod")
        echo "  - ${PROD_IMAGE}:${PROD_TAG}"
        ;;
    "all")
        echo "  - ${DEV_IMAGE}:${DEV_TAG}"
        echo "  - ${PROD_IMAGE}:${PROD_TAG}"
        ;;
esac

if [[ "$PUSH_IMAGES" == "true" ]]; then
    print_success "Images pushed to registry"
else
    print_info "Images available locally (use --push to push to registry)"
fi

print_header "Usage Examples"
echo "# Run development container:"
echo "docker run --rm -it -v \$(pwd):/workspace ${DEV_IMAGE}:${DEV_TAG}"
echo ""
echo "# Build project in container:"
echo "docker run --rm -v \$(pwd):/workspace ${DEV_IMAGE}:${DEV_TAG} bash -c 'cd /workspace && cmake --preset docker-release && cmake --build --preset docker-release'"
echo ""
echo "# Run production container:"
echo "docker run --rm ${PROD_IMAGE}:${PROD_TAG}"
