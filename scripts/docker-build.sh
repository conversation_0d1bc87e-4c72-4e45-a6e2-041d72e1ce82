#!/bin/bash

# Docker build script for OMOP ETL Pipeline
# This script provides consistent Docker-based builds

set -euo pipefail

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Default values
BUILD_TYPE="release"
CLEAN_BUILD=false
PUSH_IMAGE=false
IMAGE_TAG="latest"
REGISTRY=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Docker build script for OMOP ETL Pipeline

OPTIONS:
    -t, --type TYPE         Build type: release|debug (default: release)
    -c, --clean             Clean build (remove existing containers and images)
    -p, --push              Push image to registry after build
    -r, --registry REGISTRY Registry URL (e.g., docker.io/myorg)
    --tag TAG               Image tag (default: latest)
    -h, --help              Show this help message

EXAMPLES:
    $0                      # Build release version
    $0 -t debug             # Build debug version
    $0 -c -t release        # Clean build of release version
    $0 -p -r myregistry.com # Build and push to registry

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--type)
            BUILD_TYPE="$2"
            shift 2
            ;;
        -c|--clean)
            CLEAN_BUILD=true
            shift
            ;;
        -p|--push)
            PUSH_IMAGE=true
            shift
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        --tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate build type
if [[ "$BUILD_TYPE" != "release" && "$BUILD_TYPE" != "debug" ]]; then
    print_error "Invalid build type: $BUILD_TYPE. Must be 'release' or 'debug'"
    exit 1
fi

# Set image names
if [[ -n "$REGISTRY" ]]; then
    IMAGE_NAME="$REGISTRY/omop-etl"
    DEV_IMAGE_NAME="$REGISTRY/omop-etl-dev"
else
    IMAGE_NAME="omop-etl"
    DEV_IMAGE_NAME="omop-etl-dev"
fi

FULL_IMAGE_NAME="$IMAGE_NAME:$IMAGE_TAG"
FULL_DEV_IMAGE_NAME="$DEV_IMAGE_NAME:$IMAGE_TAG"

print_info "Starting Docker build..."
print_info "Build type: $BUILD_TYPE"
print_info "Image name: $FULL_IMAGE_NAME"
print_info "Clean build: $CLEAN_BUILD"

# Change to project root
cd "$PROJECT_ROOT"

# Clean build if requested
if [[ "$CLEAN_BUILD" == "true" ]]; then
    print_info "Cleaning existing containers and images..."
    
    # Stop and remove containers
    docker-compose -f scripts/docker-compose.yml down --remove-orphans || true
    
    # Remove images
    docker rmi "$FULL_IMAGE_NAME" 2>/dev/null || true
    docker rmi "$FULL_DEV_IMAGE_NAME" 2>/dev/null || true
    
    # Clean build cache
    docker builder prune -f
    
    print_success "Cleanup completed"
fi

# Build the main application image
print_info "Building main application image..."
docker build \
    --tag "$FULL_IMAGE_NAME" \
    --file Dockerfile \
    --target runtime \
    .

if [[ $? -eq 0 ]]; then
    print_success "Main application image built successfully: $FULL_IMAGE_NAME"
else
    print_error "Failed to build main application image"
    exit 1
fi

# Build the development image
print_info "Building development image..."
docker build \
    --tag "$FULL_DEV_IMAGE_NAME" \
    --file Dockerfile.dev \
    .

if [[ $? -eq 0 ]]; then
    print_success "Development image built successfully: $FULL_DEV_IMAGE_NAME"
else
    print_error "Failed to build development image"
    exit 1
fi

# Push images if requested
if [[ "$PUSH_IMAGE" == "true" ]]; then
    if [[ -z "$REGISTRY" ]]; then
        print_error "Registry must be specified when pushing images"
        exit 1
    fi
    
    print_info "Pushing images to registry..."
    
    docker push "$FULL_IMAGE_NAME"
    if [[ $? -eq 0 ]]; then
        print_success "Main image pushed successfully: $FULL_IMAGE_NAME"
    else
        print_error "Failed to push main image"
        exit 1
    fi
    
    docker push "$FULL_DEV_IMAGE_NAME"
    if [[ $? -eq 0 ]]; then
        print_success "Development image pushed successfully: $FULL_DEV_IMAGE_NAME"
    else
        print_error "Failed to push development image"
        exit 1
    fi
fi

print_success "Docker build completed successfully!"
print_info "Images built:"
print_info "  - Main: $FULL_IMAGE_NAME"
print_info "  - Dev:  $FULL_DEV_IMAGE_NAME"

print_info ""
print_info "Next steps:"
print_info "  - Run application: docker-compose -f scripts/docker-compose.yml up"
print_info "  - Development:     docker-compose -f scripts/docker-compose.yml --profile dev up omop-etl-dev"
print_info "  - Build in Docker: docker run --rm -v \$(pwd):/workspace $FULL_DEV_IMAGE_NAME bash -c 'cmake --preset docker-release && cmake --build --preset docker-release'"
