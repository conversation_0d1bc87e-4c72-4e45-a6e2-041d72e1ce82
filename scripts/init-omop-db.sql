-- OMOP CDM Database Initialization Script
-- This script sets up the target OMOP CDM database for the ETL pipeline

-- Create CDM schema
CREATE SCHEMA IF NOT EXISTS cdm;

-- Create vocabulary schema
CREATE SCHEMA IF NOT EXISTS vocab;

-- Create basic OMOP CDM tables (simplified for development)

-- Person table
CREATE TABLE IF NOT EXISTS cdm.person (
    person_id BIGINT PRIMARY KEY,
    gender_concept_id INTEGER,
    year_of_birth INTEGER,
    month_of_birth INTEGER,
    day_of_birth INTEGER,
    birth_datetime TIMESTAMP,
    race_concept_id INTEGER,
    ethnicity_concept_id INTEGER,
    location_id INTEGER,
    provider_id INTEGER,
    care_site_id INTEGER,
    person_source_value VARCHAR(50),
    gender_source_value VARCHAR(50),
    gender_source_concept_id INTEGER,
    race_source_value VARCHAR(50),
    race_source_concept_id INTEGER,
    ethnicity_source_value VARCHAR(50),
    ethnicity_source_concept_id INTEGER
);

-- Observation Period table
CREATE TABLE IF NOT EXISTS cdm.observation_period (
    observation_period_id BIGINT PRIMARY KEY,
    person_id BIGINT,
    observation_period_start_date DATE,
    observation_period_end_date DATE,
    period_type_concept_id INTEGER
);

-- Visit Occurrence table
CREATE TABLE IF NOT EXISTS cdm.visit_occurrence (
    visit_occurrence_id BIGINT PRIMARY KEY,
    person_id BIGINT,
    visit_concept_id INTEGER,
    visit_start_date DATE,
    visit_start_datetime TIMESTAMP,
    visit_end_date DATE,
    visit_end_datetime TIMESTAMP,
    visit_type_concept_id INTEGER,
    provider_id INTEGER,
    care_site_id INTEGER,
    visit_source_value VARCHAR(50),
    visit_source_concept_id INTEGER,
    admitted_from_concept_id INTEGER,
    admitted_from_source_value VARCHAR(50),
    discharged_to_concept_id INTEGER,
    discharged_to_source_value VARCHAR(50),
    preceding_visit_occurrence_id BIGINT
);

-- Condition Occurrence table
CREATE TABLE IF NOT EXISTS cdm.condition_occurrence (
    condition_occurrence_id BIGINT PRIMARY KEY,
    person_id BIGINT,
    condition_concept_id INTEGER,
    condition_start_date DATE,
    condition_start_datetime TIMESTAMP,
    condition_end_date DATE,
    condition_end_datetime TIMESTAMP,
    condition_type_concept_id INTEGER,
    condition_status_concept_id INTEGER,
    stop_reason VARCHAR(20),
    provider_id INTEGER,
    visit_occurrence_id BIGINT,
    visit_detail_id BIGINT,
    condition_source_value VARCHAR(50),
    condition_source_concept_id INTEGER,
    condition_status_source_value VARCHAR(50)
);

-- Drug Exposure table
CREATE TABLE IF NOT EXISTS cdm.drug_exposure (
    drug_exposure_id BIGINT PRIMARY KEY,
    person_id BIGINT,
    drug_concept_id INTEGER,
    drug_exposure_start_date DATE,
    drug_exposure_start_datetime TIMESTAMP,
    drug_exposure_end_date DATE,
    drug_exposure_end_datetime TIMESTAMP,
    verbatim_end_date DATE,
    drug_type_concept_id INTEGER,
    stop_reason VARCHAR(20),
    refills INTEGER,
    quantity NUMERIC,
    days_supply INTEGER,
    sig TEXT,
    route_concept_id INTEGER,
    lot_number VARCHAR(50),
    provider_id INTEGER,
    visit_occurrence_id BIGINT,
    visit_detail_id BIGINT,
    drug_source_value VARCHAR(50),
    drug_source_concept_id INTEGER,
    route_source_value VARCHAR(50),
    dose_unit_source_value VARCHAR(50)
);

-- Procedure Occurrence table
CREATE TABLE IF NOT EXISTS cdm.procedure_occurrence (
    procedure_occurrence_id BIGINT PRIMARY KEY,
    person_id BIGINT,
    procedure_concept_id INTEGER,
    procedure_date DATE,
    procedure_datetime TIMESTAMP,
    procedure_end_date DATE,
    procedure_end_datetime TIMESTAMP,
    procedure_type_concept_id INTEGER,
    modifier_concept_id INTEGER,
    quantity INTEGER,
    provider_id INTEGER,
    visit_occurrence_id BIGINT,
    visit_detail_id BIGINT,
    procedure_source_value VARCHAR(50),
    procedure_source_concept_id INTEGER,
    modifier_source_value VARCHAR(50)
);

-- Measurement table
CREATE TABLE IF NOT EXISTS cdm.measurement (
    measurement_id BIGINT PRIMARY KEY,
    person_id BIGINT,
    measurement_concept_id INTEGER,
    measurement_date DATE,
    measurement_datetime TIMESTAMP,
    measurement_time VARCHAR(10),
    measurement_type_concept_id INTEGER,
    operator_concept_id INTEGER,
    value_as_number NUMERIC,
    value_as_concept_id INTEGER,
    unit_concept_id INTEGER,
    range_low NUMERIC,
    range_high NUMERIC,
    provider_id INTEGER,
    visit_occurrence_id BIGINT,
    visit_detail_id BIGINT,
    measurement_source_value VARCHAR(50),
    measurement_source_concept_id INTEGER,
    unit_source_value VARCHAR(50),
    unit_source_concept_id INTEGER,
    value_source_value VARCHAR(50),
    measurement_event_id BIGINT,
    meas_event_field_concept_id INTEGER
);

-- Observation table
CREATE TABLE IF NOT EXISTS cdm.observation (
    observation_id BIGINT PRIMARY KEY,
    person_id BIGINT,
    observation_concept_id INTEGER,
    observation_date DATE,
    observation_datetime TIMESTAMP,
    observation_type_concept_id INTEGER,
    value_as_number NUMERIC,
    value_as_string VARCHAR(60),
    value_as_concept_id INTEGER,
    qualifier_concept_id INTEGER,
    unit_concept_id INTEGER,
    provider_id INTEGER,
    visit_occurrence_id BIGINT,
    visit_detail_id BIGINT,
    observation_source_value VARCHAR(50),
    observation_source_concept_id INTEGER,
    unit_source_value VARCHAR(50),
    qualifier_source_value VARCHAR(50),
    value_source_value VARCHAR(50),
    observation_event_id BIGINT,
    obs_event_field_concept_id INTEGER
);

-- Death table
CREATE TABLE IF NOT EXISTS cdm.death (
    person_id BIGINT PRIMARY KEY,
    death_date DATE,
    death_datetime TIMESTAMP,
    death_type_concept_id INTEGER,
    cause_concept_id INTEGER,
    cause_source_value VARCHAR(50),
    cause_source_concept_id INTEGER
);

-- Note table
CREATE TABLE IF NOT EXISTS cdm.note (
    note_id BIGINT PRIMARY KEY,
    person_id BIGINT,
    note_date DATE,
    note_datetime TIMESTAMP,
    note_type_concept_id INTEGER,
    note_class_concept_id INTEGER,
    note_title VARCHAR(250),
    note_text TEXT,
    encoding_concept_id INTEGER,
    language_concept_id INTEGER,
    provider_id INTEGER,
    visit_occurrence_id BIGINT,
    visit_detail_id BIGINT,
    note_source_value VARCHAR(50)
);

-- Basic vocabulary tables for testing
CREATE TABLE IF NOT EXISTS vocab.concept (
    concept_id INTEGER PRIMARY KEY,
    concept_name VARCHAR(255),
    domain_id VARCHAR(20),
    vocabulary_id VARCHAR(20),
    concept_class_id VARCHAR(20),
    standard_concept VARCHAR(1),
    concept_code VARCHAR(50),
    valid_start_date DATE,
    valid_end_date DATE,
    invalid_reason VARCHAR(1)
);

-- Insert basic vocabulary concepts for testing
INSERT INTO vocab.concept (concept_id, concept_name, domain_id, vocabulary_id, concept_class_id, standard_concept, concept_code, valid_start_date, valid_end_date) VALUES
(8507, 'Male', 'Gender', 'Gender', 'Gender', 'S', 'M', '1970-01-01', '2099-12-31'),
(8532, 'Female', 'Gender', 'Gender', 'Gender', 'S', 'F', '1970-01-01', '2099-12-31'),
(8527, 'White', 'Race', 'Race', 'Race', 'S', '5', '1970-01-01', '2099-12-31'),
(8516, 'Black or African American', 'Race', 'Race', 'Race', 'S', '3', '1970-01-01', '2099-12-31'),
(8515, 'Asian', 'Race', 'Race', 'Race', 'S', '2', '1970-01-01', '2099-12-31'),
(38003563, 'Not Hispanic or Latino', 'Ethnicity', 'Ethnicity', 'Ethnicity', 'S', '1', '1970-01-01', '2099-12-31'),
(38003564, 'Hispanic or Latino', 'Ethnicity', 'Ethnicity', 'Ethnicity', 'S', '2', '1970-01-01', '2099-12-31')
ON CONFLICT DO NOTHING;

-- Grant permissions to the OMOP user
GRANT ALL PRIVILEGES ON SCHEMA cdm TO omop_user;
GRANT ALL PRIVILEGES ON SCHEMA vocab TO omop_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA cdm TO omop_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA vocab TO omop_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA cdm TO omop_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA vocab TO omop_user;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_person_id ON cdm.person(person_id);
CREATE INDEX IF NOT EXISTS idx_observation_period_person_id ON cdm.observation_period(person_id);
CREATE INDEX IF NOT EXISTS idx_visit_occurrence_person_id ON cdm.visit_occurrence(person_id);
CREATE INDEX IF NOT EXISTS idx_condition_occurrence_person_id ON cdm.condition_occurrence(person_id);
CREATE INDEX IF NOT EXISTS idx_drug_exposure_person_id ON cdm.drug_exposure(person_id);

-- Log completion
SELECT 'OMOP CDM database initialization completed successfully' AS status;
