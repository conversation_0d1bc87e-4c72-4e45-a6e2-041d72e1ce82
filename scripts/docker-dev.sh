#!/bin/bash

# Docker development script for OMOP ETL Pipeline
# This script provides convenient commands for Docker-based development

set -euo pipefail

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Show usage
show_usage() {
    cat << EOF
Usage: $0 COMMAND [OPTIONS]

Docker development script for OMOP ETL Pipeline

COMMANDS:
    build [release|debug]   Build the project in Docker container
    test [release|debug]    Run tests in Docker container
    shell                   Start interactive shell in development container
    clean                   Clean build artifacts
    format                  Format code using clang-format
    lint                    Run static analysis (clang-tidy, cppcheck)
    up                      Start development environment
    down                    Stop development environment
    logs                    Show logs from development container
    exec COMMAND            Execute command in development container

EXAMPLES:
    $0 build                # Build release version
    $0 build debug          # Build debug version
    $0 test                 # Run tests
    $0 shell                # Start interactive shell
    $0 exec "make install"  # Execute custom command

EOF
}

# Change to project root
cd "$PROJECT_ROOT"

# Docker compose command
DOCKER_COMPOSE="docker-compose -f scripts/docker-compose.yml --profile dev"

# Parse command
COMMAND="${1:-}"
if [[ -z "$COMMAND" ]]; then
    print_error "No command specified"
    show_usage
    exit 1
fi

case "$COMMAND" in
    build)
        BUILD_TYPE="${2:-release}"
        if [[ "$BUILD_TYPE" != "release" && "$BUILD_TYPE" != "debug" ]]; then
            print_error "Invalid build type: $BUILD_TYPE. Must be 'release' or 'debug'"
            exit 1
        fi
        
        print_info "Building project ($BUILD_TYPE) in Docker container..."
        
        # Ensure development container is running
        $DOCKER_COMPOSE up -d omop-etl-dev
        
        # Build the project
        $DOCKER_COMPOSE exec omop-etl-dev bash -c "
            set -e
            echo 'Configuring CMake...'
            cmake --preset docker-$BUILD_TYPE
            echo 'Building project...'
            cmake --build --preset docker-$BUILD_TYPE --config $(echo $BUILD_TYPE | sed 's/.*/\u&/')
            echo 'Build completed successfully!'
        "
        
        if [[ $? -eq 0 ]]; then
            print_success "Build completed successfully!"
        else
            print_error "Build failed"
            exit 1
        fi
        ;;
        
    test)
        BUILD_TYPE="${2:-release}"
        if [[ "$BUILD_TYPE" != "release" && "$BUILD_TYPE" != "debug" ]]; then
            print_error "Invalid build type: $BUILD_TYPE. Must be 'release' or 'debug'"
            exit 1
        fi
        
        print_info "Running tests ($BUILD_TYPE) in Docker container..."
        
        # Ensure development container is running
        $DOCKER_COMPOSE up -d omop-etl-dev
        
        # Run tests
        $DOCKER_COMPOSE exec omop-etl-dev bash -c "
            set -e
            if [[ ! -d build/docker-$BUILD_TYPE ]]; then
                echo 'Project not built yet. Building first...'
                cmake --preset docker-$BUILD_TYPE
                cmake --build --preset docker-$BUILD_TYPE --config $(echo $BUILD_TYPE | sed 's/.*/\u&/')
            fi
            echo 'Running tests...'
            cd build/docker-$BUILD_TYPE
            ctest --output-on-failure
        "
        
        if [[ $? -eq 0 ]]; then
            print_success "Tests completed successfully!"
        else
            print_error "Tests failed"
            exit 1
        fi
        ;;
        
    shell)
        print_info "Starting interactive shell in development container..."
        
        # Ensure development container is running
        $DOCKER_COMPOSE up -d omop-etl-dev
        
        # Start interactive shell
        $DOCKER_COMPOSE exec omop-etl-dev bash
        ;;
        
    clean)
        print_info "Cleaning build artifacts..."
        
        # Ensure development container is running
        $DOCKER_COMPOSE up -d omop-etl-dev
        
        # Clean build artifacts
        $DOCKER_COMPOSE exec omop-etl-dev bash -c "
            rm -rf build/docker-*
            echo 'Build artifacts cleaned'
        "
        
        print_success "Clean completed!"
        ;;
        
    format)
        print_info "Formatting code with clang-format..."
        
        # Ensure development container is running
        $DOCKER_COMPOSE up -d omop-etl-dev
        
        # Format code
        $DOCKER_COMPOSE exec omop-etl-dev bash -c "
            find src -name '*.cpp' -o -name '*.h' -o -name '*.hpp' | xargs clang-format -i
            echo 'Code formatting completed'
        "
        
        print_success "Code formatting completed!"
        ;;
        
    lint)
        print_info "Running static analysis..."
        
        # Ensure development container is running
        $DOCKER_COMPOSE up -d omop-etl-dev
        
        # Run static analysis
        $DOCKER_COMPOSE exec omop-etl-dev bash -c "
            echo 'Running cppcheck...'
            cppcheck --enable=all --std=c++20 --suppress=missingIncludeSystem src/ || true
            
            echo 'Running clang-tidy...'
            if [[ -f build/docker-release/compile_commands.json ]]; then
                clang-tidy src/**/*.cpp -p build/docker-release || true
            else
                echo 'No compile_commands.json found. Run build first.'
            fi
        "
        
        print_success "Static analysis completed!"
        ;;
        
    up)
        print_info "Starting development environment..."
        $DOCKER_COMPOSE up -d
        print_success "Development environment started!"
        ;;
        
    down)
        print_info "Stopping development environment..."
        $DOCKER_COMPOSE down
        print_success "Development environment stopped!"
        ;;
        
    logs)
        print_info "Showing logs from development container..."
        $DOCKER_COMPOSE logs -f omop-etl-dev
        ;;
        
    exec)
        if [[ $# -lt 2 ]]; then
            print_error "No command specified for exec"
            exit 1
        fi
        
        EXEC_COMMAND="${2}"
        print_info "Executing command in development container: $EXEC_COMMAND"
        
        # Ensure development container is running
        $DOCKER_COMPOSE up -d omop-etl-dev
        
        # Execute command
        $DOCKER_COMPOSE exec omop-etl-dev bash -c "$EXEC_COMMAND"
        ;;
        
    -h|--help|help)
        show_usage
        exit 0
        ;;
        
    *)
        print_error "Unknown command: $COMMAND"
        show_usage
        exit 1
        ;;
esac
