#!/bin/bash

# Architecture detection script for OMOP ETL Pipeline
# Detects Intel, AMD, and ARM chips and provides build recommendations

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_header() {
    echo -e "\n${BLUE}==== $1 ====${NC}"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_recommendation() {
    echo -e "${GREEN}[RECOMMENDATION]${NC} $1"
}

print_header "Architecture Detection for OMOP ETL Pipeline"

# Detect operating system
OS=$(uname -s)
print_info "Operating System: $OS"

# Detect architecture
ARCH=$(uname -m)
print_info "Architecture: $ARCH"

# Detect processor information
if [[ "$OS" == "Darwin" ]]; then
    # macOS
    if command -v sysctl &> /dev/null; then
        CPU_BRAND=$(sysctl -n machdep.cpu.brand_string 2>/dev/null || echo "Unknown")
        print_info "CPU: $CPU_BRAND"
        
        # Check for Apple Silicon
        if sysctl -n machdep.cpu.brand_string 2>/dev/null | grep -q "Apple"; then
            print_info "Apple Silicon detected"
            CHIP_TYPE="Apple Silicon (ARM64)"
        else
            print_info "Intel Mac detected"
            CHIP_TYPE="Intel x86_64"
        fi
    fi
elif [[ "$OS" == "Linux" ]]; then
    # Linux
    if [[ -f /proc/cpuinfo ]]; then
        CPU_MODEL=$(grep "model name" /proc/cpuinfo | head -1 | cut -d: -f2 | sed 's/^ *//')
        if [[ -n "$CPU_MODEL" ]]; then
            print_info "CPU: $CPU_MODEL"
        fi
        
        # Detect vendor
        if grep -q "GenuineIntel" /proc/cpuinfo; then
            VENDOR="Intel"
        elif grep -q "AuthenticAMD" /proc/cpuinfo; then
            VENDOR="AMD"
        elif grep -q "ARM" /proc/cpuinfo || [[ "$ARCH" == "aarch64" ]] || [[ "$ARCH" == "arm64" ]]; then
            VENDOR="ARM"
        else
            VENDOR="Unknown"
        fi
        print_info "Vendor: $VENDOR"
    fi
fi

# Map architecture to Docker platform
case "$ARCH" in
    "x86_64")
        DOCKER_PLATFORM="linux/amd64"
        if [[ "${VENDOR:-}" == "Intel" ]]; then
            CHIP_TYPE="Intel x86_64"
        elif [[ "${VENDOR:-}" == "AMD" ]]; then
            CHIP_TYPE="AMD x86_64"
        else
            CHIP_TYPE="x86_64 (Intel/AMD compatible)"
        fi
        ;;
    "aarch64"|"arm64")
        DOCKER_PLATFORM="linux/arm64"
        CHIP_TYPE="${CHIP_TYPE:-ARM64}"
        ;;
    "armv7l")
        DOCKER_PLATFORM="linux/arm/v7"
        CHIP_TYPE="ARM 32-bit"
        ;;
    *)
        DOCKER_PLATFORM="linux/amd64"
        CHIP_TYPE="Unknown (defaulting to x86_64)"
        print_warning "Unknown architecture: $ARCH"
        ;;
esac

print_info "Chip Type: $CHIP_TYPE"
print_info "Docker Platform: $DOCKER_PLATFORM"

# Check Docker availability
print_header "Docker Environment Check"

if command -v docker &> /dev/null; then
    print_success "Docker is installed"
    
    if docker info &> /dev/null; then
        print_success "Docker daemon is running"
        
        # Check Docker version
        DOCKER_VERSION=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
        print_info "Docker version: $DOCKER_VERSION"
        
        # Check buildx availability
        if docker buildx version &> /dev/null; then
            print_success "Docker buildx is available (multi-architecture support)"
            BUILDX_VERSION=$(docker buildx version | head -1 | cut -d' ' -f2)
            print_info "Buildx version: $BUILDX_VERSION"
            
            # List available builders
            print_info "Available builders:"
            docker buildx ls | while read line; do
                echo "    $line"
            done
        else
            print_warning "Docker buildx not available (limited to single architecture)"
        fi
    else
        print_warning "Docker daemon is not running"
    fi
else
    print_warning "Docker is not installed"
fi

print_header "Build Recommendations"

# Provide architecture-specific recommendations
case "$ARCH" in
    "x86_64")
        print_recommendation "Your system supports standard Docker builds"
        echo "  • Use: ./scripts/docker-build.sh for single-architecture builds"
        echo "  • Use: ./scripts/docker-build-multiarch.sh --native-only for current platform"
        echo "  • Use: ./scripts/docker-build-multiarch.sh --multi-arch for cross-platform builds"
        ;;
    "aarch64"|"arm64")
        print_recommendation "Your ARM64 system is fully supported"
        echo "  • Use: ./scripts/docker-build.sh for single-architecture builds"
        echo "  • Use: ./scripts/docker-build-multiarch.sh --native-only for ARM64 only"
        echo "  • Use: ./scripts/docker-build-multiarch.sh --multi-arch for cross-platform builds"
        if [[ "$OS" == "Darwin" ]]; then
            echo "  • Apple Silicon Macs can build for both ARM64 and x86_64"
        fi
        ;;
    "armv7l")
        print_recommendation "Your ARM 32-bit system is supported"
        echo "  • Use: ./scripts/docker-build-multiarch.sh --platforms linux/arm/v7"
        print_warning "ARM 32-bit builds may be slower and have limited package availability"
        ;;
    *)
        print_warning "Unknown architecture - using x86_64 defaults"
        echo "  • Try: ./scripts/docker-build.sh"
        echo "  • If issues occur, please report your architecture: $ARCH"
        ;;
esac

print_header "Quick Start Commands"

echo "# Build for your current architecture:"
echo "./scripts/docker-build.sh"
echo ""
echo "# Build multi-architecture images (Intel, AMD, ARM):"
echo "./scripts/docker-build-multiarch.sh --multi-arch"
echo ""
echo "# Build only for your platform:"
echo "./scripts/docker-build-multiarch.sh --native-only"
echo ""
echo "# Test your Docker setup:"
echo "./scripts/test-docker-build.sh"
echo ""

print_header "Platform-Specific Examples"

echo "# Intel/AMD systems:"
echo "./scripts/docker-build-multiarch.sh --platforms linux/amd64"
echo ""
echo "# ARM64 systems (Apple Silicon, ARM servers):"
echo "./scripts/docker-build-multiarch.sh --platforms linux/arm64"
echo ""
echo "# ARM 32-bit systems (Raspberry Pi):"
echo "./scripts/docker-build-multiarch.sh --platforms linux/arm/v7"
echo ""
echo "# Build for all supported platforms:"
echo "./scripts/docker-build-multiarch.sh --platforms linux/amd64,linux/arm64,linux/arm/v7"
echo ""

print_header "CMake Commands"

echo "Your Docker CMake commands will work on any architecture:"
echo ""
echo "# Configure for Docker"
echo "cmake --preset docker-release"
echo "cmake --preset docker-debug"
echo ""
echo "# Build"
echo "cmake --build --preset docker-release"
echo ""

print_success "Architecture detection complete!"
print_info "Your system is ready for Docker-based OMOP ETL Pipeline development"
