-- Clinical Database Initialization Script
-- This script sets up the source clinical database for the OMOP ETL pipeline

-- Create schema for clinical data
CREATE SCHEMA IF NOT EXISTS clinical;

-- Create sample patients table
CREATE TABLE IF NOT EXISTS clinical.patients (
    patient_id SERIAL PRIMARY KEY,
    first_name VARCHAR(100),
    last_name VA<PERSON>HA<PERSON>(100),
    birth_date DATE,
    gender CHAR(1),
    race VARCHAR(50),
    ethnicity VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create sample visits table
CREATE TABLE IF NOT EXISTS clinical.visits (
    visit_id SERIAL PRIMARY KEY,
    patient_id INTEGER REFERENCES clinical.patients(patient_id),
    visit_date DATE,
    visit_type VARCHAR(50),
    discharge_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create sample medications table
CREATE TABLE IF NOT EXISTS clinical.medications (
    medication_id SERIAL PRIMARY KEY,
    patient_id INTEGER REFERENCES clinical.patients(patient_id),
    drug_name VARCHA<PERSON>(200),
    drug_strength VARCHAR(50),
    start_date DATE,
    end_date DATE,
    duration_days INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create sample conditions table
CREATE TABLE IF NOT EXISTS clinical.conditions (
    condition_id SERIAL PRIMARY KEY,
    patient_id INTEGER REFERENCES clinical.patients(patient_id),
    condition_name VARCHAR(200),
    diagnosis_date DATE,
    condition_status VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample data for testing
INSERT INTO clinical.patients (first_name, last_name, birth_date, gender, race, ethnicity) VALUES
('John', 'Doe', '1980-01-15', 'M', 'White', 'Not Hispanic'),
('Jane', 'Smith', '1975-03-22', 'F', 'Black', 'Not Hispanic'),
('Bob', 'Johnson', '1990-07-08', 'M', 'Asian', 'Not Hispanic'),
('Alice', 'Williams', '1985-11-30', 'F', 'White', 'Hispanic')
ON CONFLICT DO NOTHING;

INSERT INTO clinical.visits (patient_id, visit_date, visit_type, discharge_date) VALUES
(1, '2023-01-10', 'Outpatient', '2023-01-10'),
(1, '2023-06-15', 'Inpatient', '2023-06-18'),
(2, '2023-02-20', 'Emergency', '2023-02-20'),
(3, '2023-03-05', 'Outpatient', '2023-03-05'),
(4, '2023-04-12', 'Outpatient', '2023-04-12')
ON CONFLICT DO NOTHING;

INSERT INTO clinical.medications (patient_id, drug_name, drug_strength, start_date, end_date, duration_days) VALUES
(1, 'Metformin', '500mg', '2023-01-10', '2023-07-10', 180),
(1, 'Lisinopril', '10mg', '2023-01-10', NULL, NULL),
(2, 'Aspirin', '81mg', '2023-02-20', NULL, NULL),
(3, 'Ibuprofen', '200mg', '2023-03-05', '2023-03-12', 7),
(4, 'Amoxicillin', '500mg', '2023-04-12', '2023-04-22', 10)
ON CONFLICT DO NOTHING;

INSERT INTO clinical.conditions (patient_id, condition_name, diagnosis_date, condition_status) VALUES
(1, 'Type 2 Diabetes', '2023-01-10', 'Active'),
(1, 'Hypertension', '2023-01-10', 'Active'),
(2, 'Chest Pain', '2023-02-20', 'Resolved'),
(3, 'Headache', '2023-03-05', 'Resolved'),
(4, 'Upper Respiratory Infection', '2023-04-12', 'Resolved')
ON CONFLICT DO NOTHING;

-- Grant permissions to the clinical user
GRANT ALL PRIVILEGES ON SCHEMA clinical TO clinical_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA clinical TO clinical_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA clinical TO clinical_user;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_patients_birth_date ON clinical.patients(birth_date);
CREATE INDEX IF NOT EXISTS idx_visits_patient_id ON clinical.visits(patient_id);
CREATE INDEX IF NOT EXISTS idx_visits_visit_date ON clinical.visits(visit_date);
CREATE INDEX IF NOT EXISTS idx_medications_patient_id ON clinical.medications(patient_id);
CREATE INDEX IF NOT EXISTS idx_conditions_patient_id ON clinical.conditions(patient_id);

-- Log completion
SELECT 'Clinical database initialization completed successfully' AS status;
