#!/bin/bash

# Demo script showing the exact Docker CMake commands
# This demonstrates the consistent Docker-based build process

set -euo pipefail

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_header() {
    echo -e "\n${BLUE}==== $1 ====${NC}"
}

print_command() {
    echo -e "${YELLOW}$ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_header "Docker CMake Commands Demo - OMOP ETL Pipeline"

echo "This demo shows the exact Docker CMake commands for consistent builds:"
echo ""
echo "# Configure for Docker"
echo "cmake --preset docker-release"
echo "cmake --preset docker-debug"
echo ""
echo "# Build"
echo "cmake --build --preset docker-release"
echo ""

print_header "Step 1: Build Docker Development Image"

print_command "docker build -f Dockerfile.dev -t omop-etl-dev:demo ."
if docker build -f Dockerfile.dev -t omop-etl-dev:demo . > /tmp/docker-build.log 2>&1; then
    print_success "Docker image built successfully"
else
    echo "❌ Docker build failed. Check /tmp/docker-build.log"
    exit 1
fi

print_header "Step 2: List Available CMake Presets"

print_command "docker run --rm -v \$(pwd):/workspace omop-etl-dev:demo bash -c 'cd /workspace && cmake --list-presets=configure'"
echo ""
docker run --rm -v $(pwd):/workspace omop-etl-dev:demo bash -c "
    cd /workspace
    echo 'Available configure presets:'
    cmake --list-presets=configure 2>/dev/null || echo 'CMake preset listing not available'
    echo ''
    echo 'Available build presets:'
    cmake --list-presets=build 2>/dev/null || echo 'CMake preset listing not available'
" || echo "Note: CMake preset listing may not work due to architecture mismatch"

print_header "Step 3: Configure for Docker (Release)"

print_command "cmake --preset docker-release"
echo ""
echo "Running in Docker container:"
docker run --rm -v $(pwd):/workspace omop-etl-dev:demo bash -c "
    cd /workspace
    echo 'Configuring with docker-release preset...'
    cmake --preset docker-release
    echo 'Configuration completed!'
    echo ''
    echo 'Build directory created:'
    ls -la build/docker-release/ 2>/dev/null || echo 'Build directory not visible'
" || echo "Note: Configuration may fail due to architecture mismatch on Apple Silicon"

print_header "Step 4: Configure for Docker (Debug)"

print_command "cmake --preset docker-debug"
echo ""
echo "Running in Docker container:"
docker run --rm -v $(pwd):/workspace omop-etl-dev:demo bash -c "
    cd /workspace
    echo 'Configuring with docker-debug preset...'
    cmake --preset docker-debug
    echo 'Debug configuration completed!'
" || echo "Note: Configuration may fail due to architecture mismatch on Apple Silicon"

print_header "Step 5: Build with Docker Preset"

print_command "cmake --build --preset docker-release"
echo ""
echo "Running in Docker container:"
docker run --rm -v $(pwd):/workspace omop-etl-dev:demo bash -c "
    cd /workspace
    echo 'Building with docker-release preset...'
    cmake --build --preset docker-release --parallel 2
    echo 'Build completed!'
    echo ''
    echo 'Built artifacts:'
    find build/docker-release -name '*.a' -o -name 'omop-etl*' 2>/dev/null | head -10 || echo 'No artifacts found'
" || echo "Note: Build may fail due to architecture mismatch on Apple Silicon"

print_header "Step 6: Alternative - Using Development Scripts"

echo "For easier usage, you can also use the development scripts:"
echo ""
print_command "./scripts/docker-dev.sh build release"
echo "This internally uses: cmake --preset docker-release && cmake --build --preset docker-release"
echo ""
print_command "./scripts/docker-dev.sh build debug"
echo "This internally uses: cmake --preset docker-debug && cmake --build --preset docker-debug"

print_header "Step 7: Docker Preset Configuration Summary"

echo ""
echo "Docker CMake Presets Configuration:"
echo ""
echo "📋 Configure Presets:"
echo "   • docker-release  - Release build with GCC, Linux paths"
echo "   • docker-debug    - Debug build with GCC, Linux paths, coverage enabled"
echo ""
echo "🔨 Build Presets:"
echo "   • docker-release  - Build release configuration"
echo "   • docker-debug    - Build debug configuration"
echo ""
echo "🧪 Test Presets:"
echo "   • docker-release  - Run tests with release build"
echo "   • docker-debug    - Run tests with debug build"
echo ""

print_header "Key Benefits"

echo ""
echo "✅ Consistent Environment: Same build across Linux, macOS, Windows"
echo "✅ No Host Dependencies: All tools contained in Docker"
echo "✅ No macOS-specific paths: Uses Linux standard paths (/usr/bin/gcc)"
echo "✅ Reproducible Builds: Exact same versions every time"
echo "✅ CI/CD Ready: Easy integration with build pipelines"
echo ""

print_header "Usage Summary"

echo ""
echo "🐳 Your exact Docker CMake commands:"
echo ""
echo "# Configure for Docker"
echo "cmake --preset docker-release"
echo "cmake --preset docker-debug"
echo ""
echo "# Build"
echo "cmake --build --preset docker-release"
echo ""
echo "🚀 Or use the convenient scripts:"
echo ""
echo "./scripts/docker-dev.sh build release    # Uses docker-release preset"
echo "./scripts/docker-dev.sh build debug      # Uses docker-debug preset"
echo "./scripts/docker-dev.sh test             # Run tests"
echo "./scripts/docker-dev.sh shell            # Interactive development"
echo ""

print_header "Cleanup"

print_command "docker rmi omop-etl-dev:demo"
docker rmi omop-etl-dev:demo > /dev/null 2>&1 || true

print_success "Demo completed! Docker environment is ready for consistent builds."

echo ""
echo "📚 For more information:"
echo "   • See DOCKER.md for comprehensive documentation"
echo "   • See DOCKER-USAGE.md for quick reference"
echo "   • Run ./scripts/test-docker-build.sh for full testing"
