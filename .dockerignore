# Build directories
build/
install/
.cmake/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# Git
.git/
.gitignore

# Documentation
docs/build/
# Allow README.md and LICENSE for CPack
!README.md
!LICENSE
# Exclude other markdown files
docs/*.md
DOCKER*.md

# Test files
tests/data/
*.test

# Logs
*.log
logs/

# Temporary files
tmp/
temp/
*.tmp

# macOS specific
.DS_Store
.AppleDouble
.LSOverride

# Windows specific
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux specific
*~

# Package managers
node_modules/
.npm/
.yarn/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt

# Conan
.conan/
conandata.yml
conanfile.py

# Coverage reports
coverage/
*.gcov
*.gcda
*.gcno

# Profiling
*.prof
gmon.out

# Backup files
*.bak
*.backup
*.old

# Archives
*.tar
*.tar.gz
*.zip
*.rar

# Database files
*.db
*.sqlite
*.sqlite3

# Configuration files with secrets
.env
.env.local
.env.production
config/secrets/

# Docker files (avoid recursive copying)
Dockerfile*
docker-compose*.yml
.dockerignore

# Scripts (except essential ones)
scripts/
!scripts/docker-build.sh
!scripts/docker-dev.sh
