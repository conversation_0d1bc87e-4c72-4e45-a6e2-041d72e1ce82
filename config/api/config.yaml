# OMOP ETL API Service Configuration

server:
  host: ${API_HOST:-0.0.0.0}
  port: ${API_PORT:-8080}
  workers: ${API_WORKERS:-4}
  request_timeout: 300
  max_request_size: 104857600  # 100MB
  cors:
    enabled: true
    allowed_origins:
      - "*"
    allowed_methods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
    allowed_headers:
      - "*"
    expose_headers:
      - X-Total-Count
      - X-Page-Count

security:
  enabled: ${ENABLE_AUTH:-true}
  jwt:
    secret: ${JWT_SECRET}
    algorithm: HS256
    expiration: 3600  # 1 hour
    refresh_expiration: 604800  # 7 days
  api_keys:
    enabled: true
    header_name: X-API-Key
  rate_limiting:
    enabled: true
    requests_per_minute: 60
    requests_per_hour: 1000

database:
  type: postgresql
  connection:
    host: ${DB_HOST:-localhost}
    port: ${DB_PORT:-5432}
    database: ${DB_NAME:-omop_etl_meta}
    username: ${DB_USER}
    password: ${DB_PASSWORD}
    pool_size: 20
    max_overflow: 10
    pool_timeout: 30
    pool_recycle: 3600

etl:
  # ETL job management settings
  job_queue:
    max_concurrent_jobs: ${MAX_CONCURRENT_JOBS:-5}
    job_timeout: 7200  # 2 hours
    cleanup_interval: 3600  # 1 hour
    retention_days: 30

  # Default ETL settings
  defaults:
    batch_size: 10000
    parallel_threads: 4
    error_tolerance: 0.01
    checkpoint_interval: 50000

  # Available data sources
  data_sources:
    - name: production
      type: postgresql
      description: "Production clinical database"
      config_path: "config/etl/postgres_mappings.yaml"

    - name: csv_import
      type: csv
      description: "CSV file import"
      config_path: "config/etl/csv_mappings.yaml"

    - name: staging
      type: mysql
      description: "Staging MySQL database"
      config_path: "config/etl/mysql_mappings.yaml"

logging:
  level: ${LOG_LEVEL:-INFO}
  format: json
  file:
    enabled: true
    path: ${LOG_PATH:-/var/log/omop-etl}
    filename: api.log
    max_size: 104857600  # 100MB
    max_backups: 10
    compress: true
  console:
    enabled: true
    colorize: true

monitoring:
  metrics:
    enabled: true
    endpoint: /metrics
    include_process_metrics: true
    include_database_metrics: true

  health_check:
    enabled: true
    endpoint: /health
    checks:
      - database
      - disk_space
      - memory

storage:
  # Temporary file storage for uploads
  temp_path: ${TEMP_PATH:-/tmp/omop-etl}
  max_upload_size: **********  # 1GB
  allowed_extensions:
    - .csv
    - .json
    - .yaml
    - .yml

  # Configuration storage
  config_path: ${CONFIG_PATH:-/etc/omop-etl/configs}

  # Job output storage
  output_path: ${OUTPUT_PATH:-/var/lib/omop-etl/output}

cache:
  enabled: true
  type: memory  # or redis
  ttl: 3600
  max_size: 1000

  # Redis configuration (if type is redis)
  redis:
    host: ${REDIS_HOST:-localhost}
    port: ${REDIS_PORT:-6379}
    password: ${REDIS_PASSWORD}
    db: 0

notifications:
  enabled: ${ENABLE_NOTIFICATIONS:-false}

  email:
    enabled: false
    smtp:
      host: ${SMTP_HOST}
      port: ${SMTP_PORT:-587}
      username: ${SMTP_USER}
      password: ${SMTP_PASSWORD}
      use_tls: true
    from_address: <EMAIL>

  webhook:
    enabled: false
    endpoints:
      - url: ${WEBHOOK_URL}
        events:
          - job_started
          - job_completed
          - job_failed
        headers:
          Authorization: "Bearer ${WEBHOOK_TOKEN}"

# API Documentation
documentation:
  enabled: true
  title: "OMOP ETL Pipeline API"
  description: "RESTful API for managing OMOP CDM ETL jobs"
  version: "1.0.0"
  openapi_path: /openapi.json
  swagger_ui_path: /docs
  redoc_path: /redoc

# Feature flags
features:
  enable_async_jobs: true
  enable_job_scheduling: false
  enable_data_preview: true
  enable_vocabulary_management: false
  enable_concept_search: true

# Default user for initial setup
default_admin:
  username: admin
  email: <EMAIL>
  # Password will be generated on first run if not set
  password: ${ADMIN_PASSWORD}