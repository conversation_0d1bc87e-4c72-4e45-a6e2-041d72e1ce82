# CSV to OMOP CDM Mapping Configuration
# This file defines the mapping rules for converting CSV data to OMOP CDM format

source:
  type: csv
  encoding: UTF-8
  delimiter: ","
  quote_char: '"'
  escape_char: "\\"
  skip_header: true
  null_values: ["NULL", "NA", "N/A", ""]

  # Define CSV file locations
  files:
    patients:
      path: "${CSV_PATH}/patients.csv"
      columns: [patient_id, birth_date, gender, race, ethnicity, location_id]
    encounters:
      path: "${CSV_PATH}/encounters.csv"
      columns: [encounter_id, patient_id, encounter_type, admission_date, discharge_date, provider_id, care_site_id]
    diagnoses:
      path: "${CSV_PATH}/diagnoses.csv"
      columns: [diagnosis_id, patient_id, icd10_code, diagnosis_date, encounter_id, diagnosis_type]
    medications:
      path: "${CSV_PATH}/medications.csv"
      columns: [medication_id, patient_id, drug_name, drug_strength, drug_form, start_date, duration_days, quantity, days_supply, refills]
    lab_results:
      path: "${CSV_PATH}/lab_results.csv"
      columns: [lab_result_id, patient_id, loinc_code, result_date, result_value, result_unit, normal_range_low, normal_range_high]

target:
  type: postgresql
  connection:
    host: ${OMOP_HOST:-localhost}
    port: ${OMOP_PORT:-5432}
    database: ${OMOP_DB:-omop_cdm}
    username: ${OMOP_USER}
    password: ${OMOP_PASSWORD}
    pool_size: 10
    connection_timeout: 30

# ETL Job Configuration
job_config:
  batch_size: 5000         # Smaller batch size for CSV processing
  parallel_threads: 2      # Less parallelism for file-based processing
  error_tolerance: 0.01
  checkpoint_interval: 25000
  enable_validation: true
  temp_directory: "${TEMP_DIR:-/tmp/omop-etl}"

mappings:
  # Person Table Mapping
  person:
    source_file: patients
    target_table: person
    primary_key: person_id
    transformations:
      - source_column: patient_id
        target_column: person_id
        type: direct
        data_type: integer

      - source_column: birth_date
        target_column: birth_datetime
        type: date_transform
        format: "%m/%d/%Y"        # Common CSV date format
        output_format: "%Y-%m-%d %H:%M:%S"

      - source_column: gender
        target_column: gender_concept_id
        type: vocabulary_mapping
        vocabulary: Gender
        case_sensitive: false
        mapping:
          "M": 8507
          "MALE": 8507
          "F": 8532
          "FEMALE": 8532
          "U": 0
          "UNKNOWN": 0
          "OTHER": 0

      - source_column: race
        target_column: race_concept_id
        type: vocabulary_mapping
        vocabulary: Race
        default: 0
        case_sensitive: false

      - source_column: ethnicity
        target_column: ethnicity_concept_id
        type: vocabulary_mapping
        vocabulary: Ethnicity
        default: 0
        case_sensitive: false

      - source_column: location_id
        target_column: location_id
        type: direct
        data_type: integer
        nullable: true

    validation_rules:
      - field: person_id
        type: not_null
      - field: person_id
        type: unique
      - field: gender_concept_id
        type: in_list
        values: [8507, 8532, 0]

  # Visit Occurrence Mapping
  visit_occurrence:
    source_file: encounters
    target_table: visit_occurrence
    primary_key: visit_occurrence_id
    transformations:
      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct
        data_type: integer

      - source_column: patient_id
        target_column: person_id
        type: direct
        data_type: integer

      - source_column: encounter_type
        target_column: visit_concept_id
        type: vocabulary_mapping
        vocabulary: Visit
        case_sensitive: false
        mapping:
          "INPATIENT": 9201
          "IP": 9201
          "OUTPATIENT": 9202
          "OP": 9202
          "EMERGENCY": 9203
          "ER": 9203
          "ED": 9203

      - source_column: admission_date
        target_column: visit_start_datetime
        type: date_transform
        format: "%m/%d/%Y %H:%M"
        output_format: "%Y-%m-%d %H:%M:%S"
        default_time: "00:00:00"

      - source_column: discharge_date
        target_column: visit_end_datetime
        type: date_transform
        format: "%m/%d/%Y %H:%M"
        output_format: "%Y-%m-%d %H:%M:%S"
        default_time: "23:59:59"

      - source_column: provider_id
        target_column: provider_id
        type: direct
        data_type: integer
        nullable: true

      - source_column: care_site_id
        target_column: care_site_id
        type: direct
        data_type: integer
        nullable: true

    validation_rules:
      - field: visit_occurrence_id
        type: not_null
      - field: visit_occurrence_id
        type: unique
      - field: person_id
        type: not_null

  # Condition Occurrence Mapping
  condition_occurrence:
    source_file: diagnoses
    target_table: condition_occurrence
    primary_key: condition_occurrence_id
    transformations:
      - source_column: diagnosis_id
        target_column: condition_occurrence_id
        type: direct
        data_type: integer

      - source_column: patient_id
        target_column: person_id
        type: direct
        data_type: integer

      - source_column: icd10_code
        target_column: condition_concept_id
        type: concept_lookup
        vocabulary: ICD10CM
        source_vocabulary_id: ICD10CM
        strip_dots: true        # Remove dots from ICD10 codes

      - source_column: diagnosis_date
        target_column: condition_start_datetime
        type: date_transform
        format: "%m/%d/%Y"
        output_format: "%Y-%m-%d %H:%M:%S"

      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct
        data_type: integer
        nullable: true

      - source_column: diagnosis_type
        target_column: condition_type_concept_id
        type: vocabulary_mapping
        case_sensitive: false
        mapping:
          "PRIMARY": 44786627
          "PRINCIPAL": 44786627
          "1": 44786627
          "SECONDARY": 44786629
          "2": 44786629
          "ADMITTING": 44786630

    validation_rules:
      - field: condition_occurrence_id
        type: not_null
      - field: condition_occurrence_id
        type: unique

  # Drug Exposure Mapping
  drug_exposure:
    source_file: medications
    target_table: drug_exposure
    primary_key: drug_exposure_id
    transformations:
      - source_column: medication_id
        target_column: drug_exposure_id
        type: direct
        data_type: integer

      - source_column: patient_id
        target_column: person_id
        type: direct
        data_type: integer

      - source_columns: [drug_name, drug_strength, drug_form]
        target_column: drug_concept_id
        type: custom_transform
        function: lookup_drug_concept_csv

      - source_column: start_date
        target_column: drug_exposure_start_datetime
        type: date_transform
        format: "%m/%d/%Y"
        output_format: "%Y-%m-%d %H:%M:%S"

      - source_columns: [start_date, duration_days]
        target_column: drug_exposure_end_datetime
        type: date_calculation
        calculation: "start_date + duration_days"
        date_format: "%m/%d/%Y"

      - source_column: quantity
        target_column: quantity
        type: numeric_transform
        data_type: float
        default: 0

      - source_column: days_supply
        target_column: days_supply
        type: numeric_transform
        data_type: integer
        default: 0

      - source_column: refills
        target_column: refills
        type: numeric_transform
        data_type: integer
        default: 0

    validation_rules:
      - field: drug_exposure_id
        type: not_null
      - field: drug_exposure_id
        type: unique
      - field: quantity
        type: greater_than
        value: 0

  # Measurement Mapping
  measurement:
    source_file: lab_results
    target_table: measurement
    primary_key: measurement_id
    transformations:
      - source_column: lab_result_id
        target_column: measurement_id
        type: direct
        data_type: integer

      - source_column: patient_id
        target_column: person_id
        type: direct
        data_type: integer

      - source_column: loinc_code
        target_column: measurement_concept_id
        type: concept_lookup
        vocabulary: LOINC
        source_vocabulary_id: LOINC

      - source_column: result_date
        target_column: measurement_datetime
        type: date_transform
        format: "%m/%d/%Y %H:%M:%S"
        output_format: "%Y-%m-%d %H:%M:%S"

      - source_column: result_value
        target_column: value_as_number
        type: numeric_transform
        data_type: float
        nullable: true

      - source_column: result_unit
        target_column: unit_concept_id
        type: unit_mapping
        vocabulary: UCUM

      - source_column: normal_range_low
        target_column: range_low
        type: numeric_transform
        data_type: float
        nullable: true

      - source_column: normal_range_high
        target_column: range_high
        type: numeric_transform
        data_type: float
        nullable: true

    validation_rules:
      - field: measurement_id
        type: not_null
      - field: measurement_id
        type: unique
      - field: value_as_number
        type: between
        min: range_low
        max: range_high
        when_present: true

# Custom transformation functions for CSV
custom_transforms:
  lookup_drug_concept_csv:
    type: concatenate_lookup
    separator: " "
    trim: true
    lookup_table: drug_concept_mapping
    default: 0