# JSON to OMOP CDM Mapping Configuration
# This file defines the mapping rules for converting JSON data to OMOP CDM format

source:
  type: json
  encoding: UTF-8

  # Define JSON file locations or API endpoints
  files:
    patients:
      path: "${JSON_PATH}/patients.json"
      array_path: "$.patients[*]"  # JSONPath to array of patient objects

    encounters:
      path: "${JSON_PATH}/encounters.json"
      array_path: "$.encounters[*]"

    diagnoses:
      path: "${JSON_PATH}/diagnoses.json"
      array_path: "$.diagnoses[*]"

    medications:
      path: "${JSON_PATH}/medications.json"
      array_path: "$.medications[*]"

    lab_results:
      path: "${JSON_PATH}/lab_results.json"
      array_path: "$.lab_results[*]"

  # API endpoint configuration (alternative to files)
  api:
    base_url: "${API_BASE_URL}"
    auth:
      type: bearer
      token: "${API_TOKEN}"
    headers:
      Accept: "application/json"
    pagination:
      type: offset
      page_size: 1000

target:
  type: postgresql
  connection:
    host: ${OMOP_HOST:-localhost}
    port: ${OMOP_PORT:-5432}
    database: ${OMOP_DB:-omop_cdm}
    username: ${OMOP_USER}
    password: ${OMOP_PASSWORD}
    pool_size: 10
    connection_timeout: 30

# ETL Job Configuration
job_config:
  batch_size: 5000
  parallel_threads: 2
  error_tolerance: 0.01
  checkpoint_interval: 25000
  enable_validation: true

mappings:
  # Person Table Mapping
  person:
    source_file: patients
    target_table: person
    primary_key: person_id
    transformations:
      - source_path: "$.id"
        target_column: person_id
        type: direct

      - source_path: "$.demographics.birthDate"
        target_column: birth_datetime
        type: date_transform
        format: "ISO8601"  # 2023-01-15T00:00:00Z

      - source_path: "$.demographics.gender"
        target_column: gender_concept_id
        type: vocabulary_mapping
        vocabulary: Gender
        case_sensitive: false
        mapping:
          "male": 8507
          "m": 8507
          "female": 8532
          "f": 8532
          "unknown": 0
          "other": 0

      - source_path: "$.demographics.race"
        target_column: race_concept_id
        type: vocabulary_mapping
        vocabulary: Race
        default: 0

      - source_path: "$.demographics.ethnicity"
        target_column: ethnicity_concept_id
        type: vocabulary_mapping
        vocabulary: Ethnicity
        default: 0

      - source_path: "$.address.city"
        target_column: location_id
        type: location_lookup
        nullable: true

    validation_rules:
      - field: person_id
        type: not_null
      - field: person_id
        type: unique

  # Visit Occurrence Mapping
  visit_occurrence:
    source_file: encounters
    target_table: visit_occurrence
    primary_key: visit_occurrence_id
    transformations:
      - source_path: "$.encounterId"
        target_column: visit_occurrence_id
        type: direct

      - source_path: "$.patientId"
        target_column: person_id
        type: direct

      - source_path: "$.type"
        target_column: visit_concept_id
        type: vocabulary_mapping
        vocabulary: Visit
        mapping:
          "inpatient": 9201
          "outpatient": 9202
          "emergency": 9203

      - source_path: "$.period.start"
        target_column: visit_start_datetime
        type: date_transform
        format: "ISO8601"

      - source_path: "$.period.end"
        target_column: visit_end_datetime
        type: date_transform
        format: "ISO8601"
        nullable: true

      - source_paths: ["$.provider.id", "$.provider.reference"]
        target_column: provider_id
        type: reference_extraction
        pattern: "Practitioner/([0-9]+)"
        nullable: true

    validation_rules:
      - field: visit_occurrence_id
        type: not_null
      - field: person_id
        type: not_null

  # Condition Occurrence Mapping (FHIR-like structure)
  condition_occurrence:
    source_file: diagnoses
    target_table: condition_occurrence
    primary_key: condition_occurrence_id
    transformations:
      - source_path: "$.id"
        target_column: condition_occurrence_id
        type: direct

      - source_path: "$.subject.reference"
        target_column: person_id
        type: reference_extraction
        pattern: "Patient/([0-9]+)"

      - source_path: "$.code.coding[0].code"
        target_column: condition_concept_id
        type: concept_lookup
        vocabulary: "${source_path:$.code.coding[0].system}"

      - source_path: "$.onsetDateTime"
        target_column: condition_start_datetime
        type: date_transform
        format: "ISO8601"

      - source_path: "$.encounter.reference"
        target_column: visit_occurrence_id
        type: reference_extraction
        pattern: "Encounter/([0-9]+)"
        nullable: true

      - source_path: "$.category[0].coding[0].code"
        target_column: condition_type_concept_id
        type: vocabulary_mapping
        mapping:
          "encounter-diagnosis": 44786627
          "problem-list-item": 44786629

  # Drug Exposure Mapping
  drug_exposure:
    source_file: medications
    target_table: drug_exposure
    primary_key: drug_exposure_id
    transformations:
      - source_path: "$.id"
        target_column: drug_exposure_id
        type: direct

      - source_path: "$.subject.reference"
        target_column: person_id
        type: reference_extraction
        pattern: "Patient/([0-9]+)"

      - source_path: "$.medicationCodeableConcept.coding[0]"
        target_column: drug_concept_id
        type: complex_concept_lookup
        system_path: "$.system"
        code_path: "$.code"
        display_path: "$.display"

      - source_path: "$.effectivePeriod.start"
        target_column: drug_exposure_start_datetime
        type: date_transform
        format: "ISO8601"

      - source_path: "$.effectivePeriod.end"
        target_column: drug_exposure_end_datetime
        type: date_transform
        format: "ISO8601"
        nullable: true

      - source_path: "$.dosageInstruction[0].doseAndRate[0].doseQuantity.value"
        target_column: quantity
        type: numeric_transform

      - source_path: "$.dispenseRequest.expectedSupplyDuration.value"
        target_column: days_supply
        type: numeric_transform

      - source_path: "$.dispenseRequest.numberOfRepeatsAllowed"
        target_column: refills
        type: numeric_transform
        default: 0

  # Measurement Mapping (Observation resource)
  measurement:
    source_file: lab_results
    target_table: measurement
    primary_key: measurement_id
    transformations:
      - source_path: "$.id"
        target_column: measurement_id
        type: direct

      - source_path: "$.subject.reference"
        target_column: person_id
        type: reference_extraction
        pattern: "Patient/([0-9]+)"

      - source_path: "$.code.coding[?(@.system=='http://loinc.org')].code"
        target_column: measurement_concept_id
        type: concept_lookup
        vocabulary: LOINC

      - source_path: "$.effectiveDateTime"
        target_column: measurement_datetime
        type: date_transform
        format: "ISO8601"

      - source_paths: ["$.valueQuantity.value", "$.valueInteger", "$.valueDecimal"]
        target_column: value_as_number
        type: numeric_transform
        first_non_null: true

      - source_path: "$.valueQuantity.unit"
        target_column: unit_concept_id
        type: unit_mapping
        vocabulary: UCUM

      - source_path: "$.referenceRange[0].low.value"
        target_column: range_low
        type: numeric_transform
        nullable: true

      - source_path: "$.referenceRange[0].high.value"
        target_column: range_high
        type: numeric_transform
        nullable: true

# Custom transformation functions for JSON
custom_transforms:
  reference_extraction:
    type: regex_extract
    error_handling: default_null

  complex_concept_lookup:
    type: multi_field_lookup
    cache: true

  location_lookup:
    type: location_geocode
    cache: true
    create_if_missing: true