# PostgreSQL to OMOP CDM Mapping Configuration
# This file defines the mapping rules for converting PostgreSQL data to OMOP CDM format

source:
  type: postgresql
  connection:
    host: ${POSTGRES_HOST:-localhost}
    port: ${POSTGRES_PORT:-5432}
    database: ${POSTGRES_DB:-clinical_db}
    username: ${POSTGRES_USER}
    password: ${POSTGRES_PASSWORD}
    pool_size: 10
    connection_timeout: 30

target:
  type: postgresql
  connection:
    host: ${OMOP_HOST:-localhost}
    port: ${OMOP_PORT:-5432}
    database: ${OMOP_DB:-omop_cdm}
    username: ${OMOP_USER}
    password: ${OMOP_PASSWORD}
    pool_size: 10
    connection_timeout: 30

# ETL Job Configuration
job_config:
  batch_size: 10000
  parallel_threads: 4
  error_tolerance: 0.01  # 1% error tolerance
  checkpoint_interval: 50000
  enable_validation: true

mappings:
  # Person Table Mapping
  person:
    source_table: patients
    target_table: person
    primary_key: person_id
    transformations:
      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: birth_date
        target_column: birth_datetime
        type: date_transform
        format: "%Y-%m-%d"

      - source_column: gender
        target_column: gender_concept_id
        type: vocabulary_mapping
        vocabulary: Gender
        mapping:
          "M": 8507     # Male
          "F": 8532     # Female
          "U": 0        # Unknown

      - source_column: race
        target_column: race_concept_id
        type: vocabulary_mapping
        vocabulary: Race
        default: 0

      - source_column: ethnicity
        target_column: ethnicity_concept_id
        type: vocabulary_mapping
        vocabulary: Ethnicity
        default: 0

      - source_column: location_id
        target_column: location_id
        type: direct
        nullable: true

    validation_rules:
      - field: person_id
        type: not_null
      - field: gender_concept_id
        type: in_list
        values: [8507, 8532, 0]
      - field: birth_datetime
        type: date_range
        min: "1900-01-01"
        max: "current_date"

  # Visit Occurrence Mapping
  visit_occurrence:
    source_table: encounters
    target_table: visit_occurrence
    primary_key: visit_occurrence_id
    transformations:
      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct

      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: encounter_type
        target_column: visit_concept_id
        type: vocabulary_mapping
        vocabulary: Visit
        mapping:
          "IP": 9201    # Inpatient Visit
          "OP": 9202    # Outpatient Visit
          "ER": 9203    # Emergency Room Visit

      - source_column: admission_date
        target_column: visit_start_datetime
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: discharge_date
        target_column: visit_end_datetime
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: provider_id
        target_column: provider_id
        type: direct
        nullable: true

      - source_column: care_site_id
        target_column: care_site_id
        type: direct
        nullable: true

    validation_rules:
      - field: visit_occurrence_id
        type: not_null
      - field: person_id
        type: not_null
      - field: visit_start_datetime
        type: before
        compare_to: visit_end_datetime

  # Condition Occurrence Mapping
  condition_occurrence:
    source_table: diagnoses
    target_table: condition_occurrence
    primary_key: condition_occurrence_id
    transformations:
      - source_column: diagnosis_id
        target_column: condition_occurrence_id
        type: direct

      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: icd10_code
        target_column: condition_concept_id
        type: concept_lookup
        vocabulary: ICD10CM
        source_vocabulary_id: ICD10CM

      - source_column: diagnosis_date
        target_column: condition_start_datetime
        type: date_transform
        format: "%Y-%m-%d"

      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct
        nullable: true

      - source_column: diagnosis_type
        target_column: condition_type_concept_id
        type: vocabulary_mapping
        mapping:
          "primary": 44786627      # Primary condition
          "secondary": 44786629    # Secondary condition

    validation_rules:
      - field: condition_occurrence_id
        type: not_null
      - field: person_id
        type: not_null
      - field: condition_concept_id
        type: not_null

  # Drug Exposure Mapping
  drug_exposure:
    source_table: medications
    target_table: drug_exposure
    primary_key: drug_exposure_id
    transformations:
      - source_column: medication_id
        target_column: drug_exposure_id
        type: direct

      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_columns: [drug_name, drug_strength, drug_form]
        target_column: drug_concept_id
        type: custom_transform
        function: lookup_drug_concept

      - source_column: start_date
        target_column: drug_exposure_start_datetime
        type: date_transform
        format: "%Y-%m-%d"

      - source_columns: [start_date, duration_days]
        target_column: drug_exposure_end_datetime
        type: date_calculation
        calculation: "start_date + duration_days"

      - source_column: quantity
        target_column: quantity
        type: numeric_transform

      - source_column: days_supply
        target_column: days_supply
        type: numeric_transform

      - source_column: refills
        target_column: refills
        type: numeric_transform
        default: 0

    validation_rules:
      - field: drug_exposure_id
        type: not_null
      - field: person_id
        type: not_null
      - field: drug_exposure_start_datetime
        type: before
        compare_to: drug_exposure_end_datetime

  # Measurement Mapping
  measurement:
    source_table: lab_results
    target_table: measurement
    primary_key: measurement_id
    transformations:
      - source_column: lab_result_id
        target_column: measurement_id
        type: direct

      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: loinc_code
        target_column: measurement_concept_id
        type: concept_lookup
        vocabulary: LOINC
        source_vocabulary_id: LOINC

      - source_column: result_date
        target_column: measurement_datetime
        type: date_transform
        format: "%Y-%m-%d %H:%M:%S"

      - source_column: result_value
        target_column: value_as_number
        type: numeric_transform
        nullable: true

      - source_column: result_unit
        target_column: unit_concept_id
        type: unit_mapping
        vocabulary: UCUM

      - source_column: normal_range_low
        target_column: range_low
        type: numeric_transform
        nullable: true

      - source_column: normal_range_high
        target_column: range_high
        type: numeric_transform
        nullable: true

    validation_rules:
      - field: measurement_id
        type: not_null
      - field: person_id
        type: not_null
      - field: measurement_concept_id
        type: not_null

# Custom transformation functions
custom_transforms:
  lookup_drug_concept:
    type: sql_lookup
    query: |
      SELECT concept_id
      FROM concept
      WHERE vocabulary_id = 'RxNorm'
        AND concept_name = :drug_name
        AND domain_id = 'Drug'
        AND invalid_reason IS NULL
    cache: true
    cache_ttl: 3600