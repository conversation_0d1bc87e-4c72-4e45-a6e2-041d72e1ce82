# MySQL to OMOP CDM Mapping Configuration
# This file defines the mapping rules for converting MySQL data to OMOP CDM format

source:
  type: mysql
  connection:
    host: ${MYSQL_HOST:-localhost}
    port: ${MYSQL_PORT:-3306}
    database: ${MYSQL_DB:-clinical_db}
    username: ${MYSQL_USER}
    password: ${MYSQL_PASSWORD}
    charset: utf8mb4
    pool_size: 10
    connection_timeout: 30
    # MySQL specific options
    options:
      connect_timeout: 10
      init_command: "SET SESSION sql_mode='TRADITIONAL'"
      use_unicode: true

target:
  type: postgresql
  connection:
    host: ${OMOP_HOST:-localhost}
    port: ${OMOP_PORT:-5432}
    database: ${OMOP_DB:-omop_cdm}
    username: ${OMOP_USER}
    password: ${OMOP_PASSWORD}
    pool_size: 10
    connection_timeout: 30

# ETL Job Configuration
job_config:
  batch_size: 10000
  parallel_threads: 4
  error_tolerance: 0.01
  checkpoint_interval: 50000
  enable_validation: true
  mysql_fetch_size: 5000  # MySQL-specific batch fetch size

mappings:
  # Person Table Mapping
  person:
    source_query: |
      SELECT
        p.patient_id,
        p.birth_date,
        p.gender,
        p.race_code,
        p.ethnicity_code,
        l.location_id,
        p.created_at,
        p.updated_at
      FROM patients p
      LEFT JOIN patient_locations pl ON p.patient_id = pl.patient_id
      LEFT JOIN locations l ON pl.zip_code = l.zip_code
      WHERE p.deleted_at IS NULL
    target_table: person
    primary_key: person_id
    transformations:
      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: birth_date
        target_column: birth_datetime
        type: date_transform
        format: "mysql_datetime"  # Handles MySQL datetime format

      - source_column: gender
        target_column: gender_concept_id
        type: vocabulary_mapping
        vocabulary: Gender
        mapping:
          "M": 8507
          "F": 8532
          "U": 0
          "NULL": 0

      - source_column: race_code
        target_column: race_concept_id
        type: vocabulary_mapping
        vocabulary: Race
        default: 0
        mapping:
          "1": 8527    # White
          "2": 8516    # Black or African American
          "3": 8515    # Asian
          "4": 8657    # American Indian or Alaska Native
          "5": 8557    # Native Hawaiian or Other Pacific Islander

      - source_column: ethnicity_code
        target_column: ethnicity_concept_id
        type: vocabulary_mapping
        vocabulary: Ethnicity
        default: 0
        mapping:
          "H": 38003563   # Hispanic or Latino
          "N": 38003564   # Not Hispanic or Latino

      - source_column: location_id
        target_column: location_id
        type: direct
        nullable: true

    validation_rules:
      - field: person_id
        type: not_null
      - field: birth_datetime
        type: not_future_date

  # Visit Occurrence Mapping
  visit_occurrence:
    source_query: |
      SELECT
        e.encounter_id,
        e.patient_id,
        e.encounter_type_code,
        e.admission_datetime,
        e.discharge_datetime,
        e.attending_provider_id,
        e.facility_id,
        e.visit_source_value,
        e.discharge_disposition_code
      FROM encounters e
      WHERE e.status != 'cancelled'
        AND e.deleted_at IS NULL
    target_table: visit_occurrence
    primary_key: visit_occurrence_id
    transformations:
      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct

      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: encounter_type_code
        target_column: visit_concept_id
        type: vocabulary_mapping
        vocabulary: Visit
        mapping:
          "I": 9201     # Inpatient
          "O": 9202     # Outpatient
          "E": 9203     # Emergency
          "L": 581476   # Long Term Care

      - source_column: admission_datetime
        target_column: visit_start_datetime
        type: date_transform
        format: "mysql_datetime"

      - source_column: discharge_datetime
        target_column: visit_end_datetime
        type: date_transform
        format: "mysql_datetime"
        nullable: true

      - source_column: attending_provider_id
        target_column: provider_id
        type: direct
        nullable: true

      - source_column: facility_id
        target_column: care_site_id
        type: direct
        nullable: true

      - source_column: visit_source_value
        target_column: visit_source_value
        type: direct
        max_length: 50

    validation_rules:
      - field: visit_occurrence_id
        type: not_null
      - field: visit_start_datetime
        type: before
        compare_to: visit_end_datetime

  # Condition Occurrence Mapping
  condition_occurrence:
    source_query: |
      SELECT
        d.diagnosis_id,
        d.patient_id,
        d.icd_code,
        d.icd_version,
        d.diagnosis_datetime,
        d.encounter_id,
        d.diagnosis_priority,
        d.diagnosis_source,
        d.provider_id
      FROM diagnoses d
      WHERE d.deleted_at IS NULL
    target_table: condition_occurrence
    primary_key: condition_occurrence_id
    transformations:
      - source_column: diagnosis_id
        target_column: condition_occurrence_id
        type: direct

      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_columns: [icd_code, icd_version]
        target_column: condition_concept_id
        type: icd_concept_lookup
        version_column: icd_version

      - source_column: diagnosis_datetime
        target_column: condition_start_datetime
        type: date_transform
        format: "mysql_datetime"

      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct
        nullable: true

      - source_column: diagnosis_priority
        target_column: condition_type_concept_id
        type: vocabulary_mapping
        mapping:
          "1": 44786627      # Primary condition
          "2": 44786629      # Secondary condition
          "A": 44786630      # Admitting diagnosis

      - source_column: provider_id
        target_column: provider_id
        type: direct
        nullable: true

    validation_rules:
      - field: condition_occurrence_id
        type: not_null
      - field: condition_concept_id
        type: not_zero

  # Drug Exposure Mapping
  drug_exposure:
    source_query: |
      SELECT
        m.medication_admin_id,
        m.patient_id,
        m.drug_code,
        m.drug_code_system,
        m.drug_name,
        m.start_datetime,
        m.end_datetime,
        m.quantity,
        m.quantity_unit,
        m.days_supply,
        m.refills,
        m.route_code,
        m.dose_value,
        m.dose_unit,
        m.prescriber_id,
        m.encounter_id
      FROM medication_administrations m
      WHERE m.status = 'completed'
        AND m.deleted_at IS NULL
    target_table: drug_exposure
    primary_key: drug_exposure_id
    transformations:
      - source_column: medication_admin_id
        target_column: drug_exposure_id
        type: direct

      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_columns: [drug_code, drug_code_system, drug_name]
        target_column: drug_concept_id
        type: drug_concept_lookup
        code_system_mapping:
          "NDC": "NDC"
          "RXNORM": "RxNorm"

      - source_column: start_datetime
        target_column: drug_exposure_start_datetime
        type: date_transform
        format: "mysql_datetime"

      - source_column: end_datetime
        target_column: drug_exposure_end_datetime
        type: date_transform
        format: "mysql_datetime"
        nullable: true

      - source_column: quantity
        target_column: quantity
        type: numeric_transform

      - source_column: days_supply
        target_column: days_supply
        type: numeric_transform

      - source_column: refills
        target_column: refills
        type: numeric_transform
        default: 0

      - source_column: route_code
        target_column: route_concept_id
        type: route_mapping
        vocabulary: Route

      - source_column: prescriber_id
        target_column: provider_id
        type: direct
        nullable: true

      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct
        nullable: true

  # Measurement Mapping
  measurement:
    source_query: |
      SELECT
        lr.lab_result_id,
        lr.patient_id,
        lr.test_code,
        lr.test_code_system,
        lr.result_datetime,
        lr.value_numeric,
        lr.value_text,
        lr.unit_code,
        lr.normal_low,
        lr.normal_high,
        lr.abnormal_flag,
        lr.provider_id,
        lr.encounter_id,
        lr.specimen_source
      FROM lab_results lr
      WHERE lr.result_status = 'final'
        AND lr.deleted_at IS NULL
    target_table: measurement
    primary_key: measurement_id
    transformations:
      - source_column: lab_result_id
        target_column: measurement_id
        type: direct

      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_columns: [test_code, test_code_system]
        target_column: measurement_concept_id
        type: measurement_concept_lookup
        code_system_mapping:
          "LOINC": "LOINC"
          "LOCAL": "Local"

      - source_column: result_datetime
        target_column: measurement_datetime
        type: date_transform
        format: "mysql_datetime"

      - source_column: value_numeric
        target_column: value_as_number
        type: numeric_transform
        nullable: true

      - source_column: value_text
        target_column: value_as_concept_id
        type: value_concept_mapping
        nullable: true

      - source_column: unit_code
        target_column: unit_concept_id
        type: unit_mapping
        vocabulary: UCUM

      - source_column: normal_low
        target_column: range_low
        type: numeric_transform
        nullable: true

      - source_column: normal_high
        target_column: range_high
        type: numeric_transform
        nullable: true

      - source_column: provider_id
        target_column: provider_id
        type: direct
        nullable: true

      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct
        nullable: true

# MySQL-specific optimizations
optimization:
  use_indexes: true
  parallel_extraction: true
  cursor_size: 10000
  read_timeout: 300

# Custom transformation functions for MySQL
custom_transforms:
  icd_concept_lookup:
    type: versioned_concept_lookup
    version_mapping:
      "9": "ICD9CM"
      "10": "ICD10CM"

  drug_concept_lookup:
    type: multi_vocabulary_lookup
    priority: ["RxNorm", "NDC"]

  measurement_concept_lookup:
    type: fallback_lookup
    primary: "LOINC"
    fallback: "local_mapping_table"