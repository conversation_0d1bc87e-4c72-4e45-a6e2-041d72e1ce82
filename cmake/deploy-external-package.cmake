# cmake/deploy-external-package.cmake

# Usage:
# include(${CMAKE_SOURCE_DIR}/cmake/deploy-external-package.cmake)
# deploy_external_package(NAME <target_name> SCRIPT <script_path> [ARGS <arg1> <arg2> ...])

function(deploy_external_package)
    set(options)
    set(oneValueArgs NAME SCRIPT)
    set(multiValueArgs ARGS)
    cmake_parse_arguments(DEPLOY "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})

    if(NOT DEPLOY_NAME)
        message(FATAL_ERROR "deploy_external_package: NAME argument is required.")
    endif()
    if(NOT DEPLOY_SCRIPT)
        message(FATAL_ERROR "deploy_external_package: SCRIPT argument is required.")
    endif()

    add_custom_target(${DEPLOY_NAME}
        COMMAND ${CMAKE_COMMAND} -E echo "Running deployment script: ${DEPLOY_SCRIPT} ${DEPLOY_ARGS}"
        COMMAND ${DEPLOY_SCRIPT} ${DEPLOY_ARGS}
        WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
        COMMENT "Deploying external package using ${DEPLOY_SCRIPT}"
        VERBATIM
    )
endfunction()
