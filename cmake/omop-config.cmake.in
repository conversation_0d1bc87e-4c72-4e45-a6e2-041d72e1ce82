@PACKAGE_INIT@

include(CMakeFindDependencyMacro)

# Find required dependencies
find_dependency(PostgreSQL)
find_dependency(yaml-cpp)
find_dependency(n<PERSON><PERSON>_json)
find_dependency(spdlog)

# Optional dependencies
find_dependency(ODBC QUIET)

# Include targets file
include("${CMAKE_CURRENT_LIST_DIR}/omop-targets.cmake")

# Set schema names
set(CDM_SCHEMA "@CDM_SCHEMA@" CACHE STRING "Name of the CDM schema")
set(VOCAB_SCHEMA "@VOCAB_SCHEMA@" CACHE STRING "Name of the vocabulary schema")

# Set SQL file paths
set(OMOP_SQL_DIR "@PACKAGE_CMAKE_INSTALL_DATADIR@/omop-etl/sql")
set(OMOP_CONFIG_DIR "@PACKAGE_CMAKE_INSTALL_DATADIR@/omop-etl/config")

# Check if all required files exist
if(NOT EXISTS "${OMOP_SQL_DIR}")
    message(FATAL_ERROR "OMOP SQL directory not found: ${OMOP_SQL_DIR}")
endif()

if(NOT EXISTS "${OMOP_CONFIG_DIR}")
    message(FATAL_ERROR "OMOP config directory not found: ${OMOP_CONFIG_DIR}")
endif()

# Export variables
set(OMOP_FOUND TRUE)
set(OMOP_VERSION @PACKAGE_VERSION@)
set(OMOP_INCLUDE_DIRS "@PACKAGE_CMAKE_INSTALL_INCLUDEDIR@")
set(OMOP_LIBRARY_DIRS "@PACKAGE_CMAKE_INSTALL_LIBDIR@")
set(OMOP_BINARY_DIRS "@PACKAGE_CMAKE_INSTALL_BINDIR@")